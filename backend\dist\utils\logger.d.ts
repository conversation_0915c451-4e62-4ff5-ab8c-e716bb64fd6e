export declare function log(message: string, ...args: any[]): void;
export declare function error(message: string, ...args: any[]): void;
export declare function warn(message: string, ...args: any[]): void;
export declare function info(message: string, ...args: any[]): void;
export declare function debug(message: string, ...args: any[]): void;
export declare function trace(message: string, ...args: any[]): void;
export declare function table(data: any, columns?: string[]): void;
export declare function group(label: string): void;
export declare function groupCollapsed(label: string): void;
export declare function groupEnd(): void;
export declare function time(label: string): void;
export declare function timeEnd(label: string): void;
export declare function timeLog(label: string, ...args: any[]): void;
export declare function count(label: string): void;
export declare function countReset(label: string): void;
export declare function assert(condition: any, message: string, ...args: any[]): void;
export declare function clear(): void;
export declare function dir(obj: any, options?: any): void;
export declare function dirxml(node: any): void;
export declare function profile(label: string): void;
export declare function profileEnd(label: string): void;
export declare function timeStamp(label: string): void;
declare const logger: {
    log: typeof log;
    error: typeof error;
    warn: typeof warn;
    info: typeof info;
    debug: typeof debug;
    trace: typeof trace;
    table: typeof table;
    group: typeof group;
    groupCollapsed: typeof groupCollapsed;
    groupEnd: typeof groupEnd;
    time: typeof time;
    timeEnd: typeof timeEnd;
    timeLog: typeof timeLog;
    count: typeof count;
    countReset: typeof countReset;
    assert: typeof assert;
    clear: typeof clear;
    dir: typeof dir;
    dirxml: typeof dirxml;
    profile: typeof profile;
    profileEnd: typeof profileEnd;
    timeStamp: typeof timeStamp;
};
export default logger;
