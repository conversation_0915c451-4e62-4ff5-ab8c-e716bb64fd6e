"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./src/components/TopTaskBar.tsx":
/*!***************************************!*\
  !*** ./src/components/TopTaskBar.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TopTaskBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../context/PreferencesContext */ \"./src/context/PreferencesContext.tsx\");\n/* harmony import */ var _utils_preferences__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/preferences */ \"./src/utils/preferences.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Currency names mapping for display labels\nconst CURRENCY_NAMES = {\n    \"USD\": \"US Dollar\",\n    \"EUR\": \"Euro\",\n    \"GBP\": \"British Pound\",\n    \"CAD\": \"Canadian Dollar\",\n    \"AUD\": \"Australian Dollar\",\n    \"NZD\": \"New Zealand Dollar\",\n    \"JPY\": \"Japanese Yen\",\n    \"CNY\": \"Chinese Yuan\",\n    \"HKD\": \"Hong Kong Dollar\",\n    \"SGD\": \"Singapore Dollar\",\n    \"INR\": \"Indian Rupee\",\n    \"BRL\": \"Brazilian Real\",\n    \"MXN\": \"Mexican Peso\",\n    \"ZAR\": \"South African Rand\",\n    \"SEK\": \"Swedish Krona\",\n    \"NOK\": \"Norwegian Krone\",\n    \"DKK\": \"Danish Krone\",\n    \"CHF\": \"Swiss Franc\",\n    \"PLN\": \"Polish Zloty\",\n    \"CZK\": \"Czech Koruna\",\n    \"HUF\": \"Hungarian Forint\",\n    \"ILS\": \"Israeli Shekel\",\n    \"TRY\": \"Turkish Lira\",\n    \"AED\": \"UAE Dirham\",\n    \"SAR\": \"Saudi Riyal\",\n    \"DZD\": \"Algerian Dinar\",\n    \"QAR\": \"Qatari Riyal\",\n    \"KWD\": \"Kuwaiti Dinar\",\n    \"BHD\": \"Bahraini Dinar\",\n    \"OMR\": \"Omani Rial\",\n    \"EGP\": \"Egyptian Pound\",\n    \"NGN\": \"Nigerian Naira\",\n    \"KES\": \"Kenyan Shilling\",\n    \"ARS\": \"Argentine Peso\",\n    \"CLP\": \"Chilean Peso\",\n    \"COP\": \"Colombian Peso\",\n    \"PEN\": \"Peruvian Sol\",\n    \"UYU\": \"Uruguayan Peso\",\n    \"KRW\": \"South Korean Won\",\n    \"THB\": \"Thai Baht\",\n    \"MYR\": \"Malaysian Ringgit\",\n    \"PHP\": \"Philippine Peso\",\n    \"IDR\": \"Indonesian Rupiah\"\n};\n// Generate currency options from centralized symbols\nconst currencyOptions = _utils_preferences__WEBPACK_IMPORTED_MODULE_7__.CURRENCY_OPTIONS.map((currency)=>({\n        value: currency,\n        label: \"\".concat(CURRENCY_NAMES[currency], \" (\").concat(currency, \")\"),\n        symbol: _utils_preferences__WEBPACK_IMPORTED_MODULE_7__.CURRENCY_SYMBOLS[currency]\n    }));\n// Language names mapping for display labels\nconst LANGUAGE_NAMES = {\n    \"en\": {\n        label: \"English\",\n        nativeLabel: \"English\"\n    },\n    \"es\": {\n        label: \"Spanish\",\n        nativeLabel: \"Espa\\xf1ol\"\n    },\n    \"fr\": {\n        label: \"French\",\n        nativeLabel: \"Fran\\xe7ais\"\n    },\n    \"de\": {\n        label: \"German\",\n        nativeLabel: \"Deutsch\"\n    },\n    \"it\": {\n        label: \"Italian\",\n        nativeLabel: \"Italiano\"\n    },\n    \"pt\": {\n        label: \"Portuguese\",\n        nativeLabel: \"Portugu\\xeas\"\n    },\n    \"ru\": {\n        label: \"Russian\",\n        nativeLabel: \"Русский\"\n    },\n    \"zh\": {\n        label: \"Chinese\",\n        nativeLabel: \"中文\"\n    },\n    \"ja\": {\n        label: \"Japanese\",\n        nativeLabel: \"日本語\"\n    },\n    \"ko\": {\n        label: \"Korean\",\n        nativeLabel: \"한국어\"\n    },\n    \"ar\": {\n        label: \"Arabic\",\n        nativeLabel: \"العربية\"\n    },\n    \"hi\": {\n        label: \"Hindi\",\n        nativeLabel: \"हिन्दी\"\n    },\n    \"bn\": {\n        label: \"Bengali\",\n        nativeLabel: \"বাংলা\"\n    },\n    \"pa\": {\n        label: \"Punjabi\",\n        nativeLabel: \"ਪੰਜਾਬੀ\"\n    },\n    \"ur\": {\n        label: \"Urdu\",\n        nativeLabel: \"اردو\"\n    },\n    \"fa\": {\n        label: \"Persian\",\n        nativeLabel: \"فارسی\"\n    },\n    \"tr\": {\n        label: \"Turkish\",\n        nativeLabel: \"T\\xfcrk\\xe7e\"\n    },\n    \"nl\": {\n        label: \"Dutch\",\n        nativeLabel: \"Nederlands\"\n    },\n    \"sv\": {\n        label: \"Swedish\",\n        nativeLabel: \"Svenska\"\n    },\n    \"no\": {\n        label: \"Norwegian\",\n        nativeLabel: \"Norsk\"\n    },\n    \"da\": {\n        label: \"Danish\",\n        nativeLabel: \"Dansk\"\n    },\n    \"fi\": {\n        label: \"Finnish\",\n        nativeLabel: \"Suomi\"\n    },\n    \"pl\": {\n        label: \"Polish\",\n        nativeLabel: \"Polski\"\n    },\n    \"cs\": {\n        label: \"Czech\",\n        nativeLabel: \"Čeština\"\n    },\n    \"sk\": {\n        label: \"Slovak\",\n        nativeLabel: \"Slovenčina\"\n    },\n    \"hu\": {\n        label: \"Hungarian\",\n        nativeLabel: \"Magyar\"\n    },\n    \"ro\": {\n        label: \"Romanian\",\n        nativeLabel: \"Rom\\xe2nă\"\n    },\n    \"bg\": {\n        label: \"Bulgarian\",\n        nativeLabel: \"Български\"\n    },\n    \"hr\": {\n        label: \"Croatian\",\n        nativeLabel: \"Hrvatski\"\n    },\n    \"sl\": {\n        label: \"Slovenian\",\n        nativeLabel: \"Slovenščina\"\n    }\n};\n// Generate language options from centralized options\nconst languageOptions = _utils_preferences__WEBPACK_IMPORTED_MODULE_7__.LANGUAGE_OPTIONS.map((lang)=>{\n    var _LANGUAGE_NAMES_lang, _LANGUAGE_NAMES_lang1;\n    return {\n        value: lang,\n        label: ((_LANGUAGE_NAMES_lang = LANGUAGE_NAMES[lang]) === null || _LANGUAGE_NAMES_lang === void 0 ? void 0 : _LANGUAGE_NAMES_lang.label) || lang,\n        nativeLabel: ((_LANGUAGE_NAMES_lang1 = LANGUAGE_NAMES[lang]) === null || _LANGUAGE_NAMES_lang1 === void 0 ? void 0 : _LANGUAGE_NAMES_lang1.nativeLabel) || lang\n    };\n});\nfunction TopTaskBar() {\n    var _stores_find;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { currency: preferredCurrency, language: preferredLanguage, setCurrency, setLanguage } = (0,_context_PreferencesContext__WEBPACK_IMPORTED_MODULE_6__.usePreferences)();\n    const { currentStoreId, setCurrentStoreId, autoSelectFirstStore } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_5__.useStore)();\n    // State for stores\n    const [stores, setStores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [storesLoading, setStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for modals and menu\n    const [isStoreModalOpen, setIsStoreModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalMode, setModalMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"create\");\n    const [isDeleteOpen, setIsDeleteOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStoreMenuOpen, setIsStoreMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for forms\n    const [storeForm, setStoreForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        currency: preferredCurrency,\n        preferredLanguage: preferredLanguage,\n        userId: (user === null || user === void 0 ? void 0 : user.id) || \"\"\n    });\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStoreId, setEditingStoreId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteStoreId, setDeleteStoreId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Refs for focus management\n    const storeNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const storeMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fetch stores when user is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            fetchStores();\n        }\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Focus management for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isStoreModalOpen && storeNameInputRef.current) {\n            storeNameInputRef.current.focus();\n        }\n    }, [\n        isStoreModalOpen\n    ]);\n    // Click outside to close store menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (storeMenuRef.current && !storeMenuRef.current.contains(event.target)) {\n                setIsStoreMenuOpen(false);\n            }\n        };\n        if (isStoreMenuOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n        }\n    }, [\n        isStoreMenuOpen\n    ]);\n    // Escape key to close menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEscape = (event)=>{\n            if (event.key === \"Escape\" && isStoreMenuOpen) {\n                setIsStoreMenuOpen(false);\n            }\n        };\n        if (isStoreMenuOpen) {\n            document.addEventListener(\"keydown\", handleEscape);\n            return ()=>document.removeEventListener(\"keydown\", handleEscape);\n        }\n    }, [\n        isStoreMenuOpen\n    ]);\n    // Close menu after actions\n    const handleCreateModalOpen = ()=>{\n        openCreateModal();\n        setIsStoreMenuOpen(false);\n    };\n    const handleEditModalOpen = (store)=>{\n        openEditModal(store);\n        setIsStoreMenuOpen(false);\n    };\n    const handleDeleteModalOpen = (store)=>{\n        openDeleteModal(store);\n        setIsStoreMenuOpen(false);\n    };\n    const fetchStores = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id)) return;\n        setStoresLoading(true);\n        try {\n            const data = await _utils_api__WEBPACK_IMPORTED_MODULE_3__.storeApi.getByUserId(user.id, {\n                page: 1,\n                limit: 100\n            });\n            const storesData = (data === null || data === void 0 ? void 0 : data.data) || [];\n            setStores(storesData);\n            // Auto-select the first store if none is currently selected\n            autoSelectFirstStore(storesData);\n        } catch (error) {\n            console.error(\"Failed to fetch stores:\", error);\n        } finally{\n            setStoresLoading(false);\n        }\n    };\n    const validateForm = ()=>{\n        const errors = {};\n        if (!storeForm.name.trim()) {\n            errors.name = \"Store name is required\";\n        } else if (storeForm.name.trim().length < 2) {\n            errors.name = \"Store name must be at least 2 characters\";\n        } else if (storeForm.name.trim().length > 50) {\n            errors.name = \"Store name must be less than 50 characters\";\n        }\n        if (storeForm.description.trim().length > 200) {\n            errors.description = \"Description must be less than 200 characters\";\n        }\n        if (!storeForm.currency) {\n            errors.currency = \"Currency is required\";\n        }\n        if (!storeForm.preferredLanguage) {\n            errors.preferredLanguage = \"Language is required\";\n        }\n        return errors;\n    };\n    const resetForm = ()=>{\n        setStoreForm({\n            name: \"\",\n            description: \"\",\n            currency: preferredCurrency,\n            preferredLanguage: preferredLanguage,\n            userId: (user === null || user === void 0 ? void 0 : user.id) || \"\"\n        });\n        setFormErrors({});\n        setEditingStoreId(null);\n    };\n    const openCreateModal = ()=>{\n        resetForm();\n        setModalMode(\"create\");\n        setIsStoreModalOpen(true);\n    };\n    const openEditModal = (store)=>{\n        setStoreForm({\n            name: store.name,\n            description: store.description || \"\",\n            currency: store.currency,\n            preferredLanguage: store.preferredLanguage,\n            userId: (user === null || user === void 0 ? void 0 : user.id) || \"\"\n        });\n        setEditingStoreId(store.id);\n        setModalMode(\"edit\");\n        setFormErrors({});\n        setIsStoreModalOpen(true);\n    };\n    const handleSubmitStore = async ()=>{\n        const errors = validateForm();\n        setFormErrors(errors);\n        if (Object.keys(errors).length > 0) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (modalMode === \"create\") {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_3__.storeApi.create({\n                    ...storeForm,\n                    userId: (user === null || user === void 0 ? void 0 : user.id) || \"\"\n                });\n            } else {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_3__.storeApi.update(editingStoreId, storeForm);\n            }\n            setIsStoreModalOpen(false);\n            resetForm();\n            fetchStores();\n        } catch (error) {\n            console.error(\"Failed to \".concat(modalMode, \" store:\"), error);\n        // You might want to show a toast notification here\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const openDeleteModal = (store)=>{\n        setDeleteStoreId(store.id);\n        setIsDeleteOpen(true);\n    };\n    const handleDeleteStore = async ()=>{\n        if (!deleteStoreId) return;\n        setIsSubmitting(true);\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_3__.storeApi.delete(deleteStoreId);\n            setIsDeleteOpen(false);\n            setDeleteStoreId(null);\n            fetchStores();\n            // If we deleted the current store, clear it\n            if (currentStoreId === deleteStoreId) {\n                setCurrentStoreId(null);\n            }\n        } catch (error) {\n            console.error(\"Failed to delete store:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n            router.replace(\"/login?loggedOut=1\");\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n        }\n    };\n    const currentStore = stores.find((store)=>store.id === currentStoreId);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-slate-900 shadow-sm border-b border-white/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-slate-100\",\n                                        children: \"Teno Store\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: storeMenuRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsStoreMenuOpen(!isStoreMenuOpen),\n                                            className: \"inline-flex items-center px-4 py-2 border border-slate-600 text-sm leading-4 font-medium rounded-md text-slate-300 bg-slate-800 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors min-w-[220px]\",\n                                            disabled: storesLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 mr-2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex-1 text-left truncate\",\n                                                    children: storesLoading ? \"Loading...\" : currentStore ? currentStore.name : \"Select Store\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-2 transition-transform \".concat(isStoreMenuOpen ? \"rotate-180\" : \"\"),\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        isStoreMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-0 mt-2 w-full min-w-[280px] bg-slate-800 border border-slate-600 rounded-lg shadow-xl z-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCreateModalOpen,\n                                                        className: \"flex items-center px-4 py-3 text-sm text-emerald-400 hover:bg-slate-700 hover:text-emerald-300 w-full text-left transition-colors border-b border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Create New Store\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-slate-400\",\n                                                                        children: \"Set up a new store\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    stores.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-4 py-2 border-b border-slate-600\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-400 uppercase tracking-wide font-medium\",\n                                                                    children: \"Select Store\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-h-60 overflow-y-auto\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setCurrentStoreId(null);\n                                                                            setIsStoreMenuOpen(false);\n                                                                        },\n                                                                        className: \"flex items-center px-4 py-2 text-sm w-full text-left transition-colors \".concat(!currentStoreId ? \"bg-slate-700 text-slate-100\" : \"text-slate-300 hover:bg-slate-700 hover:text-slate-100\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 rounded-full bg-slate-500 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"italic\",\n                                                                                children: \"No store selected\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            !currentStoreId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4 ml-auto\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M5 13l4 4L19 7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                    lineNumber: 431,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 430,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    stores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>{\n                                                                                        setCurrentStoreId(store.id);\n                                                                                        setIsStoreMenuOpen(false);\n                                                                                    },\n                                                                                    className: \"flex items-center px-4 py-2 text-sm w-full text-left transition-colors \".concat(currentStoreId === store.id ? \"bg-emerald-600/20 text-emerald-200\" : \"text-slate-300 hover:bg-slate-700 hover:text-slate-100\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-2 h-2 rounded-full mr-3 \".concat(currentStoreId === store.id ? \"bg-emerald-400\" : \"bg-slate-400\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 450,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex-1 min-w-0\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium truncate\",\n                                                                                                    children: store.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                    lineNumber: 454,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                store.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-slate-400 truncate\",\n                                                                                                    children: store.description\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                    lineNumber: 456,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 453,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        currentStoreId === store.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 ml-2\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M5 13l4 4L19 7\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                lineNumber: 461,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 460,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                    lineNumber: 439,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                currentStoreId === store.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: (e)=>{\n                                                                                                e.stopPropagation();\n                                                                                                handleEditModalOpen(store);\n                                                                                            },\n                                                                                            className: \"p-1 rounded text-slate-400 hover:text-slate-200 hover:bg-slate-600 transition-colors\",\n                                                                                            title: \"Edit store\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-3 h-3\",\n                                                                                                fill: \"none\",\n                                                                                                stroke: \"currentColor\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\",\n                                                                                                    strokeWidth: 2,\n                                                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                    lineNumber: 478,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                lineNumber: 477,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 469,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: (e)=>{\n                                                                                                e.stopPropagation();\n                                                                                                handleDeleteModalOpen(store);\n                                                                                            },\n                                                                                            className: \"p-1 rounded text-slate-400 hover:text-red-400 hover:bg-slate-600 transition-colors\",\n                                                                                            title: \"Delete store\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-3 h-3\",\n                                                                                                fill: \"none\",\n                                                                                                stroke: \"currentColor\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\",\n                                                                                                    strokeWidth: 2,\n                                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                    lineNumber: 490,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                lineNumber: 489,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 481,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                    lineNumber: 468,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, store.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    stores.length === 0 && !storesLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-4 py-6 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-8 h-8 mx-auto text-slate-500 mb-2\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-400 mb-2\",\n                                                                children: \"No stores found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: \"Create your first store to get started\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: preferredCurrency,\n                                        onChange: (e)=>setCurrency(e.target.value),\n                                        className: \"block w-full pl-3 pr-10 py-2 text-base border-slate-600 bg-slate-800 text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:ring-emerald-400/50 sm:text-sm rounded-md\",\n                                        children: currencyOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: option.value,\n                                                children: [\n                                                    option.symbol,\n                                                    \" \",\n                                                    option.label\n                                                ]\n                                            }, option.value, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: preferredLanguage,\n                                        onChange: (e)=>setLanguage(e.target.value),\n                                        className: \"block w-full pl-3 pr-10 py-2 text-base border-slate-600 bg-slate-800 text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-emerald-400/50 sm:text-sm rounded-md\",\n                                        children: languageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: option.value,\n                                                children: option.nativeLabel\n                                            }, option.value, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-slate-300\",\n                                                children: user === null || user === void 0 ? void 0 : user.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleLogout,\n                                                className: \"text-sm text-slate-400 hover:text-slate-200 transition-colors\",\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this),\n            isStoreModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50\",\n                        onClick: ()=>setIsStoreModalOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full max-w-lg overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-indigo-500/20 blur opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex flex-col max-h-[90vh]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-white/10 flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full \".concat(modalMode === \"create\" ? \"bg-emerald-400\" : \"bg-blue-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-slate-100\",\n                                                        children: modalMode === \"create\" ? \"Create New Store\" : \"Edit Store\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsStoreModalOpen(false),\n                                                className: \"text-slate-400 hover:text-slate-200 rounded-md p-1 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-200\",\n                                                            children: [\n                                                                \"Store Name \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-400\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 34\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            ref: storeNameInputRef,\n                                                            type: \"text\",\n                                                            value: storeForm.name,\n                                                            onChange: (e)=>{\n                                                                setStoreForm({\n                                                                    ...storeForm,\n                                                                    name: e.target.value\n                                                                });\n                                                                if (formErrors.name) setFormErrors({\n                                                                    ...formErrors,\n                                                                    name: undefined\n                                                                });\n                                                            },\n                                                            className: \"w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 transition-all \".concat(formErrors.name ? \"border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50\" : \"border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50\"),\n                                                            placeholder: \"e.g., Tech Haven, Fashion Forward, Green Groceries\",\n                                                            maxLength: 50\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        formErrors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-400 text-xs flex items-center mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 mr-1\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                formErrors.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-slate-400\",\n                                                            children: [\n                                                                storeForm.name.length,\n                                                                \"/50 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-200\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: storeForm.description,\n                                                            onChange: (e)=>{\n                                                                setStoreForm({\n                                                                    ...storeForm,\n                                                                    description: e.target.value\n                                                                });\n                                                                if (formErrors.description) setFormErrors({\n                                                                    ...formErrors,\n                                                                    description: undefined\n                                                                });\n                                                            },\n                                                            className: \"w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 transition-all resize-none \".concat(formErrors.description ? \"border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50\" : \"border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50\"),\n                                                            placeholder: \"Describe your store's mission, products, or specialty...\",\n                                                            rows: 3,\n                                                            maxLength: 200\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        formErrors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-400 text-xs flex items-center mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 mr-1\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 650,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                formErrors.description\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-slate-400\",\n                                                            children: [\n                                                                storeForm.description.length,\n                                                                \"/200 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-slate-200\",\n                                                                    children: [\n                                                                        \"Currency \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 663,\n                                                                            columnNumber: 34\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: storeForm.currency,\n                                                                    onChange: (e)=>{\n                                                                        setStoreForm({\n                                                                            ...storeForm,\n                                                                            currency: e.target.value\n                                                                        });\n                                                                        if (formErrors.currency) setFormErrors({\n                                                                            ...formErrors,\n                                                                            currency: undefined\n                                                                        });\n                                                                    },\n                                                                    className: \"w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 focus:outline-none focus:ring-2 transition-all \".concat(formErrors.currency ? \"border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50\" : \"border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select currency\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        currencyOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: option.value,\n                                                                                children: [\n                                                                                    option.symbol,\n                                                                                    \" \",\n                                                                                    option.label\n                                                                                ]\n                                                                            }, option.value, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 679,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                formErrors.currency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-400 text-xs flex items-center mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3 h-3 mr-1\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 687,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 686,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        formErrors.currency\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-slate-200\",\n                                                                    children: [\n                                                                        \"Language \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 34\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: storeForm.preferredLanguage,\n                                                                    onChange: (e)=>{\n                                                                        setStoreForm({\n                                                                            ...storeForm,\n                                                                            preferredLanguage: e.target.value\n                                                                        });\n                                                                        if (formErrors.preferredLanguage) setFormErrors({\n                                                                            ...formErrors,\n                                                                            preferredLanguage: undefined\n                                                                        });\n                                                                    },\n                                                                    className: \"w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 focus:outline-none focus:ring-2 transition-all \".concat(formErrors.preferredLanguage ? \"border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50\" : \"border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select language\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 711,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        languageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: option.value,\n                                                                                children: option.nativeLabel\n                                                                            }, option.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 713,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                formErrors.preferredLanguage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-400 text-xs flex items-center mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3 h-3 mr-1\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 721,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 720,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        formErrors.preferredLanguage\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-t border-white/10 flex items-center justify-end gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsStoreModalOpen(false),\n                                                disabled: isSubmitting,\n                                                className: \"border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-colors disabled:opacity-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSubmitStore,\n                                                disabled: isSubmitting || !storeForm.name.trim() || !storeForm.currency || !storeForm.preferredLanguage,\n                                                className: \"px-6 py-2 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-lg font-medium shadow-lg shadow-emerald-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center\",\n                                                children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 749,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        modalMode === \"create\" ? \"Creating...\" : \"Updating...\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: modalMode === \"create\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Create Store\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Update Store\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                lineNumber: 567,\n                columnNumber: 9\n            }, this),\n            isDeleteOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50\",\n                        onClick: ()=>setIsDeleteOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                        lineNumber: 782,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full max-w-md overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-red-500/20 via-orange-500/20 to-red-500/20 blur opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                lineNumber: 786,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-white/10 flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-slate-100\",\n                                                        children: \"Delete Store\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 792,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsDeleteOpen(false),\n                                                disabled: isSubmitting,\n                                                className: \"text-slate-400 hover:text-slate-200 rounded-md p-1 transition-colors disabled:opacity-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-slate-100 font-medium mb-2\",\n                                                            children: \"Are you absolutely sure?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-300 mb-3\",\n                                                            children: [\n                                                                \"This will permanently delete the store \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-slate-100\",\n                                                                    children: [\n                                                                        '\"',\n                                                                        (_stores_find = stores.find((s)=>s.id === deleteStoreId)) === null || _stores_find === void 0 ? void 0 : _stores_find.name,\n                                                                        '\"'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 62\n                                                                }, this),\n                                                                \" and all associated data.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-400\",\n                                                            children: \"This action cannot be undone.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-t border-white/10 flex items-center justify-end gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsDeleteOpen(false),\n                                                disabled: isSubmitting,\n                                                className: \"border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-colors disabled:opacity-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleDeleteStore,\n                                                disabled: isSubmitting,\n                                                className: \"px-4 py-2 bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 text-white rounded-lg font-medium shadow-lg shadow-red-500/25 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                                                children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Deleting...\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 mr-1\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Delete Store\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                        lineNumber: 785,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                lineNumber: 781,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, this);\n}\n_s(TopTaskBar, \"zO63j/sXuPiVyxu0QjWBcSXjuGM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_6__.usePreferences,\n        _context_StoreContext__WEBPACK_IMPORTED_MODULE_5__.useStore\n    ];\n});\n_c = TopTaskBar;\nvar _c;\n$RefreshReg$(_c, \"TopTaskBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/TopTaskBar.tsx\n"));

/***/ })

});