import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';

@Entity('sessions')
export class Session {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar' })
  sessionToken: string;

  @Column({ type: 'varchar' })
  userId: string;

  @Column({ type: 'timestamp' })
  expires: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.sessions)
  user: any;
}
