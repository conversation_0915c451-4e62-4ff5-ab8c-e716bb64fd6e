"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const storageKey = \"teno:auth:user\";\n    const lastUserIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const current = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(current);\n            try {\n                if (current) {\n                    localStorage.setItem(storageKey, JSON.stringify(current));\n                } else {\n                    localStorage.removeItem(storageKey);\n                }\n            } catch (e) {}\n            try {\n                const prevId = lastUserIdRef.current;\n                var _current_id;\n                const nextId = (_current_id = current === null || current === void 0 ? void 0 : current.id) !== null && _current_id !== void 0 ? _current_id : null;\n                const changed = prevId !== nextId;\n                lastUserIdRef.current = nextId;\n                if (changed && \"object\" !== \"undefined\") {\n                    const evtName = current ? \"teno:auth:login\" : \"teno:auth:logout\";\n                    window.dispatchEvent(new CustomEvent(\"teno:auth:change\", {\n                        detail: {\n                            user: current\n                        }\n                    }));\n                    window.dispatchEvent(new CustomEvent(evtName, {\n                        detail: {\n                            user: current\n                        }\n                    }));\n                }\n            } catch (e) {}\n        } catch (err) {\n            console.warn(\"[AuthContext] Error during refresh:\", err);\n            setError(\"Failed to load user\");\n            setUser(null);\n            try {\n                localStorage.removeItem(storageKey);\n            } catch (e) {}\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const loginWithGoogle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((nextPath)=>{\n        (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.redirectToGoogleAuth)(nextPath);\n    }, []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        // Optimistically clear local state for snappier UX\n        setUser(null);\n        try {\n            localStorage.removeItem(storageKey);\n        } catch (e) {}\n        try {\n            await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.performLogout)();\n        } finally{\n            // Always refresh after logout to ensure backend/session is in sync\n            await refresh();\n        }\n    }, [\n        refresh\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Optimistically hydrate from localStorage for fast first paint\n        try {\n            const raw = localStorage.getItem(storageKey);\n            if (raw) {\n                const cached = JSON.parse(raw);\n                setUser(cached);\n                var _cached_id;\n                lastUserIdRef.current = (_cached_id = cached === null || cached === void 0 ? void 0 : cached.id) !== null && _cached_id !== void 0 ? _cached_id : null;\n                // If we have a cached user, try to refresh from backend\n                refresh();\n            } else {\n                // No cached user, just set loading to false\n                setIsLoading(false);\n            }\n        } catch (e) {\n            console.warn(\"[AuthContext] Error reading cached user:\", e);\n            setIsLoading(false);\n        }\n        // Listen for global unauthorized signals to immediately drop user state\n        const onUnauthorized = ()=>{\n            (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.clearClientAuthArtifacts)();\n            setUser(null);\n            lastUserIdRef.current = null;\n        };\n        if (true) {\n            window.addEventListener(\"teno:auth:unauthorized\", onUnauthorized);\n        }\n        return ()=>{\n            if (true) {\n                window.removeEventListener(\"teno:auth:unauthorized\", onUnauthorized);\n            }\n        };\n    }, [\n        refresh\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            isLoading,\n            error,\n            refresh,\n            loginWithGoogle,\n            logout\n        }), [\n        user,\n        isLoading,\n        error,\n        refresh,\n        loginWithGoogle,\n        logout\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"i1Hafzw8L1e75c2rxUDcFO0bvko=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!ctx) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return ctx;\n}\n_s1(useAuth, \"/dMy7t63NXD4eYACoT93CePwGrg=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/AuthContext.tsx\n"));

/***/ }),

/***/ "./src/context/PreferencesContext.tsx":
/*!********************************************!*\
  !*** ./src/context/PreferencesContext.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreferencesProvider: function() { return /* binding */ PreferencesProvider; },\n/* harmony export */   usePreferences: function() { return /* binding */ usePreferences; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _utils_preferences__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/preferences */ \"./src/utils/preferences.ts\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst PreferencesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction getBrowserDefaults() {\n    const { language, country, currency } = (0,_utils_preferences__WEBPACK_IMPORTED_MODULE_3__.getBrowserPreferenceDefaults)();\n    return {\n        language,\n        country,\n        currency\n    };\n}\nfunction PreferencesProvider(param) {\n    let { children } = param;\n    _s();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const defaults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getBrowserDefaults(), []);\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.language);\n    const [currency, setCurrencyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.currency);\n    const [country, setCountryState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.country);\n    const storageKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const userId = (user === null || user === void 0 ? void 0 : user.id) ? String(user.id) : \"anon\";\n        const key = \"teno:prefs:\".concat(userId);\n        console.debug(\"[Prefs] storageKey computed\", {\n            userId,\n            key\n        });\n        return key;\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Hydrate from localStorage on mount or when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const raw = localStorage.getItem(storageKey);\n            console.debug(\"[Prefs] hydrate start\", {\n                storageKey,\n                raw\n            });\n            if (raw) {\n                const parsed = JSON.parse(raw);\n                const nextLanguage = parsed.language || defaults.language;\n                const nextCurrency = parsed.currency || defaults.currency;\n                const nextCountry = parsed.country || defaults.country;\n                console.debug(\"[Prefs] hydrate parsed\", {\n                    parsed,\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n            } else {\n                console.debug(\"[Prefs] hydrate no existing, using defaults\", defaults);\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        } catch (e) {\n            console.debug(\"[Prefs] hydrate error, falling back to defaults\", defaults);\n            setLanguageState(defaults.language);\n            setCurrencyState(defaults.currency);\n            setCountryState(defaults.country);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        storageKey\n    ]);\n    // Re-hydrate on auth changes (login/logout) since key scope changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const onAuthChange = ()=>{\n            try {\n                const raw = localStorage.getItem(storageKey);\n                if (raw) {\n                    const parsed = JSON.parse(raw);\n                    setLanguageState(parsed.language || defaults.language);\n                    setCurrencyState(parsed.currency || defaults.currency);\n                    setCountryState(parsed.country || defaults.country);\n                } else {\n                    setLanguageState(defaults.language);\n                    setCurrencyState(defaults.currency);\n                    setCountryState(defaults.country);\n                }\n            } catch (e) {\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        };\n        if (true) {\n            window.addEventListener(\"teno:auth:change\", onAuthChange);\n        }\n        return ()=>{\n            if (true) {\n                window.removeEventListener(\"teno:auth:change\", onAuthChange);\n            }\n        };\n    }, [\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    // If a user is present, fetch server-side preferences and apply\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userId = (user === null || user === void 0 ? void 0 : user.id) ? String(user.id) : null;\n        if (!userId) return;\n        let aborted = false;\n        (async ()=>{\n            try {\n                const url = \"\".concat((0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.getBackendUrl)(), \"/users/\").concat(encodeURIComponent(userId));\n                console.debug(\"[Prefs] fetching server preferences\", {\n                    userId,\n                    url\n                });\n                const resp = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.fetchWithCredentials)(url);\n                if (!resp.ok) return;\n                const payload = await resp.json();\n                if (aborted || !payload) return;\n                const nextLanguage = payload.preferredLanguage || defaults.language;\n                const nextCurrency = payload.preferredCurrency || defaults.currency;\n                const nextCountry = payload.countryCode || defaults.country;\n                console.debug(\"[Prefs] server preferences received\", {\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n                try {\n                    localStorage.setItem(storageKey, JSON.stringify({\n                        language: nextLanguage,\n                        currency: nextCurrency,\n                        country: nextCountry\n                    }));\n                } catch (e) {}\n            } catch (e) {}\n        })();\n        return ()=>{\n            aborted = true;\n        };\n    }, [\n        user === null || user === void 0 ? void 0 : user.id,\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    const persist = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((next)=>{\n        try {\n            const current = {\n                language,\n                currency,\n                country\n            };\n            const merged = {\n                ...current,\n                ...next\n            };\n            console.debug(\"[Prefs] persist\", {\n                storageKey,\n                current,\n                next,\n                merged\n            });\n            localStorage.setItem(storageKey, JSON.stringify(merged));\n        } catch (e) {}\n    }, [\n        language,\n        currency,\n        country,\n        storageKey\n    ]);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lng)=>{\n        setLanguageState(lng);\n        persist({\n            language: lng\n        });\n    }, [\n        persist\n    ]);\n    const setCurrency = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cur)=>{\n        console.debug(\"[Prefs] setCurrency\", {\n            from: currency,\n            to: cur\n        });\n        setCurrencyState(cur);\n        persist({\n            currency: cur\n        });\n    }, [\n        persist,\n        currency\n    ]);\n    const setCountry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cc)=>{\n        setCountryState(cc);\n        persist({\n            country: cc\n        });\n    }, [\n        persist\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            language,\n            currency,\n            country,\n            setLanguage,\n            setCurrency,\n            setCountry\n        }), [\n        language,\n        currency,\n        country,\n        setLanguage,\n        setCurrency,\n        setCountry\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreferencesContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\PreferencesContext.tsx\",\n        lineNumber: 169,\n        columnNumber: 3\n    }, this);\n}\n_s(PreferencesProvider, \"FxcX8wsLUqDDd1Kr0oVnren+WS4=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = PreferencesProvider;\nfunction usePreferences() {\n    _s1();\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PreferencesContext);\n    if (!ctx) {\n        throw new Error(\"usePreferences must be used within a PreferencesProvider\");\n    }\n    return ctx;\n}\n_s1(usePreferences, \"/dMy7t63NXD4eYACoT93CePwGrg=\");\nvar _c;\n$RefreshReg$(_c, \"PreferencesProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29udGV4dC9QcmVmZXJlbmNlc0NvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQTZGO0FBQ3JEO0FBQytFO0FBQ25EO0FBV3BFLE1BQU1VLG1DQUFxQlYsb0RBQWFBLENBQXNDVztBQVE5RSxTQUFTQztJQUNSLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxPQUFPLEVBQUVDLFFBQVEsRUFBRSxHQUFHUixnRkFBNEJBO0lBQ3BFLE9BQU87UUFBRU07UUFBVUM7UUFBU0M7SUFBUztBQUN0QztBQUVPLFNBQVNDLG9CQUFvQixLQUEyQztRQUEzQyxFQUFFQyxRQUFRLEVBQWlDLEdBQTNDOztJQUNuQyxNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHWixxREFBT0E7SUFDeEIsTUFBTWEsV0FBV2YsOENBQU9BLENBQUMsSUFBTVEsc0JBQXNCLEVBQUU7SUFDdkQsTUFBTSxDQUFDQyxVQUFVTyxpQkFBaUIsR0FBR2YsK0NBQVFBLENBQW9CYyxTQUFTTixRQUFRO0lBQ2xGLE1BQU0sQ0FBQ0UsVUFBVU0saUJBQWlCLEdBQUdoQiwrQ0FBUUEsQ0FBb0JjLFNBQVNKLFFBQVE7SUFDbEYsTUFBTSxDQUFDRCxTQUFTUSxnQkFBZ0IsR0FBR2pCLCtDQUFRQSxDQUFjYyxTQUFTTCxPQUFPO0lBRXpFLE1BQU1TLGFBQWFuQiw4Q0FBT0EsQ0FBQztRQUMxQixNQUFNb0IsU0FBU04sQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNTyxFQUFFLElBQUdDLE9BQU9SLEtBQUtPLEVBQUUsSUFBSTtRQUM1QyxNQUFNRSxNQUFNLGNBQXFCLE9BQVBIO1FBQzFCSSxRQUFRQyxLQUFLLENBQUMsK0JBQStCO1lBQUVMO1lBQVFHO1FBQUk7UUFDM0QsT0FBT0E7SUFDUixHQUFHO1FBQUNULGlCQUFBQSwyQkFBQUEsS0FBTU8sRUFBRTtLQUFDO0lBRWIsMERBQTBEO0lBQzFEdEIsZ0RBQVNBLENBQUM7UUFDVCxJQUFJO1lBQ0gsTUFBTTJCLE1BQU1DLGFBQWFDLE9BQU8sQ0FBQ1Q7WUFDakNLLFFBQVFDLEtBQUssQ0FBQyx5QkFBeUI7Z0JBQUVOO2dCQUFZTztZQUFJO1lBQ3pELElBQUlBLEtBQUs7Z0JBQ1IsTUFBTUcsU0FBU0MsS0FBS0MsS0FBSyxDQUFDTDtnQkFDMUIsTUFBTU0sZUFBZUgsT0FBT3BCLFFBQVEsSUFBSU0sU0FBU04sUUFBUTtnQkFDekQsTUFBTXdCLGVBQWVKLE9BQU9sQixRQUFRLElBQUlJLFNBQVNKLFFBQVE7Z0JBQ3pELE1BQU11QixjQUFjTCxPQUFPbkIsT0FBTyxJQUFJSyxTQUFTTCxPQUFPO2dCQUN0RGMsUUFBUUMsS0FBSyxDQUFDLDBCQUEwQjtvQkFBRUk7b0JBQVFHO29CQUFjQztvQkFBY0M7Z0JBQVk7Z0JBQzFGbEIsaUJBQWlCZ0I7Z0JBQ2pCZixpQkFBaUJnQjtnQkFDakJmLGdCQUFnQmdCO1lBQ2pCLE9BQU87Z0JBQ05WLFFBQVFDLEtBQUssQ0FBQywrQ0FBK0NWO2dCQUM3REMsaUJBQWlCRCxTQUFTTixRQUFRO2dCQUNsQ1EsaUJBQWlCRixTQUFTSixRQUFRO2dCQUNsQ08sZ0JBQWdCSCxTQUFTTCxPQUFPO1lBQ2pDO1FBQ0QsRUFBRSxVQUFNO1lBQ1BjLFFBQVFDLEtBQUssQ0FBQyxtREFBbURWO1lBQ2pFQyxpQkFBaUJELFNBQVNOLFFBQVE7WUFDbENRLGlCQUFpQkYsU0FBU0osUUFBUTtZQUNsQ08sZ0JBQWdCSCxTQUFTTCxPQUFPO1FBQ2pDO0lBQ0EsdURBQXVEO0lBQ3hELEdBQUc7UUFBQ1M7S0FBVztJQUVmLG9FQUFvRTtJQUNwRXBCLGdEQUFTQSxDQUFDO1FBQ1QsTUFBTW9DLGVBQWU7WUFDcEIsSUFBSTtnQkFDSCxNQUFNVCxNQUFNQyxhQUFhQyxPQUFPLENBQUNUO2dCQUNqQyxJQUFJTyxLQUFLO29CQUNSLE1BQU1HLFNBQVNDLEtBQUtDLEtBQUssQ0FBQ0w7b0JBQzFCVixpQkFBaUJhLE9BQU9wQixRQUFRLElBQUlNLFNBQVNOLFFBQVE7b0JBQ3JEUSxpQkFBaUJZLE9BQU9sQixRQUFRLElBQUlJLFNBQVNKLFFBQVE7b0JBQ3JETyxnQkFBZ0JXLE9BQU9uQixPQUFPLElBQUlLLFNBQVNMLE9BQU87Z0JBQ25ELE9BQU87b0JBQ05NLGlCQUFpQkQsU0FBU04sUUFBUTtvQkFDbENRLGlCQUFpQkYsU0FBU0osUUFBUTtvQkFDbENPLGdCQUFnQkgsU0FBU0wsT0FBTztnQkFDakM7WUFDRCxFQUFFLFVBQU07Z0JBQ1BNLGlCQUFpQkQsU0FBU04sUUFBUTtnQkFDbENRLGlCQUFpQkYsU0FBU0osUUFBUTtnQkFDbENPLGdCQUFnQkgsU0FBU0wsT0FBTztZQUNqQztRQUNEO1FBQ0EsSUFBSSxJQUFrQixFQUFhO1lBQ2xDMEIsT0FBT0MsZ0JBQWdCLENBQUMsb0JBQW9CRjtRQUM3QztRQUNBLE9BQU87WUFDTixJQUFJLElBQWtCLEVBQWE7Z0JBQ2xDQyxPQUFPRSxtQkFBbUIsQ0FBQyxvQkFBb0JIO1lBQ2hEO1FBQ0Q7SUFDRCxHQUFHO1FBQUNoQjtRQUFZSixTQUFTTixRQUFRO1FBQUVNLFNBQVNKLFFBQVE7UUFBRUksU0FBU0wsT0FBTztLQUFDO0lBRXZFLGdFQUFnRTtJQUNoRVgsZ0RBQVNBLENBQUM7UUFDVCxNQUFNcUIsU0FBU04sQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNTyxFQUFFLElBQUdDLE9BQU9SLEtBQUtPLEVBQUUsSUFBSTtRQUM1QyxJQUFJLENBQUNELFFBQVE7UUFDYixJQUFJbUIsVUFBVTtRQUNiO1lBQ0EsSUFBSTtnQkFDQyxNQUFNQyxNQUFNLEdBQTRCQyxPQUF6QnBDLDBEQUFhQSxJQUFHLFdBQW9DLE9BQTNCb0MsbUJBQW1CckI7Z0JBQy9ESSxRQUFRQyxLQUFLLENBQUMsdUNBQXVDO29CQUFFTDtvQkFBUW9CO2dCQUFJO2dCQUNuRSxNQUFNRSxPQUFPLE1BQU10QyxpRUFBb0JBLENBQUNvQztnQkFDeEMsSUFBSSxDQUFDRSxLQUFLQyxFQUFFLEVBQUU7Z0JBQ2QsTUFBTUMsVUFBVSxNQUFNRixLQUFLRyxJQUFJO2dCQUMvQixJQUFJTixXQUFXLENBQUNLLFNBQVM7Z0JBQ3pCLE1BQU1aLGVBQWUsUUFBU2MsaUJBQWlCLElBQTBCL0IsU0FBU04sUUFBUTtnQkFDMUYsTUFBTXdCLGVBQWUsUUFBU2MsaUJBQWlCLElBQTBCaEMsU0FBU0osUUFBUTtnQkFDMUYsTUFBTXVCLGNBQWMsUUFBU2MsV0FBVyxJQUFvQmpDLFNBQVNMLE9BQU87Z0JBQzVFYyxRQUFRQyxLQUFLLENBQUMsdUNBQXVDO29CQUFFTztvQkFBY0M7b0JBQWNDO2dCQUFZO2dCQUMvRmxCLGlCQUFpQmdCO2dCQUNqQmYsaUJBQWlCZ0I7Z0JBQ2pCZixnQkFBZ0JnQjtnQkFDaEIsSUFBSTtvQkFDSFAsYUFBYXNCLE9BQU8sQ0FBQzlCLFlBQVlXLEtBQUtvQixTQUFTLENBQUM7d0JBQUV6QyxVQUFVdUI7d0JBQWNyQixVQUFVc0I7d0JBQWN2QixTQUFTd0I7b0JBQVk7Z0JBQ3hILEVBQUUsVUFBTSxDQUFDO1lBQ1YsRUFBRSxVQUFNLENBQUM7UUFDVjtRQUNBLE9BQU87WUFBUUssVUFBVTtRQUFNO0lBQ2hDLEdBQUc7UUFBQ3pCLGlCQUFBQSwyQkFBQUEsS0FBTU8sRUFBRTtRQUFFRjtRQUFZSixTQUFTTixRQUFRO1FBQUVNLFNBQVNKLFFBQVE7UUFBRUksU0FBU0wsT0FBTztLQUFDO0lBRWpGLE1BQU15QyxVQUFVdEQsa0RBQVdBLENBQUMsQ0FBQ3VEO1FBQzVCLElBQUk7WUFDSCxNQUFNQyxVQUEwQjtnQkFDL0I1QztnQkFDQUU7Z0JBQ0FEO1lBQ0Q7WUFDQSxNQUFNNEMsU0FBUztnQkFBRSxHQUFHRCxPQUFPO2dCQUFFLEdBQUdELElBQUk7WUFBQztZQUNyQzVCLFFBQVFDLEtBQUssQ0FBQyxtQkFBbUI7Z0JBQUVOO2dCQUFZa0M7Z0JBQVNEO2dCQUFNRTtZQUFPO1lBQ3JFM0IsYUFBYXNCLE9BQU8sQ0FBQzlCLFlBQVlXLEtBQUtvQixTQUFTLENBQUNJO1FBQ2pELEVBQUUsVUFBTSxDQUFDO0lBQ1YsR0FBRztRQUFDN0M7UUFBVUU7UUFBVUQ7UUFBU1M7S0FBVztJQUU1QyxNQUFNb0MsY0FBYzFELGtEQUFXQSxDQUFDLENBQUMyRDtRQUNoQ3hDLGlCQUFpQndDO1FBQ2pCTCxRQUFRO1lBQUUxQyxVQUFVK0M7UUFBSTtJQUN6QixHQUFHO1FBQUNMO0tBQVE7SUFFWixNQUFNTSxjQUFjNUQsa0RBQVdBLENBQUMsQ0FBQzZEO1FBQ2hDbEMsUUFBUUMsS0FBSyxDQUFDLHVCQUF1QjtZQUFFa0MsTUFBTWhEO1lBQVVpRCxJQUFJRjtRQUFJO1FBQy9EekMsaUJBQWlCeUM7UUFDakJQLFFBQVE7WUFBRXhDLFVBQVUrQztRQUFJO0lBQ3pCLEdBQUc7UUFBQ1A7UUFBU3hDO0tBQVM7SUFFdEIsTUFBTWtELGFBQWFoRSxrREFBV0EsQ0FBQyxDQUFDaUU7UUFDL0I1QyxnQkFBZ0I0QztRQUNoQlgsUUFBUTtZQUFFekMsU0FBU29EO1FBQUc7SUFDdkIsR0FBRztRQUFDWDtLQUFRO0lBRVosTUFBTVksUUFBUS9ELDhDQUFPQSxDQUEwQixJQUFPO1lBQ3JEUztZQUNBRTtZQUNBRDtZQUNBNkM7WUFDQUU7WUFDQUk7UUFDRCxJQUFJO1FBQUNwRDtRQUFVRTtRQUFVRDtRQUFTNkM7UUFBYUU7UUFBYUk7S0FBVztJQUV2RSxxQkFDQyw4REFBQ3ZELG1CQUFtQjBELFFBQVE7UUFBQ0QsT0FBT0E7a0JBQ2xDbEQ7Ozs7OztBQUdKO0dBakpnQkQ7O1FBQ0VWLGlEQUFPQTs7O0tBRFRVO0FBbUpULFNBQVNxRDs7SUFDZixNQUFNQyxNQUFNcEUsaURBQVVBLENBQUNRO0lBQ3ZCLElBQUksQ0FBQzRELEtBQUs7UUFDVCxNQUFNLElBQUlDLE1BQU07SUFDakI7SUFDQSxPQUFPRDtBQUNSO0lBTmdCRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29udGV4dC9QcmVmZXJlbmNlc0NvbnRleHQudHN4P2VlZTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ2FsbGJhY2ssIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlTWVtbywgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICcuL0F1dGhDb250ZXh0JztcclxuaW1wb3J0IHsgQ291bnRyeUNvZGUsIFN1cHBvcnRlZEN1cnJlbmN5LCBTdXBwb3J0ZWRMYW5ndWFnZSwgZ2V0QnJvd3NlclByZWZlcmVuY2VEZWZhdWx0cyB9IGZyb20gJy4uL3V0aWxzL3ByZWZlcmVuY2VzJztcclxuaW1wb3J0IHsgZmV0Y2hXaXRoQ3JlZGVudGlhbHMsIGdldEJhY2tlbmRVcmwgfSBmcm9tICcuLi91dGlscy9hdXRoJztcclxuXHJcbmludGVyZmFjZSBQcmVmZXJlbmNlc0NvbnRleHRWYWx1ZSB7XHJcblx0bGFuZ3VhZ2U6IFN1cHBvcnRlZExhbmd1YWdlO1xyXG5cdGN1cnJlbmN5OiBTdXBwb3J0ZWRDdXJyZW5jeTtcclxuXHRjb3VudHJ5OiBDb3VudHJ5Q29kZTtcclxuXHRzZXRMYW5ndWFnZTogKGxhbmd1YWdlOiBTdXBwb3J0ZWRMYW5ndWFnZSkgPT4gdm9pZDtcclxuXHRzZXRDdXJyZW5jeTogKGN1cnJlbmN5OiBTdXBwb3J0ZWRDdXJyZW5jeSkgPT4gdm9pZDtcclxuXHRzZXRDb3VudHJ5OiAoY291bnRyeTogQ291bnRyeUNvZGUpID0+IHZvaWQ7XHJcbn1cclxuXHJcbmNvbnN0IFByZWZlcmVuY2VzQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8UHJlZmVyZW5jZXNDb250ZXh0VmFsdWUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XHJcblxyXG50eXBlIFBlcnNpc3RlZFByZWZzID0ge1xyXG5cdGxhbmd1YWdlPzogU3VwcG9ydGVkTGFuZ3VhZ2U7XHJcblx0Y3VycmVuY3k/OiBTdXBwb3J0ZWRDdXJyZW5jeTtcclxuXHRjb3VudHJ5PzogQ291bnRyeUNvZGU7XHJcbn07XHJcblxyXG5mdW5jdGlvbiBnZXRCcm93c2VyRGVmYXVsdHMoKTogUmVxdWlyZWQ8UGVyc2lzdGVkUHJlZnM+IHtcclxuXHRjb25zdCB7IGxhbmd1YWdlLCBjb3VudHJ5LCBjdXJyZW5jeSB9ID0gZ2V0QnJvd3NlclByZWZlcmVuY2VEZWZhdWx0cygpO1xyXG5cdHJldHVybiB7IGxhbmd1YWdlLCBjb3VudHJ5LCBjdXJyZW5jeSB9O1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUHJlZmVyZW5jZXNQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XHJcblx0Y29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XHJcblx0Y29uc3QgZGVmYXVsdHMgPSB1c2VNZW1vKCgpID0+IGdldEJyb3dzZXJEZWZhdWx0cygpLCBbXSk7XHJcblx0Y29uc3QgW2xhbmd1YWdlLCBzZXRMYW5ndWFnZVN0YXRlXSA9IHVzZVN0YXRlPFN1cHBvcnRlZExhbmd1YWdlPihkZWZhdWx0cy5sYW5ndWFnZSk7XHJcblx0Y29uc3QgW2N1cnJlbmN5LCBzZXRDdXJyZW5jeVN0YXRlXSA9IHVzZVN0YXRlPFN1cHBvcnRlZEN1cnJlbmN5PihkZWZhdWx0cy5jdXJyZW5jeSk7XHJcblx0Y29uc3QgW2NvdW50cnksIHNldENvdW50cnlTdGF0ZV0gPSB1c2VTdGF0ZTxDb3VudHJ5Q29kZT4oZGVmYXVsdHMuY291bnRyeSk7XHJcblxyXG5cdGNvbnN0IHN0b3JhZ2VLZXkgPSB1c2VNZW1vKCgpID0+IHtcclxuXHRcdGNvbnN0IHVzZXJJZCA9IHVzZXI/LmlkID8gU3RyaW5nKHVzZXIuaWQpIDogJ2Fub24nO1xyXG5cdFx0Y29uc3Qga2V5ID0gYHRlbm86cHJlZnM6JHt1c2VySWR9YDtcclxuXHRcdGNvbnNvbGUuZGVidWcoJ1tQcmVmc10gc3RvcmFnZUtleSBjb21wdXRlZCcsIHsgdXNlcklkLCBrZXkgfSk7XHJcblx0XHRyZXR1cm4ga2V5O1xyXG5cdH0sIFt1c2VyPy5pZF0pO1xyXG5cclxuXHQvLyBIeWRyYXRlIGZyb20gbG9jYWxTdG9yYWdlIG9uIG1vdW50IG9yIHdoZW4gdXNlciBjaGFuZ2VzXHJcblx0dXNlRWZmZWN0KCgpID0+IHtcclxuXHRcdHRyeSB7XHJcblx0XHRcdGNvbnN0IHJhdyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHN0b3JhZ2VLZXkpO1xyXG5cdFx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIGh5ZHJhdGUgc3RhcnQnLCB7IHN0b3JhZ2VLZXksIHJhdyB9KTtcclxuXHRcdFx0aWYgKHJhdykge1xyXG5cdFx0XHRcdGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UocmF3KSBhcyBQZXJzaXN0ZWRQcmVmcztcclxuXHRcdFx0XHRjb25zdCBuZXh0TGFuZ3VhZ2UgPSBwYXJzZWQubGFuZ3VhZ2UgfHwgZGVmYXVsdHMubGFuZ3VhZ2U7XHJcblx0XHRcdFx0Y29uc3QgbmV4dEN1cnJlbmN5ID0gcGFyc2VkLmN1cnJlbmN5IHx8IGRlZmF1bHRzLmN1cnJlbmN5O1xyXG5cdFx0XHRcdGNvbnN0IG5leHRDb3VudHJ5ID0gcGFyc2VkLmNvdW50cnkgfHwgZGVmYXVsdHMuY291bnRyeTtcclxuXHRcdFx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIGh5ZHJhdGUgcGFyc2VkJywgeyBwYXJzZWQsIG5leHRMYW5ndWFnZSwgbmV4dEN1cnJlbmN5LCBuZXh0Q291bnRyeSB9KTtcclxuXHRcdFx0XHRzZXRMYW5ndWFnZVN0YXRlKG5leHRMYW5ndWFnZSk7XHJcblx0XHRcdFx0c2V0Q3VycmVuY3lTdGF0ZShuZXh0Q3VycmVuY3kpO1xyXG5cdFx0XHRcdHNldENvdW50cnlTdGF0ZShuZXh0Q291bnRyeSk7XHJcblx0XHRcdH0gZWxzZSB7XHJcblx0XHRcdFx0Y29uc29sZS5kZWJ1ZygnW1ByZWZzXSBoeWRyYXRlIG5vIGV4aXN0aW5nLCB1c2luZyBkZWZhdWx0cycsIGRlZmF1bHRzKTtcclxuXHRcdFx0XHRzZXRMYW5ndWFnZVN0YXRlKGRlZmF1bHRzLmxhbmd1YWdlKTtcclxuXHRcdFx0XHRzZXRDdXJyZW5jeVN0YXRlKGRlZmF1bHRzLmN1cnJlbmN5KTtcclxuXHRcdFx0XHRzZXRDb3VudHJ5U3RhdGUoZGVmYXVsdHMuY291bnRyeSk7XHJcblx0XHRcdH1cclxuXHRcdH0gY2F0Y2gge1xyXG5cdFx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIGh5ZHJhdGUgZXJyb3IsIGZhbGxpbmcgYmFjayB0byBkZWZhdWx0cycsIGRlZmF1bHRzKTtcclxuXHRcdFx0c2V0TGFuZ3VhZ2VTdGF0ZShkZWZhdWx0cy5sYW5ndWFnZSk7XHJcblx0XHRcdHNldEN1cnJlbmN5U3RhdGUoZGVmYXVsdHMuY3VycmVuY3kpO1xyXG5cdFx0XHRzZXRDb3VudHJ5U3RhdGUoZGVmYXVsdHMuY291bnRyeSk7XHJcblx0XHR9XHJcblx0XHQvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXHJcblx0fSwgW3N0b3JhZ2VLZXldKTtcclxuXHJcblx0Ly8gUmUtaHlkcmF0ZSBvbiBhdXRoIGNoYW5nZXMgKGxvZ2luL2xvZ291dCkgc2luY2Uga2V5IHNjb3BlIGNoYW5nZXNcclxuXHR1c2VFZmZlY3QoKCkgPT4ge1xyXG5cdFx0Y29uc3Qgb25BdXRoQ2hhbmdlID0gKCkgPT4ge1xyXG5cdFx0XHR0cnkge1xyXG5cdFx0XHRcdGNvbnN0IHJhdyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHN0b3JhZ2VLZXkpO1xyXG5cdFx0XHRcdGlmIChyYXcpIHtcclxuXHRcdFx0XHRcdGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UocmF3KSBhcyBQZXJzaXN0ZWRQcmVmcztcclxuXHRcdFx0XHRcdHNldExhbmd1YWdlU3RhdGUocGFyc2VkLmxhbmd1YWdlIHx8IGRlZmF1bHRzLmxhbmd1YWdlKTtcclxuXHRcdFx0XHRcdHNldEN1cnJlbmN5U3RhdGUocGFyc2VkLmN1cnJlbmN5IHx8IGRlZmF1bHRzLmN1cnJlbmN5KTtcclxuXHRcdFx0XHRcdHNldENvdW50cnlTdGF0ZShwYXJzZWQuY291bnRyeSB8fCBkZWZhdWx0cy5jb3VudHJ5KTtcclxuXHRcdFx0XHR9IGVsc2Uge1xyXG5cdFx0XHRcdFx0c2V0TGFuZ3VhZ2VTdGF0ZShkZWZhdWx0cy5sYW5ndWFnZSk7XHJcblx0XHRcdFx0XHRzZXRDdXJyZW5jeVN0YXRlKGRlZmF1bHRzLmN1cnJlbmN5KTtcclxuXHRcdFx0XHRcdHNldENvdW50cnlTdGF0ZShkZWZhdWx0cy5jb3VudHJ5KTtcclxuXHRcdFx0XHR9XHJcblx0XHRcdH0gY2F0Y2gge1xyXG5cdFx0XHRcdHNldExhbmd1YWdlU3RhdGUoZGVmYXVsdHMubGFuZ3VhZ2UpO1xyXG5cdFx0XHRcdHNldEN1cnJlbmN5U3RhdGUoZGVmYXVsdHMuY3VycmVuY3kpO1xyXG5cdFx0XHRcdHNldENvdW50cnlTdGF0ZShkZWZhdWx0cy5jb3VudHJ5KTtcclxuXHRcdFx0fVxyXG5cdFx0fTtcclxuXHRcdGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG5cdFx0XHR3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigndGVubzphdXRoOmNoYW5nZScsIG9uQXV0aENoYW5nZSBhcyBFdmVudExpc3RlbmVyKTtcclxuXHRcdH1cclxuXHRcdHJldHVybiAoKSA9PiB7XHJcblx0XHRcdGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG5cdFx0XHRcdHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCd0ZW5vOmF1dGg6Y2hhbmdlJywgb25BdXRoQ2hhbmdlIGFzIEV2ZW50TGlzdGVuZXIpO1xyXG5cdFx0XHR9XHJcblx0XHR9O1xyXG5cdH0sIFtzdG9yYWdlS2V5LCBkZWZhdWx0cy5sYW5ndWFnZSwgZGVmYXVsdHMuY3VycmVuY3ksIGRlZmF1bHRzLmNvdW50cnldKTtcclxuXHJcblx0Ly8gSWYgYSB1c2VyIGlzIHByZXNlbnQsIGZldGNoIHNlcnZlci1zaWRlIHByZWZlcmVuY2VzIGFuZCBhcHBseVxyXG5cdHVzZUVmZmVjdCgoKSA9PiB7XHJcblx0XHRjb25zdCB1c2VySWQgPSB1c2VyPy5pZCA/IFN0cmluZyh1c2VyLmlkKSA6IG51bGw7XHJcblx0XHRpZiAoIXVzZXJJZCkgcmV0dXJuO1xyXG5cdFx0bGV0IGFib3J0ZWQgPSBmYWxzZTtcclxuXHRcdChhc3luYyAoKSA9PiB7XHJcblx0XHRcdHRyeSB7XHJcblx0XHRcdFx0ICAgIGNvbnN0IHVybCA9IGAke2dldEJhY2tlbmRVcmwoKX0vdXNlcnMvJHtlbmNvZGVVUklDb21wb25lbnQodXNlcklkKX1gO1xyXG5cdFx0XHRcdGNvbnNvbGUuZGVidWcoJ1tQcmVmc10gZmV0Y2hpbmcgc2VydmVyIHByZWZlcmVuY2VzJywgeyB1c2VySWQsIHVybCB9KTtcclxuXHRcdFx0XHRjb25zdCByZXNwID0gYXdhaXQgZmV0Y2hXaXRoQ3JlZGVudGlhbHModXJsKTtcclxuXHRcdFx0XHRpZiAoIXJlc3Aub2spIHJldHVybjtcclxuXHRcdFx0XHRjb25zdCBwYXlsb2FkID0gYXdhaXQgcmVzcC5qc29uKCk7XHJcblx0XHRcdFx0aWYgKGFib3J0ZWQgfHwgIXBheWxvYWQpIHJldHVybjtcclxuXHRcdFx0XHRjb25zdCBuZXh0TGFuZ3VhZ2UgPSAocGF5bG9hZC5wcmVmZXJyZWRMYW5ndWFnZSBhcyBTdXBwb3J0ZWRMYW5ndWFnZSkgfHwgZGVmYXVsdHMubGFuZ3VhZ2U7XHJcblx0XHRcdFx0Y29uc3QgbmV4dEN1cnJlbmN5ID0gKHBheWxvYWQucHJlZmVycmVkQ3VycmVuY3kgYXMgU3VwcG9ydGVkQ3VycmVuY3kpIHx8IGRlZmF1bHRzLmN1cnJlbmN5O1xyXG5cdFx0XHRcdGNvbnN0IG5leHRDb3VudHJ5ID0gKHBheWxvYWQuY291bnRyeUNvZGUgYXMgQ291bnRyeUNvZGUpIHx8IGRlZmF1bHRzLmNvdW50cnk7XHJcblx0XHRcdFx0Y29uc29sZS5kZWJ1ZygnW1ByZWZzXSBzZXJ2ZXIgcHJlZmVyZW5jZXMgcmVjZWl2ZWQnLCB7IG5leHRMYW5ndWFnZSwgbmV4dEN1cnJlbmN5LCBuZXh0Q291bnRyeSB9KTtcclxuXHRcdFx0XHRzZXRMYW5ndWFnZVN0YXRlKG5leHRMYW5ndWFnZSk7XHJcblx0XHRcdFx0c2V0Q3VycmVuY3lTdGF0ZShuZXh0Q3VycmVuY3kpO1xyXG5cdFx0XHRcdHNldENvdW50cnlTdGF0ZShuZXh0Q291bnRyeSk7XHJcblx0XHRcdFx0dHJ5IHtcclxuXHRcdFx0XHRcdGxvY2FsU3RvcmFnZS5zZXRJdGVtKHN0b3JhZ2VLZXksIEpTT04uc3RyaW5naWZ5KHsgbGFuZ3VhZ2U6IG5leHRMYW5ndWFnZSwgY3VycmVuY3k6IG5leHRDdXJyZW5jeSwgY291bnRyeTogbmV4dENvdW50cnkgfSkpO1xyXG5cdFx0XHRcdH0gY2F0Y2gge31cclxuXHRcdFx0fSBjYXRjaCB7fVxyXG5cdFx0fSkoKTtcclxuXHRcdHJldHVybiAoKSA9PiB7IGFib3J0ZWQgPSB0cnVlOyB9O1xyXG5cdH0sIFt1c2VyPy5pZCwgc3RvcmFnZUtleSwgZGVmYXVsdHMubGFuZ3VhZ2UsIGRlZmF1bHRzLmN1cnJlbmN5LCBkZWZhdWx0cy5jb3VudHJ5XSk7XHJcblxyXG5cdGNvbnN0IHBlcnNpc3QgPSB1c2VDYWxsYmFjaygobmV4dDogUGVyc2lzdGVkUHJlZnMpID0+IHtcclxuXHRcdHRyeSB7XHJcblx0XHRcdGNvbnN0IGN1cnJlbnQ6IFBlcnNpc3RlZFByZWZzID0ge1xyXG5cdFx0XHRcdGxhbmd1YWdlLFxyXG5cdFx0XHRcdGN1cnJlbmN5LFxyXG5cdFx0XHRcdGNvdW50cnksXHJcblx0XHRcdH07XHJcblx0XHRcdGNvbnN0IG1lcmdlZCA9IHsgLi4uY3VycmVudCwgLi4ubmV4dCB9O1xyXG5cdFx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIHBlcnNpc3QnLCB7IHN0b3JhZ2VLZXksIGN1cnJlbnQsIG5leHQsIG1lcmdlZCB9KTtcclxuXHRcdFx0bG9jYWxTdG9yYWdlLnNldEl0ZW0oc3RvcmFnZUtleSwgSlNPTi5zdHJpbmdpZnkobWVyZ2VkKSk7XHJcblx0XHR9IGNhdGNoIHt9XHJcblx0fSwgW2xhbmd1YWdlLCBjdXJyZW5jeSwgY291bnRyeSwgc3RvcmFnZUtleV0pO1xyXG5cclxuXHRjb25zdCBzZXRMYW5ndWFnZSA9IHVzZUNhbGxiYWNrKChsbmc6IFN1cHBvcnRlZExhbmd1YWdlKSA9PiB7XHJcblx0XHRzZXRMYW5ndWFnZVN0YXRlKGxuZyk7XHJcblx0XHRwZXJzaXN0KHsgbGFuZ3VhZ2U6IGxuZyB9KTtcclxuXHR9LCBbcGVyc2lzdF0pO1xyXG5cclxuXHRjb25zdCBzZXRDdXJyZW5jeSA9IHVzZUNhbGxiYWNrKChjdXI6IFN1cHBvcnRlZEN1cnJlbmN5KSA9PiB7XHJcblx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIHNldEN1cnJlbmN5JywgeyBmcm9tOiBjdXJyZW5jeSwgdG86IGN1ciB9KTtcclxuXHRcdHNldEN1cnJlbmN5U3RhdGUoY3VyKTtcclxuXHRcdHBlcnNpc3QoeyBjdXJyZW5jeTogY3VyIH0pO1xyXG5cdH0sIFtwZXJzaXN0LCBjdXJyZW5jeV0pO1xyXG5cclxuXHRjb25zdCBzZXRDb3VudHJ5ID0gdXNlQ2FsbGJhY2soKGNjOiBDb3VudHJ5Q29kZSkgPT4ge1xyXG5cdFx0c2V0Q291bnRyeVN0YXRlKGNjKTtcclxuXHRcdHBlcnNpc3QoeyBjb3VudHJ5OiBjYyB9KTtcclxuXHR9LCBbcGVyc2lzdF0pO1xyXG5cclxuXHRjb25zdCB2YWx1ZSA9IHVzZU1lbW88UHJlZmVyZW5jZXNDb250ZXh0VmFsdWU+KCgpID0+ICh7XHJcblx0XHRsYW5ndWFnZSxcclxuXHRcdGN1cnJlbmN5LFxyXG5cdFx0Y291bnRyeSxcclxuXHRcdHNldExhbmd1YWdlLFxyXG5cdFx0c2V0Q3VycmVuY3ksXHJcblx0XHRzZXRDb3VudHJ5LFxyXG5cdH0pLCBbbGFuZ3VhZ2UsIGN1cnJlbmN5LCBjb3VudHJ5LCBzZXRMYW5ndWFnZSwgc2V0Q3VycmVuY3ksIHNldENvdW50cnldKTtcclxuXHJcblx0cmV0dXJuIChcclxuXHRcdDxQcmVmZXJlbmNlc0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cclxuXHRcdFx0e2NoaWxkcmVufVxyXG5cdFx0PC9QcmVmZXJlbmNlc0NvbnRleHQuUHJvdmlkZXI+XHJcblx0KTtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHVzZVByZWZlcmVuY2VzKCk6IFByZWZlcmVuY2VzQ29udGV4dFZhbHVlIHtcclxuXHRjb25zdCBjdHggPSB1c2VDb250ZXh0KFByZWZlcmVuY2VzQ29udGV4dCk7XHJcblx0aWYgKCFjdHgpIHtcclxuXHRcdHRocm93IG5ldyBFcnJvcigndXNlUHJlZmVyZW5jZXMgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFByZWZlcmVuY2VzUHJvdmlkZXInKTtcclxuXHR9XHJcblx0cmV0dXJuIGN0eDtcclxufVxyXG5cclxuXHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ2FsbGJhY2siLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlTWVtbyIsInVzZVN0YXRlIiwidXNlQXV0aCIsImdldEJyb3dzZXJQcmVmZXJlbmNlRGVmYXVsdHMiLCJmZXRjaFdpdGhDcmVkZW50aWFscyIsImdldEJhY2tlbmRVcmwiLCJQcmVmZXJlbmNlc0NvbnRleHQiLCJ1bmRlZmluZWQiLCJnZXRCcm93c2VyRGVmYXVsdHMiLCJsYW5ndWFnZSIsImNvdW50cnkiLCJjdXJyZW5jeSIsIlByZWZlcmVuY2VzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJkZWZhdWx0cyIsInNldExhbmd1YWdlU3RhdGUiLCJzZXRDdXJyZW5jeVN0YXRlIiwic2V0Q291bnRyeVN0YXRlIiwic3RvcmFnZUtleSIsInVzZXJJZCIsImlkIiwiU3RyaW5nIiwia2V5IiwiY29uc29sZSIsImRlYnVnIiwicmF3IiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInBhcnNlZCIsIkpTT04iLCJwYXJzZSIsIm5leHRMYW5ndWFnZSIsIm5leHRDdXJyZW5jeSIsIm5leHRDb3VudHJ5Iiwib25BdXRoQ2hhbmdlIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJhYm9ydGVkIiwidXJsIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwicmVzcCIsIm9rIiwicGF5bG9hZCIsImpzb24iLCJwcmVmZXJyZWRMYW5ndWFnZSIsInByZWZlcnJlZEN1cnJlbmN5IiwiY291bnRyeUNvZGUiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwicGVyc2lzdCIsIm5leHQiLCJjdXJyZW50IiwibWVyZ2VkIiwic2V0TGFuZ3VhZ2UiLCJsbmciLCJzZXRDdXJyZW5jeSIsImN1ciIsImZyb20iLCJ0byIsInNldENvdW50cnkiLCJjYyIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VQcmVmZXJlbmNlcyIsImN0eCIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/context/PreferencesContext.tsx\n"));

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/EntityTable.fadein.css */ \"./src/components/EntityTable.fadein.css\");\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../context/PreferencesContext */ \"./src/context/PreferencesContext.tsx\");\n/* harmony import */ var _utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/useAuthGuard */ \"./src/utils/useAuthGuard.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction MyApp(param) {\n    let { Component, pageProps } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_StoreContext__WEBPACK_IMPORTED_MODULE_4__.StoreProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__.PreferencesProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GuardedApp, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                        ...pageProps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = MyApp;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MyApp);\nfunction GuardedApp(param) {\n    let { children } = param;\n    _s();\n    // Run global auth guard on every route navigation\n    (0,_utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__.useAuthGuard)({\n        publicPaths: [\n            \"/\",\n            \"/login\",\n            \"/_error\",\n            \"/setup/store\",\n            \"/store/[storeUuid]\",\n            \"/storefront\",\n            \"/live/[uuid]\"\n        ]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(GuardedApp, \"TitGtyjW32Onfz5lscu0IFsEfl4=\", false, function() {\n    return [\n        _utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__.useAuthGuard\n    ];\n});\n_c1 = GuardedApp;\nvar _c, _c1;\n$RefreshReg$(_c, \"MyApp\");\n$RefreshReg$(_c1, \"GuardedApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n"));

/***/ })

});