import { useState, useEffect, useCallback } from 'react';
import { storeApi, productApi, customerApi, orderApi, conversationApi, agentApi } from './api';

// Generic hook for data fetching with loading and error states
export function useApiQuery<T>(
  queryFn: () => Promise<T>,
  dependencies: any[] = [],
  enabled: boolean = true
) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const refetch = useCallback(async () => {
    if (!enabled) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await queryFn();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, [queryFn, enabled]);

  useEffect(() => {
    refetch();
  }, dependencies);

  return { data, isLoading, error, refetch };
}

// Generic hook for mutations
export function useApiMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>
) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<TData | null>(null);

  const mutate = useCallback(async (variables: TVariables) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await mutationFn(variables);
      setData(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [mutationFn]);

  return { mutate, isLoading, error, data };
}

// Store hooks
export function useStores(params?: { page?: number; limit?: number; userId?: string | number }) {
  return useApiQuery(
    () => storeApi.getAll(params),
    [params?.page, params?.limit, params?.userId],
    !!params?.userId
  );
}

export function useStoreById(id: string | number | null) {
  return useApiQuery(
    () => storeApi.getById(id!),
    [id],
    !!id
  );
}

export function useStoresByUserId(userId: string | number | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => storeApi.getByUserId(userId!, params),
    [userId, params?.page, params?.limit],
    !!userId
  );
}

export function useStoreByUuid(uuid: string | null) {
  return useApiQuery(
    () => storeApi.getByUuid(uuid!),
    [uuid],
    !!uuid
  );
}

export function useCreateStore() {
  return useApiMutation(storeApi.create);
}

export function useUpdateStore() {
  return useApiMutation(({ id, data }: { id: string | number; data: any }) => storeApi.update(id, data));
}

export function useDeleteStore() {
  return useApiMutation(storeApi.delete);
}

// Product hooks
export function useProducts(params?: { page?: number; limit?: number; storeId?: string | number }) {
  return useApiQuery(
    () => productApi.getAll(params),
    [params?.page, params?.limit, params?.storeId],
    !!params?.storeId
  );
}

export function useProductById(id: string | number | null) {
  return useApiQuery(
    () => productApi.getById(id!),
    [id],
    !!id
  );
}

export function useProductsByStoreId(storeId: string | number | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => productApi.getByStoreId(storeId!, params),
    [storeId, params?.page, params?.limit],
    !!storeId
  );
}

export function useProductsByStoreUuid(storeUuid: string | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => productApi.getByStoreUuid(storeUuid!, params),
    [storeUuid, params?.page, params?.limit],
    !!storeUuid
  );
}

export function useCreateProduct() {
  return useApiMutation(productApi.create);
}

export function useUpdateProduct() {
  return useApiMutation(({ id, data }: { id: string | number; data: any }) => productApi.update(id, data));
}

export function useDeleteProduct() {
  return useApiMutation(productApi.delete);
}

// Customer hooks
export function useCustomers(params?: { page?: number; limit?: number; storeId?: string | number }) {
  return useApiQuery(
    () => customerApi.getAll(params),
    [params?.page, params?.limit, params?.storeId],
    !!params?.storeId
  );
}

export function useCustomerById(id: string | number | null) {
  return useApiQuery(
    () => customerApi.getById(id!),
    [id],
    !!id
  );
}

export function useCustomersByStoreId(storeId: string | number | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => customerApi.getByStoreId(storeId!, params),
    [storeId, params?.page, params?.limit],
    !!storeId
  );
}

export function useCreateCustomer() {
  return useApiMutation(customerApi.create);
}

export function useUpdateCustomer() {
  return useApiMutation(({ id, data }: { id: string | number; data: any }) => customerApi.update(id, data));
}

export function useDeleteCustomer() {
  return useApiMutation(customerApi.delete);
}

// Order hooks
export function useOrders(params?: { page?: number; limit?: number; storeId?: string | number }) {
  return useApiQuery(
    () => orderApi.getAll(params),
    [params?.page, params?.limit, params?.storeId],
    !!params?.storeId
  );
}

export function useOrderById(id: string | number | null) {
  return useApiQuery(
    () => orderApi.getById(id!),
    [id],
    !!id
  );
}

export function useOrdersByStoreId(storeId: string | number | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => orderApi.getByStoreId(storeId!, params),
    [storeId, params?.page, params?.limit],
    !!storeId
  );
}

export function useFilterOrders() {
  return useApiMutation(orderApi.filter);
}

export function useCreateOrder() {
  return useApiMutation(orderApi.create);
}

export function useUpdateOrder() {
  return useApiMutation(({ id, data }: { id: string | number; data: any }) => orderApi.update(id, data));
}

export function useDeleteOrder() {
  return useApiMutation(orderApi.delete);
}

// Conversation hooks
export function useConversations(params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => conversationApi.getAll(params),
    [params?.page, params?.limit],
    true
  );
}

export function useConversationById(id: string | number | null) {
  return useApiQuery(
    () => conversationApi.getById(id!),
    [id],
    !!id
  );
}

export function useConversationByUuid(uuid: string | null) {
  return useApiQuery(
    () => conversationApi.getByUuid(uuid!),
    [uuid],
    !!uuid
  );
}

export function useConversationsByStoreId(storeId: string | number | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => conversationApi.getByStoreId(storeId!, params),
    [storeId, params?.page, params?.limit],
    !!storeId
  );
}

export function useConversationsByUserId(userId: string | number | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => conversationApi.getByUserId(userId!, params),
    [userId, params?.page, params?.limit],
    !!userId
  );
}

export function useCreateConversation() {
  return useApiMutation(conversationApi.create);
}

export function useConversationTimeline(id: string | number | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => conversationApi.getTimeline(id!, params),
    [id, params?.page, params?.limit],
    !!id
  );
}

export function useConversationUnifiedTimeline(id: string | number | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => conversationApi.getUnifiedTimeline(id!, params),
    [id, params?.page, params?.limit],
    !!id
  );
}

export function useConversationUnifiedTimelineByUuid(uuid: string | null, params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => conversationApi.getUnifiedTimelineByUuid(uuid!, params),
    [uuid, params?.page, params?.limit],
    !!uuid
  );
}

export function useAppendMessage() {
  return useApiMutation(({ id, data }: { id: string | number; data: any }) => conversationApi.appendMessage(id, data));
}

export function useAppendMessageByUuid() {
  return useApiMutation(({ uuid, data }: { uuid: string; data: any }) => conversationApi.appendMessageByUuid(uuid, data));
}

// Agent hooks
export function useAgents(params?: { page?: number; limit?: number }) {
  return useApiQuery(
    () => agentApi.getAll(params),
    [params?.page, params?.limit],
    true
  );
}

export function useAgentById(id: string | number | null) {
  return useApiQuery(
    () => agentApi.getById(id!),
    [id],
    !!id
  );
}

export function useCreateAgent() {
  return useApiMutation(agentApi.create);
}

export function useUpdateAgent() {
  return useApiMutation(({ id, data }: { id: string | number; data: any }) => agentApi.update(id, data));
}

export function useDeleteAgent() {
  return useApiMutation(agentApi.delete);
}

export function useGenerateAgentMessage() {
  return useApiMutation(agentApi.generateMessage);
}

/**
 * Hook for F1 keyboard shortcut to open "Add New" modals
 * Follows UI guidelines for keyboard shortcuts
 */
export function useAddEntityShortcut(isEnabled: boolean, onOpen: () => void) {
  useEffect(() => {
    if (!isEnabled) return;

    const handler = (event: KeyboardEvent) => {
      if (event.key !== 'F1') return;

      const target = event.target as HTMLElement | null;
      const tag = target?.tagName?.toLowerCase();
      const isFormField = tag === 'input' || tag === 'textarea' || tag === 'select' || (target?.isContentEditable ?? false);
      if (isFormField) return;

      event.preventDefault();
      onOpen();
    };

    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [isEnabled, onOpen]);
}
