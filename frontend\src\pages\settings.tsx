import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../context/AuthContext';
import { userApi } from '../utils/api';
import { useAuthGuard } from '../utils/useAuthGuard';
import { CURRENCY_OPTIONS, LANGUAGE_OPTIONS } from '../utils/preferences';

export default function SettingsPage() {
  useAuthGuard();
  const router = useRouter();
  const { user, refresh } = useAuth();

  const userId = useMemo(() => (user?.id ? String(user.id) : null), [user?.id]);
  const [userData, setUserData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [form, setForm] = useState({
    name: '',
    preferredLanguage: '',
    preferredCurrency: '',
  });

  const [touched, setTouched] = useState(false);

  // Enums (dropdown options) centralized in utils/preferences

  const isLoadingData = isLoading || !userId;

  const onLoad = userData;
  const countryCode = onLoad?.countryCode || '';

  // Fetch user data
  const fetchUserData = async () => {
    if (!userId) return;
    
    setIsLoading(true);
    setError(null);
    try {
      const result = await userApi.getById(userId);
      if (result && typeof result === 'object' && 'data' in result) {
        setUserData(result.data || result);
      } else {
        setUserData(result);
      }
    } catch (err) {
      setError('Failed to fetch user data');
      console.error('Error fetching user data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize form when data loads
  useEffect(() => {
    if (!isLoadingData && !touched && onLoad) {
      setForm({
        name: onLoad.name || '',
        preferredLanguage: onLoad.preferredLanguage || LANGUAGE_OPTIONS[0],
        preferredCurrency: onLoad.preferredCurrency || CURRENCY_OPTIONS[0],
      });
      setTouched(true);
    }
  }, [isLoadingData, touched, onLoad]);

  // Fetch user data when component mounts
  useEffect(() => {
    fetchUserData();
  }, [userId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userId) return;
    
    try {
      await userApi.update(userId, {
        name: form.name || undefined,
        preferredLanguage: form.preferredLanguage || undefined,
        preferredCurrency: form.preferredCurrency || undefined,
        updatedBy: userId,
      });
      await fetchUserData();
      await refresh();
    } catch (err) {
      setError('Failed to update user settings');
      console.error('Error updating user:', err);
    }
  };

  return (
    <div className="min-h-screen bg-slate-950 text-slate-100">
      <div className="max-w-3xl mx-auto px-4 py-10">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-semibold">User Settings</h1>
          <button onClick={() => router.back()} className="px-3 py-1.5 rounded-lg bg-slate-800 hover:bg-slate-700">Back</button>
        </div>

        <div className="bg-slate-900 border border-slate-800 rounded-2xl p-6">
          {isLoading ? (
            <div className="animate-pulse h-40 bg-slate-800 rounded-xl" />
          ) : (
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label className="block text-sm mb-2">Name</label>
                <input
                  type="text"
                  value={form.name}
                  onChange={(e) => setForm((p) => ({ ...p, name: e.target.value }))}
                  className="w-full px-4 py-2 rounded-lg bg-slate-800 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-emerald-500"
                  placeholder="Your display name"
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm mb-2">Preferred Language</label>
                  <select
                    value={form.preferredLanguage}
                    onChange={(e) => setForm((p) => ({ ...p, preferredLanguage: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg bg-slate-800 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-emerald-500"
                  >
                    {LANGUAGE_OPTIONS.map((opt) => (
                      <option key={opt} value={opt}>{opt}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm mb-2">Preferred Currency</label>
                  <select
                    value={form.preferredCurrency}
                    onChange={(e) => setForm((p) => ({ ...p, preferredCurrency: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg bg-slate-800 border border-slate-700 focus:outline-none focus:ring-2 focus:ring-emerald-500"
                  >
                    {CURRENCY_OPTIONS.map((opt) => (
                      <option key={opt} value={opt}>{opt}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm mb-2">Country</label>
                <input
                  type="text"
                  value={countryCode}
                  readOnly
                  className="w-full px-4 py-2 rounded-lg bg-slate-800 border border-slate-700 text-slate-400 cursor-not-allowed"
                />
                <p className="text-xs text-slate-500 mt-1">Country is set from your account and cannot be changed.</p>
              </div>

              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={() => fetchUserData()}
                  className="px-4 py-2 rounded-lg border border-slate-700 hover:bg-slate-800"
                >
                  Reset
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-4 py-2 rounded-lg bg-emerald-600 hover:bg-emerald-500 disabled:opacity-60"
                >
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}


