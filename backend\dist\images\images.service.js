"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImagesService = void 0;
const common_1 = require("@nestjs/common");
const sharp = require("sharp");
const path = require("path");
const fs = require("fs/promises");
let ImagesService = class ImagesService {
    async processImage(file) {
        const filename = `${Date.now()}-${file.originalname}`;
        const uploadPath = path.join(process.cwd(), 'uploads', filename);
        await fs.mkdir(path.dirname(uploadPath), { recursive: true });
        await sharp(file.buffer)
            .resize(800, 800, { fit: 'inside', withoutEnlargement: true })
            .jpeg({ quality: 80 })
            .toFile(uploadPath);
        return filename;
    }
    async getImage(filename) {
        const imagePath = path.join(process.cwd(), 'uploads', filename);
        try {
            return await fs.readFile(imagePath);
        }
        catch (error) {
            throw new Error('Image not found');
        }
    }
    async deleteImage(filename) {
        const imagePath = path.join(process.cwd(), 'uploads', filename);
        try {
            await fs.unlink(imagePath);
        }
        catch (error) {
        }
    }
};
exports.ImagesService = ImagesService;
exports.ImagesService = ImagesService = __decorate([
    (0, common_1.Injectable)()
], ImagesService);
//# sourceMappingURL=images.service.js.map