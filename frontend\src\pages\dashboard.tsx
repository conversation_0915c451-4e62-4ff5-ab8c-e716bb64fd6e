import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../context/AuthContext';
import { useStore } from '../context/StoreContext';
import { storeApi } from '../utils/api';
import TopTaskBar from '../components/TopTaskBar';
import SideTaskBar from '../components/SideTaskBar';

export default function Dashboard() {
  const { user, isLoading, error, refresh, logout } = useAuth();
  const { autoSelectFirstStore } = useStore();
  const router = useRouter();
  const [storesData, setStoresData] = useState<any>(null);
  const [storesLoading, setStoresLoading] = useState(false);
  const [hasCheckedStores, setHasCheckedStores] = useState(false);

  useEffect(() => {
    // Check for auth success
    if (router.query.auth === 'success') {
      // Remove the query param from URL
      router.replace('/dashboard', undefined, { shallow: true });
    }

    // Fetch user data
    refresh();
  }, [router.query.auth, refresh]);

  // Fetch stores when user is available
  useEffect(() => {
    if (user?.id) {
      console.log('Fetching stores for user:', user.id);
      setStoresLoading(true);
      storeApi.getByUserId(user.id, { page: 1, limit: 100 })
        .then((data) => {
          console.log('Stores data received:', data);
          setStoresData(data);

          // Auto-select the first store if available
          if ((data as any)?.data?.length > 0) {
            autoSelectFirstStore((data as any).data);
          }

          setHasCheckedStores(true);
        })
        .catch((error) => {
          console.error('Failed to fetch stores:', error);
          setHasCheckedStores(true);
        })
        .finally(() => {
          setStoresLoading(false);
        });
    }
  }, [user?.id]);

  // Fallback: if authenticated here and have zero stores, send to setup
  // Only redirect if we've actually checked for stores and found none
  // AND we're not coming from store creation
  useEffect(() => {
    if (!isLoading && user && !storesLoading && hasCheckedStores) {
      const totalStores = storesData?.meta?.total ?? storesData?.data?.length ?? 0;
      const isFromStoreCreation = router.query.from === 'store-creation';
      const hasStoreCreationFlag = (() => {
        try {
          return localStorage.getItem('teno:store-creation-complete') === 'true';
        } catch {
          return false;
        }
      })();
      
      console.log('Dashboard redirect check:', {
        totalStores,
        isFromStoreCreation,
        hasStoreCreationFlag,
        storesData,
        hasCheckedStores
      });
      
      // Only redirect to setup if user has no active stores
      if (totalStores === 0 && !isFromStoreCreation && !hasStoreCreationFlag) {
        console.log('Redirecting to setup store - no active stores found');
        router.replace('/setup/store');
      } else if (totalStores > 0) {
        console.log('Active stores found, staying on dashboard');
        // Clear the store creation flag since we have stores now
        try {
          localStorage.removeItem('teno:store-creation-complete');
        } catch {}
      } else if (isFromStoreCreation || hasStoreCreationFlag) {
        console.log('Coming from store creation, staying on dashboard');
        // Clear the store creation flag
        try {
          localStorage.removeItem('teno:store-creation-complete');
        } catch {}
      }
    }
  }, [isLoading, user, storesLoading, storesData, router, hasCheckedStores]);

  // Clean up the 'from' query parameter after processing
  useEffect(() => {
    if (router.query.from === 'store-creation') {
      // Remove the query param from URL to keep it clean
      router.replace('/dashboard', undefined, { shallow: true });
    }
  }, [router.query.from, router]);

  // Clean up localStorage flag when user changes
  useEffect(() => {
    return () => {
      try {
        localStorage.removeItem('teno:store-creation-complete');
      } catch {}
    };
  }, [user?.id]);

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Authentication Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={handleLogout}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Logout
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push('/login');
    return null;
  }

  return (
    <>
      <Head>
        <title>Dashboard - Teno Store</title>
        <meta name="description" content="Manage your stores and business" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        <TopTaskBar />
        
        <div className="flex">
          <SideTaskBar />
          
          <main className="flex-1 p-6">
            <div className="max-w-7xl mx-auto">
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
                <p className="text-gray-600 mt-2">Welcome back, {user.name}!</p>
              </div>

              {storesLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                </div>
              ) : storesData?.data?.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {storesData.data.map((store: any) => (
                    <div
                      key={store.id}
                      className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer"
                      onClick={() => router.push(`/store/${store.id}`)}
                    >
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">{store.name}</h3>
                      <p className="text-gray-600 mb-4">{store.description || 'No description'}</p>
                      <div className="flex justify-between text-sm text-gray-500">
                        <span>Currency: {store.currency}</span>
                        <span>Language: {store.preferredLanguage}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-600 mb-4">No stores found. Let&apos;s create your first store!</p>
                  <button
                    onClick={() => router.push('/setup/store')}
                    className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors"
                  >
                    Create Store
                  </button>
                </div>
              )}
            </div>
          </main>
        </div>
      </div>
    </>
  );
}

