/**
 * Enhanced BigInt handling utilities for TypeORM and general serialization
 */

/**
 * Transforms BigInt values to strings in an object or array
 * This is the core function for handling BigInt serialization
 */
export function bigIntTransformer<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'bigint') {
    return obj.toString() as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(bigIntTransformer) as unknown as T;
  }

  if (typeof obj === 'object') {
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = bigIntTransformer(value);
    }
    return result;
  }

  return obj;
}

/**
 * Global BigInt handler that overrides JSON.stringify to handle BigInt values
 * This prevents "Do not know how to serialize a BigInt" errors globally
 */
export function setupGlobalBigIntHandler() {
  // Store the original JSON.stringify
  const originalStringify = JSON.stringify;
  
  // Override JSON.stringify with BigInt support
  JSON.stringify = function(value: any, replacer?: any, space?: any) {
    const bigIntReplacer = (key: string, value: any) => {
      if (typeof value === 'bigint') {
        return value.toString();
      }
      return value;
    };
    
    // Combine custom replacer with BigInt replacer if provided
    const finalReplacer = replacer ? 
      (key: string, value: any) => {
        const bigIntResult = bigIntReplacer(key, value);
        return replacer(key, bigIntResult);
      } : 
      bigIntReplacer;
    
    return originalStringify(value, finalReplacer, space);
  };

  console.log('Global BigInt handler setup complete');
}

/**
 * TypeORM-specific BigInt handler for query results
 * This can be used to wrap TypeORM query results before serialization
 */
export function handleTypeORMResult<T>(result: T): T {
  try {
    return bigIntTransformer(result);
  } catch (error) {
    console.warn('Error handling TypeORM result:', error);
    // If transformation fails, try to return the original result
    // This might still fail on JSON.stringify, but it's better than nothing
    return result;
  }
}

/**
 * Safe JSON stringify that handles BigInt values
 * Use this instead of JSON.stringify when you know you might have BigInt values
 */
export function safeJsonStringify(obj: any, space?: number | string): string {
  try {
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === 'bigint') {
        return value.toString();
      }
      return value;
    }, space);
  } catch (error) {
    console.error('Error in safe JSON stringify:', error);
    // Fallback: try to transform the object first
    try {
      const transformed = bigIntTransformer(obj);
      return JSON.stringify(transformed, null, space);
    } catch (fallbackError) {
      console.error('Fallback JSON stringify also failed:', fallbackError);
      throw new Error(`Failed to serialize object: ${error.message}`);
    }
  }
}

/**
 * Utility to check if an object contains BigInt values
 */
export function containsBigInt(obj: any): boolean {
  if (obj === null || obj === undefined) {
    return false;
  }

  if (typeof obj === 'bigint') {
    return true;
  }

  if (Array.isArray(obj)) {
    return obj.some(containsBigInt);
  }

  if (typeof obj === 'object') {
    return Object.values(obj).some(containsBigInt);
  }

  return false;
}

/**
 * Utility to safely clone objects while handling BigInt values
 */
export function safeClone<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'bigint') {
    return obj.toString() as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(safeClone) as unknown as T;
  }

  if (typeof obj === 'object') {
    const cloned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      cloned[key] = safeClone(value);
    }
    return cloned;
  }

  return obj;
}
