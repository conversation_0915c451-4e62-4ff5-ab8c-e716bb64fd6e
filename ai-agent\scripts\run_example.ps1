# Example PowerShell script to run the LLM API test
# This script shows how to set up the environment and run the test on Windows

Write-Host "🚀 Setting up environment for LLM API test..." -ForegroundColor Green

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "📝 Creating .env file from example..." -ForegroundColor Yellow
    Copy-Item "scripts/example.env" ".env"
    Write-Host "⚠️  Please edit .env file and add your actual API_KEY" -ForegroundColor Red
    Write-Host "   Then run this script again" -ForegroundColor Red
    exit 1
}

# Load environment variables from .env file
Write-Host "📖 Loading environment variables..." -ForegroundColor Green
Get-Content ".env" | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        $name = $matches[1].Trim()
        $value = $matches[2].Trim()
        if ($value -ne "your_openrouter_api_key_here") {
            [Environment]::SetEnvironmentVariable($name, $value, "Process")
        }
    }
}

# Check if API_KEY is set
$apiKey = [Environment]::GetEnvironmentVariable("API_KEY", "Process")
if (-not $apiKey -or $apiKey -eq "your_openrouter_api_key_here") {
    Write-Host "❌ API_KEY not set in .env file" -ForegroundColor Red
    Write-Host "   Please edit .env file and add your actual OpenRouter API key" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Environment setup complete!" -ForegroundColor Green
Write-Host "🔑 API Key: $($apiKey.Substring(0, [Math]::Min(10, $apiKey.Length)))..." -ForegroundColor Cyan
Write-Host "🌐 Base URL: $([Environment]::GetEnvironmentVariable('BASE_URL', 'Process'))" -ForegroundColor Cyan
Write-Host "🤖 Model: $([Environment]::GetEnvironmentVariable('MODEL', 'Process'))" -ForegroundColor Cyan
Write-Host ""

# Run the test
Write-Host "🧪 Running LLM API test..." -ForegroundColor Green
npm run test:llm

Write-Host ""
Write-Host "✅ Test completed!" -ForegroundColor Green
