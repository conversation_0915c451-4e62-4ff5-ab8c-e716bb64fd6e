"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSaleAgent = createSaleAgent;
const axios_1 = __importDefault(require("axios"));
const agent_1 = require("../src/agent");
const tools_1 = require("../src/tools");
/**
 * Tool for searching products
 */
const searchProducts = (0, tools_1.createTool)(async function searchProducts(query) {
    try {
        // This would typically connect to your backend API
        const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3000';
        const response = await axios_1.default.get(`${apiBaseUrl}/api/products/search`, {
            params: { q: query },
            timeout: 5000
        });
        return response.data;
    }
    catch (error) {
        console.error('Error searching products:', error);
        return [];
    }
}, {
    description: 'Search for products by name or description',
    parameterTypes: {
        query: String
    },
    requiredParams: ['query']
});
/**
 * Tool for getting product details
 */
const getProductDetails = (0, tools_1.createTool)(async function getProductDetails(productId) {
    try {
        const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3000';
        const response = await axios_1.default.get(`${apiBaseUrl}/api/products/${productId}`, {
            timeout: 5000
        });
        return response.data;
    }
    catch (error) {
        console.error('Error getting product details:', error);
        return null;
    }
}, {
    description: 'Get detailed information about a specific product',
    parameterTypes: {
        productId: String
    },
    requiredParams: ['productId']
});
/**
 * Tool for creating a quote
 */
const createQuote = (0, tools_1.createTool)(async function createQuote(customerId, productIds, quantities) {
    try {
        const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3000';
        const response = await axios_1.default.post(`${apiBaseUrl}/api/quotes`, {
            customerId,
            items: productIds.map((productId, index) => ({
                productId,
                quantity: quantities[index] || 1
            }))
        }, {
            timeout: 5000
        });
        return response.data;
    }
    catch (error) {
        console.error('Error creating quote:', error);
        throw new Error('Failed to create quote');
    }
}, {
    description: 'Create a sales quote for a customer with specified products and quantities',
    parameterTypes: {
        customerId: String,
        productIds: Array,
        quantities: Array
    },
    requiredParams: ['customerId', 'productIds', 'quantities']
});
/**
 * Tool for getting customer information
 */
const getCustomerInfo = (0, tools_1.createTool)(async function getCustomerInfo(customerId) {
    try {
        const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3000';
        const response = await axios_1.default.get(`${apiBaseUrl}/api/customers/${customerId}`, {
            timeout: 5000
        });
        return response.data;
    }
    catch (error) {
        console.error('Error getting customer info:', error);
        return null;
    }
}, {
    description: 'Get information about a customer by their ID',
    parameterTypes: {
        customerId: String
    },
    requiredParams: ['customerId']
});
/**
 * Create a sales agent with product and quote management capabilities
 */
function createSaleAgent() {
    return new agent_1.Agent('SaleAgent', `You are a professional sales assistant for Dido Distribution. You help customers find products, provide pricing information, and create quotes.
    
    Your capabilities:
    - Search for products in the inventory
    - Provide detailed product information and pricing
    - Create quotes for customers
    - Access customer information
    
    Guidelines:
    - Always be helpful and professional
    - Provide accurate product information and pricing
    - Suggest related or alternative products when appropriate
    - Confirm details before creating quotes
    - Ask for customer ID when creating quotes`, [searchProducts, getProductDetails, createQuote, getCustomerInfo], 'Sales assistant for product information and quote generation');
}
//# sourceMappingURL=sale-agent.js.map