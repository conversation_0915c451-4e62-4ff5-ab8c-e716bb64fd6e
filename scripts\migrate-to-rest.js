#!/usr/bin/env node

/**
 * Migration script to help identify and replace tRPC usage
 * Run with: node scripts/migrate-to-rest.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Scanning for tRPC usage...\n');

// Find all TypeScript/JavaScript files
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = [];
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath);
      } else if (extensions.includes(path.extname(item))) {
        files.push(fullPath);
      }
    }
  }
  
  scan(dir);
  return files;
}

// Search for tRPC patterns
function searchForTrpcPatterns(files) {
  const patterns = [
    {
      name: 'tRPC imports',
      regex: /import.*trpc.*from/gi,
      files: []
    },
    {
      name: 'tRPC usage',
      regex: /trpc\.[a-zA-Z]+\.[a-zA-Z]+/gi,
      files: []
    },
    {
      name: 'useQuery calls',
      regex: /\.useQuery\(/gi,
      files: []
    },
    {
      name: 'useMutation calls',
      regex: /\.useMutation\(/gi,
      files: []
    }
  ];

  for (const file of files) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      
      for (const pattern of patterns) {
        if (pattern.regex.test(content)) {
          pattern.files.push(file);
        }
      }
    } catch (error) {
      console.warn(`⚠️  Could not read ${file}:`, error.message);
    }
  }

  return patterns;
}

// Generate migration suggestions
function generateSuggestions(patterns) {
  console.log('📋 Migration Summary:\n');
  
  for (const pattern of patterns) {
    if (pattern.files.length > 0) {
      console.log(`🔴 ${pattern.name} found in ${pattern.files.length} files:`);
      pattern.files.forEach(file => {
        console.log(`   - ${file}`);
      });
      console.log('');
    }
  }

  console.log('📝 Migration Steps:\n');
  console.log('1. Replace tRPC imports with REST API imports:');
  console.log('   - import { trpc } from \'../utils/trpc\'');
  console.log('   + import { userApi, conversationApi, orderApi } from \'../utils/api\'');
  console.log('');
  
  console.log('2. Replace useQuery with useApiQuery:');
  console.log('   - const { data } = trpc.user.getById.useQuery({ id: userId })');
  console.log('   + const { data } = useApiQuery(() => userApi.getById(userId), [userId])');
  console.log('');
  
  console.log('3. Replace useMutation with useApiMutation:');
  console.log('   - const mutation = trpc.user.update.useMutation()');
  console.log('   + const { mutate, isLoading } = useApiMutation((data) => userApi.update(userId, data))');
  console.log('');
  
  console.log('4. Update function calls:');
  console.log('   - await mutation.mutateAsync(data)');
  console.log('   + mutate(data)');
  console.log('');
}

// Main execution
try {
  const frontendDir = path.join(__dirname, '..', 'frontend', 'src');
  const backendDir = path.join(__dirname, '..', 'backend', 'src');
  
  console.log('📁 Scanning frontend directory...');
  const frontendFiles = findFiles(frontendDir);
  console.log(`   Found ${frontendFiles.length} files\n`);
  
  console.log('📁 Scanning backend directory...');
  const backendFiles = findFiles(backendDir);
  console.log(`   Found ${backendFiles.length} files\n`);
  
  const allFiles = [...frontendFiles, ...backendFiles];
  const patterns = searchForTrpcPatterns(allFiles);
  
  generateSuggestions(patterns);
  
  console.log('✅ Migration script completed!');
  console.log('📚 See MIGRATION_GUIDE.md for detailed instructions.');
  
} catch (error) {
  console.error('❌ Error running migration script:', error.message);
  process.exit(1);
}
