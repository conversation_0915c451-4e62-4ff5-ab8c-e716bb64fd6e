import { OrderStatusService, OrderPriorityService } from '../../orders/orders.service';
import { Order } from '../../orders/order.entity';
export { OrderStatusService, OrderPriorityService };
export interface CreateOrderInput {
    status?: OrderStatusService;
    priority?: OrderPriorityService;
    useTax?: boolean;
    taxRate?: number;
    orderDate?: Date;
    expectedDeliveryDate?: Date | null;
    preferredDeliveryLocation?: string | null;
    userId: string;
    storeId: string | bigint;
    customerId: string;
    customerPhone?: string;
    customerEmail?: string;
    customerName?: string;
    customerAddress?: string;
    createdBy: string;
    items: Array<{
        productId: string;
        productName: string;
        quantity: number;
        unitPrice: number;
        lineTotal?: number;
        taxAmount?: number;
    }>;
}
export interface UpdateOrderInput {
    id: string;
    status?: OrderStatusService;
    priority?: OrderPriorityService;
    useTax?: boolean;
    taxRate?: number;
    orderDate?: Date;
    expectedDeliveryDate?: Date | null;
    preferredDeliveryLocation?: string | null;
    cancellationReason?: string | null;
    updatedBy: string;
    items?: Array<{
        productId: string;
        productName: string;
        quantity: number;
        unitPrice: number;
        lineTotal?: number;
        taxAmount?: number;
    }>;
}
export declare function createOrder(input: CreateOrderInput, db: any): Promise<Order>;
export declare function updateOrder(input: UpdateOrderInput, db: any): Promise<Order>;
export declare function checkOrderUnderwayByPhone(phone: string, db: any): Promise<Order | null>;
