"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/live/[uuid]",{

/***/ "./src/pages/live/[uuid].tsx":
/*!***********************************!*\
  !*** ./src/pages/live/[uuid].tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PublicLiveConversationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/TimelineItem */ \"./src/components/TimelineItem.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Icon Components\nconst CustomerIcon = (param)=>{\n    let { name } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-orange-400/30\",\n        children: name.charAt(0).toUpperCase()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n};\n_c = CustomerIcon;\nfunction PublicLiveConversationPage() {\n    var _timeline_timeline, _conversation_store;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { uuid } = router.query;\n    const [page] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isCustomerNameSet, setIsCustomerNameSet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isWaitingForAgent, setIsWaitingForAgent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const limit = 50;\n    const conversationUuid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        // Wait for router to be ready and uuid to be available\n        if (router.isReady && uuid) {\n            return uuid;\n        }\n        return null;\n    }, [\n        router.isReady,\n        uuid\n    ]);\n    // Fetch conversation data\n    const fetchConversation = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getByUuid(conversationUuid);\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setConversation(result.data || result);\n            } else {\n                setConversation(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching conversation:\", err);\n            setError(\"Failed to load conversation\");\n        }\n    };\n    // Fetch timeline data\n    const fetchTimeline = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getUnifiedTimelineByUuid(conversationUuid, {\n                page,\n                limit\n            });\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setTimeline(result.data || result);\n            } else {\n                setTimeline(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching timeline:\", err);\n            setError(\"Failed to load timeline\");\n        }\n    };\n    // Fetch data when dependencies change\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (conversationUuid && router.isReady) {\n            fetchConversation();\n            fetchTimeline();\n        }\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    // Set up polling for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!conversationUuid || !router.isReady) return;\n        const interval = setInterval(()=>{\n            fetchConversation();\n            fetchTimeline();\n        }, 2000); // Reduced from 5000ms to 2000ms for better responsiveness\n        return ()=>clearInterval(interval);\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    const appendMessage = async (messageData)=>{\n        if (!conversationUuid) return;\n        try {\n            var _messagesEndRef_current;\n            console.log(\"\\uD83D\\uDD27 Frontend: Sending message:\", messageData);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.appendMessageByUuid(conversationUuid, messageData);\n            console.log(\"✅ Frontend: Message sent successfully:\", response);\n            setNewMessage(\"\");\n            // Set waiting state for agent response (only for user messages)\n            const isUserMessage = !messageData.agentId || messageData.agentId === \"customer-message\";\n            if (isUserMessage) {\n                setIsWaitingForAgent(true);\n            }\n            // Refresh data\n            fetchConversation();\n            fetchTimeline();\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        } catch (err) {\n            console.error(\"❌ Frontend: Failed to send message:\", err);\n            setError(\"Failed to send message\");\n            setIsWaitingForAgent(false);\n        }\n    };\n    // Use a ref to track previous message count to prevent unnecessary effects\n    const prevMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    const prevAgentMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        var _timeline_timeline;\n        const currentMessageCount = (timeline === null || timeline === void 0 ? void 0 : (_timeline_timeline = timeline.timeline) === null || _timeline_timeline === void 0 ? void 0 : _timeline_timeline.length) || 0;\n        if (currentMessageCount > prevMessageCountRef.current) {\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n            prevMessageCountRef.current = currentMessageCount;\n        }\n        // Check for new agent messages to clear waiting state\n        if (timeline === null || timeline === void 0 ? void 0 : timeline.timeline) {\n            const agentMessages = timeline.timeline.filter((item)=>{\n                var _item_data_metadata, _item_data, _item_data_metadata1, _item_data1;\n                return item.type === \"message\" && ((_item_data = item.data) === null || _item_data === void 0 ? void 0 : (_item_data_metadata = _item_data.metadata) === null || _item_data_metadata === void 0 ? void 0 : _item_data_metadata.agentId) && ((_item_data1 = item.data) === null || _item_data1 === void 0 ? void 0 : (_item_data_metadata1 = _item_data1.metadata) === null || _item_data_metadata1 === void 0 ? void 0 : _item_data_metadata1.agentId) !== \"customer-message\";\n            });\n            const currentAgentMessageCount = agentMessages.length;\n            if (currentAgentMessageCount > prevAgentMessageCountRef.current && isWaitingForAgent) {\n                setIsWaitingForAgent(false);\n                prevAgentMessageCountRef.current = currentAgentMessageCount;\n            } else if (currentAgentMessageCount !== prevAgentMessageCountRef.current) {\n                prevAgentMessageCountRef.current = currentAgentMessageCount;\n            }\n        }\n    }, [\n        timeline === null || timeline === void 0 ? void 0 : (_timeline_timeline = timeline.timeline) === null || _timeline_timeline === void 0 ? void 0 : _timeline_timeline.length,\n        timeline === null || timeline === void 0 ? void 0 : timeline.timeline,\n        isWaitingForAgent\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage(e);\n        }\n    };\n    const handleSendMessage = (e)=>{\n        e.preventDefault();\n        if (!newMessage.trim() || !conversation) return;\n        // Additional validation\n        if (!conversation.uuid) {\n            console.error(\"Conversation UUID is missing\");\n            return;\n        }\n        // Require customer name\n        if (!customerName.trim()) {\n            setError(\"Please enter your name to send a message\");\n            return;\n        }\n        try {\n            // Create customer message\n            const messageData = {\n                content: \"[\".concat(customerName.trim(), \"]: \").concat(newMessage.trim()),\n                createdBy: \"1\",\n                agentId: \"customer-message\"\n            };\n            appendMessage(messageData);\n        } catch (error) {\n            console.error(\"Failed to prepare message:\", error);\n        }\n    };\n    // Customer name setup modal\n    if (!isCustomerNameSet && conversation) {\n        var _conversation_store1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-slate-950 relative overflow-hidden flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"Join Live Chat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Join the live customer conversation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0\",\n                            style: {\n                                backgroundImage: \"\\n              radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\\n            \"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 max-w-md mx-auto px-6 py-8 bg-slate-900/50 border border-cyan-500/20 rounded-xl backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400 mb-2\",\n                                    children: \"Join Live Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-cyan-300/80\",\n                                    children: [\n                                        (conversation === null || conversation === void 0 ? void 0 : (_conversation_store1 = conversation.store) === null || _conversation_store1 === void 0 ? void 0 : _conversation_store1.name) || \"Store\",\n                                        \" - Live Conversation\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: (e)=>{\n                                e.preventDefault();\n                                if (customerName.trim()) {\n                                    setIsCustomerNameSet(true);\n                                }\n                            },\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"customerName\",\n                                            className: \"block text-sm font-medium text-cyan-300 mb-2\",\n                                            children: \"Your Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"customerName\",\n                                            value: customerName,\n                                            onChange: (e)=>setCustomerName(e.target.value),\n                                            placeholder: \"Enter your name\",\n                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 text-cyan-100 placeholder-cyan-400/50\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !customerName.trim(),\n                                    className: \"w-full px-6 py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95\",\n                                    children: \"Join Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            fontFamily: 'system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif'\n        },\n        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"min-h-screen bg-slate-950 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        className: \"jsx-c1d451e38a0b1618\",\n                        children: (conversation === null || conversation === void 0 ? void 0 : conversation.title) || \"Live Chat\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Live customer conversation\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 opacity-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n            radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\\n            radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\\n            radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\\n          \"\n                        },\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n            linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%),\\n            linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%)\\n          \",\n                            backgroundSize: \"50px 50px\"\n                        },\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 opacity-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"relative z-10 max-w-4xl mx-auto px-4 py-4 md:py-6 h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"mb-4 md:mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontFamily: \"Orbitron, monospace\"\n                                },\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400\",\n                                children: (conversation === null || conversation === void 0 ? void 0 : (_conversation_store = conversation.store) === null || _conversation_store === void 0 ? void 0 : _conversation_store.name) || \"LIVE CHAT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontFamily: \"Exo 2, sans-serif\"\n                                },\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-sm md:text-base text-cyan-300/80 mt-1\",\n                                children: [\n                                    \"Live Chat - \",\n                                    customerName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"bg-red-500/10 border border-red-500/30 text-red-300 px-4 py-3 rounded-lg backdrop-blur-sm mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-5 h-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\",\n                                        className: \"jsx-c1d451e38a0b1618\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this),\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"relative w-16 h-16 mx-auto mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 border-4 border-cyan-400/20 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 border-4 border-transparent border-t-cyan-400 rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontFamily: \"Exo 2, sans-serif\"\n                                    },\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-cyan-300/80\",\n                                    children: \"Initializing Neural Interface...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this),\n                    conversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 flex flex-col bg-slate-900/30 border border-cyan-500/20 rounded-xl backdrop-blur-sm overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 overflow-y-auto p-3 md:p-4 space-y-3 md:space-y-4 scrollbar-thin scrollbar-thumb-cyan-500/30 scrollbar-track-transparent\",\n                                children: [\n                                    ((timeline === null || timeline === void 0 ? void 0 : timeline.timeline) || []).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            item: item\n                                        }, \"\".concat(item.type, \"-\").concat(item.id), false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)),\n                                    (conversation === null || conversation === void 0 ? void 0 : conversation.notificationStatus) && conversation.notificationStatus !== \"None\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-3 md:gap-4 justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerIcon, {\n                                                    name: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"max-w-[85%] md:max-w-[70%]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontFamily: \"Exo 2, sans-serif\"\n                                                                },\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-xs opacity-80\",\n                                                                children: conversation.notificationStatus === \"AgentIsThinking\" ? \"AI Agent is thinking...\" : \"AI Agent is generating response...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"inline-flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/80 rounded-full animate-bounce\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/60 rounded-full animate-bounce [animation-delay:120ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/40 rounded-full animate-bounce [animation-delay:240ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-c1d451e38a0b1618\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSendMessage,\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"border-t border-cyan-500/20 p-3 md:p-4 bg-slate-900/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-2 md:gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Enter your message...\",\n                                                    style: {\n                                                        fontFamily: \"Exo 2, sans-serif\"\n                                                    },\n                                                    rows: 1,\n                                                    disabled: isLoading,\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-full px-3 md:px-4 py-2 md:py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 resize-none text-cyan-100 placeholder-cyan-400/50 text-sm md:text-base transition-all duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-4 h-4 border-2 border-cyan-400/30 border-t-cyan-400 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: !newMessage.trim() || isLoading || !customerName.trim(),\n                                            style: {\n                                                fontFamily: \"Exo 2, sans-serif\"\n                                            },\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-4 md:px-6 py-2 md:py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-cyan-500/25 text-sm md:text-base\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-4 h-4 md:w-5 md:h-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\",\n                                                    className: \"jsx-c1d451e38a0b1618\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this) : null\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"c1d451e38a0b1618\",\n                children: \".scrollbar-thin{scrollbar-width:thin}.scrollbar-thumb-cyan-500\\\\\\\\/30::-webkit-scrollbar-thumb {background-color:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}.scrollbar-track-transparent::-webkit-scrollbar-track{background-color:transparent}::-webkit-scrollbar{width:6px}::-webkit-scrollbar-track{background:transparent}::-webkit-scrollbar-thumb{background:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}::-webkit-scrollbar-thumb:hover{background:rgba(6,182,212,.5)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicLiveConversationPage, \"/+nIfU5EvWaGMDw0s0mq0qp4vNA=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c1 = PublicLiveConversationPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"CustomerIcon\");\n$RefreshReg$(_c1, \"PublicLiveConversationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/live/[uuid].tsx\n"));

/***/ })

});