"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createToolCall = createToolCall;
exports.updateToolCall = updateToolCall;
exports.getToolCallsByConversation = getToolCallsByConversation;
exports.getToolCalls = getToolCalls;
exports.getToolCallById = getToolCallById;
exports.deleteToolCall = deleteToolCall;
exports.getToolCallStats = getToolCallStats;
const tool_call_entity_1 = require("../../conversations/tool-call.entity");
const message_entity_1 = require("../../conversations/message.entity");
const conversation_entity_1 = require("../../conversations/conversation.entity");
async function createToolCall(db, input) {
    const { toolName, toolInput, conversationId, createdBy, toolOutput, executionTime, success = true, errorMessage, cost, inputTokens, outputTokens } = input;
    try {
        const messageRepository = db.getRepository(message_entity_1.Message);
        const toolCallRepository = db.getRepository(tool_call_entity_1.ToolCall);
        const conversationRepository = db.getRepository(conversation_entity_1.Conversation);
        const [lastMessage, lastToolCall] = await Promise.all([
            messageRepository.findOne({
                where: { conversationId: conversationId.toString(), isDeleted: false },
                order: { createdAt: 'DESC' },
                select: ['id', 'createdAt'],
            }),
            toolCallRepository.findOne({
                where: { conversationId: conversationId.toString(), isDeleted: false },
                order: { createdAt: 'DESC' },
                select: ['id', 'createdAt'],
            }),
        ]);
        const nextSequence = Date.now();
        const toolCall = await toolCallRepository.save({
            toolName,
            parameters: toolInput,
            conversationId: conversationId.toString(),
            createdBy: createdBy.toString(),
            result: toolOutput,
            duration: executionTime || 0,
            status: success ? 'completed' : 'failed',
            error: errorMessage,
            userId: createdBy.toString(),
        });
        await conversationRepository.update({ id: conversationId.toString() }, {
            updatedAt: new Date(),
        });
        return toolCall;
    }
    catch (error) {
        console.error('Failed to create tool call record:', error);
        throw new Error(`Failed to create tool call record: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function updateToolCall(db, input) {
    const { id, toolOutput, executionTime, success, errorMessage, updatedBy } = input;
    try {
        const toolCallRepository = db.getRepository(tool_call_entity_1.ToolCall);
        const toolCall = await toolCallRepository.update({
            id: id.toString(),
            isDeleted: false,
        }, {
            result: toolOutput,
            duration: executionTime || 0,
            status: success ? 'completed' : 'failed',
            error: errorMessage,
            updatedBy: updatedBy.toString(),
            updatedAt: new Date(),
        });
        return toolCall;
    }
    catch (error) {
        console.error('Failed to update tool call record:', error);
        throw new Error(`Failed to update tool call record: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function getToolCallsByConversation(db, conversationId, limit, offset) {
    try {
        const toolCallRepository = db.getRepository(tool_call_entity_1.ToolCall);
        const toolCalls = await toolCallRepository.find({
            where: {
                conversationId: conversationId.toString(),
                isDeleted: false,
            },
            order: {
                createdAt: 'ASC',
            },
            take: limit,
            skip: offset,
        });
        return toolCalls;
    }
    catch (error) {
        console.error('Failed to get tool calls for conversation:', error);
        throw new Error(`Failed to get tool calls for conversation: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function getToolCalls(db, filter, limit, offset) {
    try {
        const toolCallRepository = db.getRepository(tool_call_entity_1.ToolCall);
        const where = {
            isDeleted: false,
        };
        if (filter.conversationId) {
            where.conversationId = filter.conversationId;
        }
        if (filter.toolName) {
            where.toolName = filter.toolName;
        }
        if (filter.success !== undefined) {
            where.success = filter.success;
        }
        if (filter.createdAfter || filter.createdBefore) {
            where.createdAt = {};
            if (filter.createdAfter) {
                where.createdAt.gte = filter.createdAfter;
            }
            if (filter.createdBefore) {
                where.createdAt.lte = filter.createdBefore;
            }
        }
        const toolCalls = await toolCallRepository.find({
            where,
            order: {
                createdAt: 'DESC',
            },
            take: limit,
            skip: offset,
        });
        return toolCalls;
    }
    catch (error) {
        console.error('Failed to get tool calls:', error);
        throw new Error(`Failed to get tool calls: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function getToolCallById(db, id) {
    try {
        const toolCallRepository = db.getRepository(tool_call_entity_1.ToolCall);
        const toolCall = await toolCallRepository.findOne({
            where: {
                id: id.toString(),
                isDeleted: false,
            },
        });
        return toolCall;
    }
    catch (error) {
        console.error('Failed to get tool call by ID:', error);
        throw new Error(`Failed to get tool call by ID: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function deleteToolCall(db, id, deletedBy) {
    try {
        const toolCallRepository = db.getRepository(tool_call_entity_1.ToolCall);
        await toolCallRepository.update({
            id: id.toString(),
            isDeleted: false,
        }, {
            isDeleted: true,
            updatedBy: deletedBy.toString(),
            updatedAt: new Date(),
        });
        return true;
    }
    catch (error) {
        console.error('Failed to delete tool call:', error);
        throw new Error(`Failed to delete tool call: ${error instanceof Error ? error.message : String(error)}`);
    }
}
async function getToolCallStats(db, conversationId) {
    try {
        const toolCallRepository = db.getRepository(tool_call_entity_1.ToolCall);
        const toolCalls = await toolCallRepository.find({
            where: {
                conversationId: conversationId.toString(),
                isDeleted: false,
            },
            select: ['toolName', 'status', 'duration'],
        });
        const total = toolCalls.length;
        const successful = toolCalls.filter((tc) => tc.success).length;
        const failed = total - successful;
        const executionTimes = toolCalls
            .filter((tc) => tc.executionTime !== null)
            .map((tc) => tc.executionTime);
        const averageExecutionTime = executionTimes.length > 0
            ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length
            : null;
        const toolUsage = {};
        toolCalls.forEach((tc) => {
            toolUsage[tc.toolName] = (toolUsage[tc.toolName] || 0) + 1;
        });
        return {
            total,
            successful,
            failed,
            averageExecutionTime,
            toolUsage,
        };
    }
    catch (error) {
        console.error('Failed to get tool call stats:', error);
        throw new Error(`Failed to get tool call stats: ${error instanceof Error ? error.message : String(error)}`);
    }
}
//# sourceMappingURL=tool-call.service.js.map