import { OrdersService, OrderStatusService, OrderPriorityService, CreateOrderInput as OriginalCreateOrderInput, UpdateOrderInput as OriginalUpdateOrderInput } from '../../orders/orders.service';
import { Order } from '../../orders/order.entity';
import { Customer } from '../../customers/customer.entity';
import { Repository } from 'typeorm';

// Re-export the enums for the agent tools
export { OrderStatusService, OrderPriorityService };

export interface CreateOrderInput {
  status?: OrderStatusService;
  priority?: OrderPriorityService;
  useTax?: boolean;
  taxRate?: number;
  orderDate?: Date;
  expectedDeliveryDate?: Date | null;
  preferredDeliveryLocation?: string | null;
  userId: string;
  storeId: string | bigint;
  customerId: string;
  customerPhone?: string;
  customerEmail?: string;
  customerName?: string;
  customerAddress?: string;
  createdBy: string;
  items: Array<{
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    lineTotal?: number;
    taxAmount?: number;
  }>;
}

export interface UpdateOrderInput {
  id: string;
  status?: OrderStatusService;
  priority?: OrderPriorityService;
  useTax?: boolean;
  taxRate?: number;
  orderDate?: Date;
  expectedDeliveryDate?: Date | null;
  preferredDeliveryLocation?: string | null;
  cancellationReason?: string | null;
  updatedBy: string;
  items?: Array<{
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    lineTotal?: number;
    taxAmount?: number;
  }>;
}

/**
 * Create a new order using the OrdersService
 */
export async function createOrder(input: CreateOrderInput, db: any): Promise<Order> {
  const orderRepo = db.getRepository('Order') as Repository<Order>;
  const ordersService = new OrdersService(orderRepo, db.getRepository('OrderItem'));
  
  // Transform the input to match the OrdersService expected format
  const serviceInput: OriginalCreateOrderInput = {
    status: input.status,
    priority: input.priority,
    useTax: input.useTax,
    taxRate: input.taxRate,
    orderDate: input.orderDate,
    expectedDeliveryDate: input.expectedDeliveryDate,
    preferredDeliveryLocation: input.preferredDeliveryLocation,
    userId: input.userId,
    storeId: input.storeId.toString(),
    customerId: input.customerId,
    createdBy: input.createdBy,
    items: input.items.map(item => ({
      productId: item.productId,
      productName: item.productName,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      taxAmount: item.taxAmount || 0,
    })),
  };
  
  return ordersService.createOrder(serviceInput);
}

/**
 * Update an existing order using the OrdersService
 */
export async function updateOrder(input: UpdateOrderInput, db: any): Promise<Order> {
  const orderRepo = db.getRepository('Order') as Repository<Order>;
  const ordersService = new OrdersService(orderRepo, db.getRepository('OrderItem'));
  
  // Transform the input to match the OrdersService expected format
  const serviceInput: OriginalUpdateOrderInput = {
    id: input.id,
    status: input.status,
    priority: input.priority,
    useTax: input.useTax,
    taxRate: input.taxRate,
    orderDate: input.orderDate,
    expectedDeliveryDate: input.expectedDeliveryDate,
    preferredDeliveryLocation: input.preferredDeliveryLocation,
    cancellationReason: input.cancellationReason,
    updatedBy: input.updatedBy,
    items: input.items?.map(item => ({
      productId: item.productId,
      productName: item.productName,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      taxAmount: item.taxAmount || 0,
    })),
  };
  
  return ordersService.updateOrder(serviceInput);
}

/**
 * Check if there's an order underway for a customer by phone
 */
export async function checkOrderUnderwayByPhone(phone: string, db: any): Promise<Order | null> {
  const orderRepo = db.getRepository('Order') as Repository<Order>;
  const customerRepo = db.getRepository('Customer') as Repository<Customer>;
  
  // Find customer by phone
  const customer = await customerRepo.findOne({
    where: { phone, isDeleted: false }
  });
  
  if (!customer) {
    return null;
  }
  
  // Find underway orders for this customer
  const underwayStatuses = [
    OrderStatusService.DRAFT,
    OrderStatusService.PENDING,
    OrderStatusService.CONFIRMED,
    OrderStatusService.PROCESSING,
  ];
  
  const order = await orderRepo.findOne({
    where: {
      customerId: customer.id,
      status: underwayStatuses as any,
      isDeleted: false,
    },
    relations: ['customer'],
    order: { createdAt: 'DESC' }
  });
  
  return order;
}
