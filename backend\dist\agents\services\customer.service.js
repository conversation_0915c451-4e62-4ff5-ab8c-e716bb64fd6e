"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.filterCustomers = filterCustomers;
exports.createCustomer = createCustomer;
exports.updateCustomer = updateCustomer;
async function filterCustomers(filter, db) {
    const customerRepo = db.getRepository('Customer');
    const where = { isDeleted: false };
    if (filter.storeId) {
        where.storeId = filter.storeId.toString();
    }
    if (filter.email) {
        where.email = filter.email;
    }
    if (filter.name) {
        where.name = filter.name;
    }
    if (filter.phone) {
        where.phone = filter.phone;
    }
    return customerRepo.find({
        where,
        order: { createdAt: 'DESC' }
    });
}
async function createCustomer(input, db) {
    const customerRepo = db.getRepository('Customer');
    const customer = customerRepo.create({
        name: input.name,
        email: input.email,
        phone: input.phone,
        address: input.address,
        storeId: input.storeId.toString(),
        createdBy: input.createdBy,
    });
    return customerRepo.save(customer);
}
async function updateCustomer(customerId, updateData, db) {
    const customerRepo = db.getRepository('Customer');
    const customer = await customerRepo.findOne({
        where: { id: customerId, isDeleted: false }
    });
    if (!customer) {
        throw new Error(`Customer with ID ${customerId} not found`);
    }
    Object.assign(customer, updateData);
    return customerRepo.save(customer);
}
//# sourceMappingURL=customer.service.js.map