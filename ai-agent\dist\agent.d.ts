import { ToolFunction, ToolSpec } from './types';
/**
 * Agent definition with instructions and tools.
 *
 * An Agent represents an LLM-powered assistant with specific instructions
 * and a set of tools it can use to accomplish tasks.
 */
export declare class Agent {
    name: string;
    instructions: string;
    tools: ToolFunction[];
    handoffDescription?: string;
    constructor(name: string, instructions: string, tools?: ToolFunction[], handoffDescription?: string);
    /**
     * Get OpenAI-compatible tool specifications.
     *
     * @returns List of tool specifications in the format expected by OpenAI API
     */
    getToolSpecs(): ToolSpec[];
    /**
     * Get mapping from tool names to their implementations.
     *
     * @returns Map of tool names to their function implementations
     */
    getToolMap(): Map<string, ToolFunction>;
    /**
     * Add a tool to the agent
     */
    addTool(tool: ToolFunction): void;
    /**
     * Remove a tool from the agent
     */
    removeTool(toolName: string): boolean;
    /**
     * Check if the agent has a specific tool
     */
    hasTool(toolName: string): boolean;
}
//# sourceMappingURL=agent.d.ts.map