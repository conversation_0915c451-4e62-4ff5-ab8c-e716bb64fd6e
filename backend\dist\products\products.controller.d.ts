import { ProductsService } from './products.service';
import { Product } from './product.entity';
import { PaginatedResponse } from '../types/pagination';
export declare class ProductsController {
    private readonly productsService;
    constructor(productsService: ProductsService);
    create(createProductDto: Partial<Product> & {
        userId?: string | number;
    }): Promise<Product>;
    findAll(page?: string, limit?: string): Promise<PaginatedResponse<Product>>;
    findByStoreId(storeId: string, page?: string, limit?: string): Promise<PaginatedResponse<Product>>;
    findOne(id: string): Promise<Product>;
    update(id: string, updateProductDto: Partial<Product>): Promise<Product>;
    remove(id: string): Promise<void>;
}
