// Generic print utility to inject HTML into DOM and trigger print

export type PrintOptions = {
	// Optional callback before window.print
	onBeforePrint?: () => void;
	// Optional callback after cleanup
	onAfterPrint?: () => void;
	// Optional title for the printed document
	title?: string;
};

export function printHtml(htmlContent: string, options: PrintOptions = {}): void {
	if (typeof window === 'undefined' || typeof document === 'undefined') return;

	const { onBeforePrint, onAfterPrint, title } = options;

	// Create container
	const container = document.createElement('div');
	container.id = 'print-container';
	container.style.display = 'none';
	container.innerHTML = htmlContent;
	document.body.appendChild(container);

	// Add basic print styles
	const style = document.createElement('style');
	style.id = 'print-styles';
	style.media = 'print';
	style.textContent = `
		@media print {
			@page { margin: 12mm; }
			body * { visibility: hidden !important; }
			#print-container, #print-container * { visibility: visible !important; }
			#print-container { position: absolute; left: 0; top: 0; width: 100%; display: block !important; }
		}
	`;
	document.head.appendChild(style);

	// Optionally set document title for print
	const originalTitle = document.title;
	if (title) {
		document.title = title;
	}

	// Show container before print
	container.style.display = 'block';

	onBeforePrint?.();

	window.print();

	const cleanup = () => {
		style.parentNode?.removeChild(style);
		container.parentNode?.removeChild(container);
		document.title = originalTitle;
		onAfterPrint?.();
	};

	// Some browsers fire afterprint, but we also fallback via timeout
	const afterPrintListener = () => {
		cleanup();
		window.removeEventListener('afterprint', afterPrintListener);
	};
	window.addEventListener('afterprint', afterPrintListener);
	// Fallback cleanup
	setTimeout(() => {
		cleanup();
		window.removeEventListener('afterprint', afterPrintListener);
	}, 1000);
}

function getBaseLanguage(language?: string): 'en' | 'fr' | 'ar' {
	if (!language) return 'en';
	const base = String(language).toLowerCase().split('-')[0];
	if (base === 'fr') return 'fr';
	if (base === 'ar') return 'ar';
	return 'en';
}

export function renderOrderToHtml(order: any, currencyFormatter: Intl.NumberFormat, language?: string): string {
	const baseLang = getBaseLanguage(language);
	const rtl = baseLang === 'ar';

	const texts = {
		header: baseLang === 'fr' ? 'Résumé de commande' : baseLang === 'ar' ? 'ملخص الطلب' : 'Order Summary',
		orderSep: ' • ',
		item: baseLang === 'fr' ? 'Article' : baseLang === 'ar' ? 'المنتج' : 'Item',
		qty: baseLang === 'fr' ? 'Qté' : baseLang === 'ar' ? 'الكمية' : 'Qty',
		unitPrice: baseLang === 'fr' ? 'Prix unitaire' : baseLang === 'ar' ? 'سعر الوحدة' : 'Unit Price',
		lineTotal: baseLang === 'fr' ? 'Total' : baseLang === 'ar' ? 'الإجمالي' : 'Total',
		subtotal: baseLang === 'fr' ? 'Sous-total' : baseLang === 'ar' ? 'المجموع الفرعي' : 'Subtotal',
		tax: baseLang === 'fr' ? 'Taxe' : baseLang === 'ar' ? 'الضريبة' : 'Tax',
		total: baseLang === 'fr' ? 'Total' : baseLang === 'ar' ? 'الإجمالي' : 'Total',
	};

	const itemsHtml = (order.items || [])
		.map((item: any) => {
			                        const unitPrice = parseFloat(item.unitPrice?.toString?.() ?? '0') || 0;
			                        const lineTotal = parseFloat(item.lineTotal?.toString?.() ?? '0') || 0;
			return `
				<tr>
					<td style="padding: 8px; border-bottom: 1px solid #e5e7eb;">${item.productName}</td>
					<td style="padding: 8px; border-bottom: 1px solid #e5e7eb; text-align: ${rtl ? 'left' : 'right'};">${item.quantity}</td>
					<td style="padding: 8px; border-bottom: 1px solid #e5e7eb; text-align: ${rtl ? 'left' : 'right'};">${currencyFormatter.format(unitPrice)}</td>
					<td style="padding: 8px; border-bottom: 1px solid #e5e7eb; text-align: ${rtl ? 'left' : 'right'};">${currencyFormatter.format(lineTotal)}</td>
				</tr>
			`;
		})
		.join('');

	        const subtotal = parseFloat(order.subtotal?.toString?.() ?? '0') || 0;
	        const taxAmount = parseFloat(order.taxAmount?.toString?.() ?? '0') || 0;
	        const total = parseFloat(order.total?.toString?.() ?? '0') || 0;

	const orderNumber = order.orderNumber ?? `#${order.id?.toString?.() ?? order.id}`;
	const orderDate = order.orderDate ? new Date(order.orderDate as string).toLocaleDateString() : '';

	return `
		<div style="max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif; color: #111827;" dir="${rtl ? 'rtl' : 'ltr'}">
			<div style="text-align: center; margin-bottom: 24px; border-bottom: 2px solid #111827; padding-bottom: 12px;">
				<div style="font-size: 22px; font-weight: 700;">${texts.header}</div>
				<div style="font-size: 14px; color: #6b7280;">${orderNumber}${orderDate ? `${texts.orderSep}${orderDate}` : ''}</div>
			</div>
			<table style="width: 100%; border-collapse: collapse; font-size: 14px;">
				<thead>
					<tr style="border-bottom: 1px solid #e5e7eb;">
						<th style="padding: 8px; text-align: ${rtl ? 'right' : 'left'};">${texts.item}</th>
						<th style="padding: 8px; text-align: ${rtl ? 'left' : 'right'};">${texts.qty}</th>
						<th style="padding: 8px; text-align: ${rtl ? 'left' : 'right'};">${texts.unitPrice}</th>
						<th style="padding: 8px; text-align: ${rtl ? 'left' : 'right'};">${texts.lineTotal}</th>
					</tr>
				</thead>
				<tbody>
					${itemsHtml}
				</tbody>
			</table>
			<div style="margin-top: 16px;">
				<div style="display: flex; justify-content: flex-end; gap: 24px;">
					<div style="min-width: 220px;">
						<div style="display: flex; justify-content: space-between; margin-bottom: 6px;"><span>${texts.subtotal}:</span><span>${currencyFormatter.format(subtotal)}</span></div>
						<div style="display: flex; justify-content: space-between; margin-bottom: 6px;"><span>${texts.tax}:</span><span>${currencyFormatter.format(taxAmount)}</span></div>
						<div style="border-top: 1px solid #e5e7eb; padding-top: 6px; font-weight: 600; display: flex; justify-content: space-between;"><span>${texts.total}:</span><span>${currencyFormatter.format(total)}</span></div>
					</div>
				</div>
			</div>
		</div>
	`;
}


