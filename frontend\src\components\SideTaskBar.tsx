import { useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../context/AuthContext';

interface NavigationItem {
  id: string;
  name: string;
  href: string;
  icon: JSX.Element;
  badge?: string;
  isExternal?: boolean;
  description?: string;
}

interface NavigationSection {
  name: string;
  items: NavigationItem[];
}

export default function SideTaskBar() {
  const router = useRouter();
  const { user } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const navigationSections: NavigationSection[] = [
    {
      name: "Overview",
      items: [
        {
          id: 'dashboard',
          name: 'Dashboard',
          href: '/dashboard',
          description: 'Overview of your store metrics',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            </svg>
          ),
        },
      ],
    },
    {
      name: "Communication",
      items: [
        {
          id: 'conversations',
          name: 'Conversations',
          href: '/conversations',
          description: 'Agent conversations with customers',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          ),
        },
      ],
    },
    {
      name: "Store Management",
      items: [
        {
          id: 'products',
          name: 'Products',
          href: '/products',
          description: 'Manage your product catalog',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          ),
        },
        {
          id: 'inventory',
          name: 'Inventory',
          href: '/inventory',
          description: 'Track stock levels and movements',
          badge: 'Soon',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
            </svg>
          ),
        },
        {
          id: 'customers',
          name: 'Customers',
          href: '/customers',
          description: 'Manage customer relationships',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          ),
        },
      ],
    },
    {
      name: "Sales & Orders",
      items: [
        {
          id: 'orders',
          name: 'Orders',
          href: '/orders',
          description: 'Process and manage orders',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          ),
        },
        {
          id: 'sales',
          name: 'Sales',
          href: '/sales',
          description: 'View sales analytics and reports',
          badge: 'Soon',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          ),
        },
      ],
    },
    {
      name: "Supply Chain",
      items: [
        {
          id: 'purchases',
          name: 'Purchases',
          href: '/purchases',
          description: 'Manage purchase orders',
          badge: 'Soon',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6-5V7a2 2 0 00-2-2H9a2 2 0 00-2 2v1" />
            </svg>
          ),
        },
        {
          id: 'suppliers',
          name: 'Suppliers',
          href: '/suppliers',
          description: 'Manage supplier relationships',
          badge: 'Soon',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          ),
        },
      ],
    },
  ];

  const isActiveRoute = (href: string) => {
    if (href === '/dashboard') {
      return router.pathname === '/dashboard';
    }
    return router.pathname.startsWith(href);
  };

  const handleNavigation = (item: NavigationItem) => {
    if (item.badge === 'Soon') {
      // Show coming soon notification
      return;
    }
    
    if (item.isExternal) {
      window.open(item.href, '_blank');
    } else {
      router.push(item.href);
    }
  };

  if (!user) {
    return null; // Don't show sidebar if not authenticated
  }

  return (
    <>
      {/* Mobile backdrop */}
      <div className="fixed inset-0 z-40 lg:hidden" aria-hidden="true">
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
      </div>

      {/* Sidebar */}
      <div className={`fixed top-0 left-0 z-50 h-full bg-slate-900 border-r border-slate-700/50 transition-all duration-300 ease-in-out ${
        isCollapsed ? 'w-16' : 'w-64'
      } flex flex-col`}>
        
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700/50">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-emerald-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <span className="text-lg font-bold bg-gradient-to-r from-emerald-400 to-cyan-400 bg-clip-text text-transparent">
                Navigation
              </span>
            </div>
          )}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 rounded-lg bg-slate-800/60 border border-slate-600/50 text-slate-400 hover:text-white hover:bg-slate-700/60 transition-all duration-200"
          >
            <svg className={`w-4 h-4 transition-transform duration-200 ${isCollapsed ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            </svg>
          </button>
        </div>

        {/* Navigation Content */}
        <div className="flex-1 overflow-y-auto py-4 space-y-6">
          {navigationSections.map((section) => (
            <div key={section.name} className="px-3">
              {!isCollapsed && (
                <h3 className="px-3 text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3">
                  {section.name}
                </h3>
              )}
              <nav className="space-y-1">
                {section.items.map((item) => {
                  const isActive = isActiveRoute(item.href);
                  const isComingSoon = item.badge === 'Soon';
                  
                  return (
                    <button
                      key={item.id}
                      onClick={() => handleNavigation(item)}
                      disabled={isComingSoon}
                      className={`group w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                        isActive
                          ? 'bg-emerald-600/20 border border-emerald-500/30 text-emerald-400'
                          : isComingSoon
                          ? 'text-slate-500 cursor-not-allowed'
                          : 'text-slate-300 hover:bg-slate-800/60 hover:text-white'
                      }`}
                      title={isCollapsed ? item.name : item.description}
                    >
                      <div className={`flex-shrink-0 ${isActive ? 'text-emerald-400' : isComingSoon ? 'text-slate-500' : 'text-slate-400 group-hover:text-white'}`}>
                        {item.icon}
                      </div>
                      
                      {!isCollapsed && (
                        <>
                          <span className="ml-3 flex-1 text-left">{item.name}</span>
                          {item.badge && (
                            <span className={`ml-2 px-2 py-0.5 text-xs rounded-full font-medium ${
                              item.badge === 'Soon'
                                ? 'bg-yellow-900/30 text-yellow-400 border border-yellow-500/30'
                                : 'bg-emerald-900/30 text-emerald-400 border border-emerald-500/30'
                            }`}>
                              {item.badge}
                            </span>
                          )}
                          {isActive && (
                            <svg className="w-4 h-4 text-emerald-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          )}
                        </>
                      )}
                      
                      {/* Collapsed tooltip indicator */}
                      {isCollapsed && item.badge && (
                        <div className="absolute left-14 w-2 h-2 bg-yellow-400 rounded-full"></div>
                      )}
                    </button>
                  );
                })}
              </nav>
            </div>
          ))}
        </div>

        {/* Sidebar Footer */}
        {!isCollapsed && (
          <div className="p-4 border-t border-slate-700/50">
            <div className="bg-slate-800/60 rounded-lg p-3 border border-slate-600/50">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold shadow-sm flex-shrink-0">
                  {user ? (user.name?.[0] || user.email?.[0] || 'U').toUpperCase() : 'G'}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-white truncate">
                    {user ? (user.name || user.email?.split('@')[0]) : 'Guest'}
                  </div>
                  <div className="text-xs text-slate-400 truncate">
                    {user ? user.email : 'Not signed in'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main content offset */}
      <div className={`transition-all duration-300 ease-in-out ${isCollapsed ? 'ml-16' : 'ml-64'}`}>
        {/* This div pushes content to the right of the sidebar */}
      </div>
    </>
  );
}
