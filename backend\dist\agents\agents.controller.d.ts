import { AgentsService } from './agents.service';
import { Agent } from './agent.entity';
export declare class AgentsController {
    private readonly agentsService;
    constructor(agentsService: AgentsService);
    create(createAgentDto: Partial<Agent> & {
        userId?: string | number;
    }): Promise<Agent>;
    findAll(): Promise<Agent[]>;
    findByStoreId(storeId: string): Promise<Agent[]>;
    findByUserId(userId: string): Promise<Agent[]>;
    findOne(id: string): Promise<Agent>;
    update(id: string, updateAgentDto: Partial<Agent>): Promise<Agent>;
    remove(id: string): Promise<void>;
}
