# Example environment configuration for testing the LLM API
# Copy this file to .env and fill in your actual values

# Your OpenRouter API key
API_KEY=your_openrouter_api_key_here

# OpenRouter base URL (default)
BASE_URL=https://openrouter.ai/api/v1

# Model to use (Google Gemini 2.5 Flash Lite as shown in backend config)
MODEL=google/gemini-2.5-flash-lite

# Optional: Enable tracing for debugging
AGENT_TRACE=1

# Optional: Disable tools for testing (set to 1 to disable)
# DISABLE_TOOLS=0
