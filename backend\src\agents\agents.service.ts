import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Agent } from './agent.entity';

@Injectable()
export class AgentsService {
  constructor(
    @InjectRepository(Agent)
    private agentsRepository: Repository<Agent>,
  ) {}

  async findAll(): Promise<Agent[]> {
    return this.agentsRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Agent> {
    const agent = await this.agentsRepository.findOne({
      where: { id, isDeleted: false },
    });
    
    if (!agent) {
      throw new NotFoundException(`Agent with ID ${id} not found`);
    }
    
    return agent;
  }

  async findByStoreId(storeId: string): Promise<Agent[]> {
    return this.agentsRepository.find({
      where: { storeId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findByUserId(userId: string): Promise<Agent[]> {
    // Find agents created by the user
    return this.agentsRepository.find({
      where: { createdBy: userId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async create(createAgentDto: Partial<Agent> & { userId?: string | number }): Promise<Agent> {
    // Extract userId and map it to the correct fields
    const { userId, ...agentData } = createAgentDto;

    // Create the agent with proper field mapping
    const agent = this.agentsRepository.create({
      ...agentData,
      createdBy: userId?.toString(),
    });

    return this.agentsRepository.save(agent);
  }

  async update(id: string, updateAgentDto: Partial<Agent>): Promise<Agent> {
    const agent = await this.findOne(id);
    Object.assign(agent, updateAgentDto);
    return this.agentsRepository.save(agent);
  }

  async remove(id: string): Promise<void> {
    const agent = await this.findOne(id);
    agent.isDeleted = true;
    await this.agentsRepository.save(agent);
  }
}
