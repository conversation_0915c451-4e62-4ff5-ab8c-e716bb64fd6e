"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPaginatedResponseSchema = exports.paginationMetaSchema = exports.paginationInputSchema = void 0;
exports.createPaginationMeta = createPaginationMeta;
const zod_1 = require("zod");
exports.paginationInputSchema = zod_1.z.object({
    page: zod_1.z.coerce.number().min(1).default(1),
    limit: zod_1.z.coerce.number().min(1).max(100).default(10),
});
exports.paginationMetaSchema = zod_1.z.object({
    total: zod_1.z.number().describe('Total number of items across all pages'),
    page: zod_1.z.number().describe('Current page number'),
    limit: zod_1.z.number().describe('Number of items per page'),
    totalPages: zod_1.z.number().describe('Total number of pages available'),
    hasNext: zod_1.z.boolean().describe('Boolean indicating if there are more pages'),
    hasPrev: zod_1.z.boolean().describe('Boolean indicating if there are previous pages'),
});
const createPaginatedResponseSchema = (dataSchema) => zod_1.z.object({
    data: zod_1.z.array(dataSchema),
    meta: exports.paginationMetaSchema,
});
exports.createPaginatedResponseSchema = createPaginatedResponseSchema;
function createPaginationMeta(total, page, limit) {
    const totalPages = Math.ceil(total / limit);
    return {
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
    };
}
//# sourceMappingURL=pagination.js.map