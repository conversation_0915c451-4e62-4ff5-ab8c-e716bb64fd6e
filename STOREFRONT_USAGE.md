# Public Storefront Feature

This document explains the new public storefront feature that allows customers to browse and shop from stores without requiring authentication.

## Overview

The public storefront provides customers with:
- Browse stores without logging in
- View store products with images, descriptions, and prices
- Add products to a shopping cart
- Manage cart items (add, remove, update quantities)
- View cart totals

## Routes

### Main Landing Page
- **Route**: `/`
- **Purpose**: Landing page with options to browse stores or access admin dashboard
- **Access**: Public

### Store Directory
- **Route**: `/storefront`
- **Purpose**: Lists all available stores with quick access options
- **Access**: Public
- **Features**:
  - View all stores with their descriptions
  - See store statistics (product count, customer count)
  - Quick dropdown store selector
  - Store cards with "Visit Store" buttons

### Individual Store Page
- **Route**: `/store/[storeId]`
- **Purpose**: Public storefront for a specific store
- **Access**: Public
- **Features**:
  - Store name and description in header
  - Product grid with images, names, descriptions, prices, and SKUs
  - Add to cart functionality
  - Shopping cart sidebar
  - Cart management (quantity updates, item removal)
  - Running cart total

## API Endpoints Used

The storefront leverages existing public API endpoints:

- `store.getById` - Get store information
- `store.getAll` - Get all stores for directory
- `product.getByStoreId` - Get products for a specific store

## Cart Functionality

The shopping cart is implemented as client-side state and includes:

- **Add to Cart**: Click "Add to Cart" on any product
- **View Cart**: Click the cart button in the header (shows item count)
- **Update Quantities**: Use +/- buttons in cart sidebar
- **Remove Items**: Click trash icon in cart sidebar
- **Cart Total**: Automatically calculated and displayed

## Features

### No Authentication Required
- Customers can browse and shop without creating accounts
- No login prompts or authentication barriers
- Completely public access

### Responsive Design
- Mobile-friendly layout
- Product grid adapts to screen size
- Cart sidebar works on all devices

### Image Handling
- Product images with fallback placeholders
- Error handling for broken image URLs
- Consistent placeholder design

### Currency Formatting
- Uses existing `formatCurrency` utility
- Consistent price display throughout

## Usage Examples

### Accessing a Store Directly
```
http://localhost:3000/store/1
```

### Browsing All Stores
```
http://localhost:3000/storefront
```

### Starting from Landing Page
```
http://localhost:3000/
```

## Technical Notes

### State Management
- Cart state is maintained in React component state
- No persistence between sessions (intentional for simplicity)
- Could be extended with localStorage or session storage

### API Integration
- Uses existing tRPC endpoints
- Leverages superjson for BigInt serialization
- Error handling for network issues

### Styling
- Consistent with existing admin dashboard theme
- Dark theme with slate colors
- Blue accent colors for primary actions

## Future Enhancements

Potential improvements could include:
- Cart persistence with localStorage
- Customer contact form for orders
- Product search and filtering
- Categories and product organization
- Checkout flow with order creation
- Email order confirmation
- Guest checkout process
