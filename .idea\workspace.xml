<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1bcc7005-04c4-40a5-9ed6-0d4ab7a04e0a" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agent-conversation.service.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agent-conversation.service.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agent-conversation.service.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-conversation-tools.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-conversation-tools.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-conversation-tools.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-checkOrderUnderway.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-checkOrderUnderway.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-checkOrderUnderway.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-placeOrder.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-placeOrder.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-placeOrder.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-searchCustomers.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-searchCustomers.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-searchCustomers.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-updateCustomer.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-updateCustomer.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-updateCustomer.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-updateOrder.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-updateOrder.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/agent-tool-updateOrder.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/index.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/shared.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/shared.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/agentTools/shared.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/conversation-agent.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/conversation-agent.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/conversation-agent.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/tool-call.service.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/tool-call.service.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/agent/tool-call.service.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/db/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/db/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/db/index.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/services/customer.service.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/agents/services/customer.service.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/services/customer.service.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/agents/services/customer.service.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/services/customer.service.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/agents/services/customer.service.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/services/order.service.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/agents/services/order.service.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/services/order.service.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/agents/services/order.service.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/agents/services/order.service.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/agents/services/order.service.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/conversations/conversation.entity.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/conversations/conversation.entity.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/conversations/conversation.entity.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/conversations/conversation.entity.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/conversations/conversation.entity.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/conversations/conversation.entity.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/conversations/conversations.service.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/conversations/conversations.service.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/conversations/conversations.service.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/conversations/conversations.service.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/conversations/conversations.service.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/conversations/conversations.service.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/tool-calls/tool-call.entity.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/tool-calls/tool-call.entity.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/tool-calls/tool-call.entity.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/tool-calls/tool-call.entity.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/tool-calls/tool-call.entity.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/tool-calls/tool-call.entity.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agent-conversation.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/agents/agent/agent-conversation.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agentTools/README-updateCustomer.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agentTools/agent-conversation-tools.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agentTools/agent-tool-checkOrderUnderway.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agentTools/agent-tool-placeOrder.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agentTools/agent-tool-searchCustomers.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agentTools/agent-tool-updateCustomer.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agentTools/agent-tool-updateOrder.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agentTools/index.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/agentTools/shared.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/conversation-agent.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/agent/tool-call.service.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/db/index.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/services/customer.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/agents/services/customer.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/agents/services/order.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/agents/services/order.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/conversations/conversation.entity.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/conversations/conversation.entity.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/conversations/conversations.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/conversations/conversations.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/tool-calls/tool-call.entity.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/tool-calls/tool-call.entity.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence"><![CDATA[{}]]></component>
  <component name="KubernetesApiProvider"><![CDATA[{
  "isMigrated": true
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 3
}]]></component>
  <component name="ProjectId" id="31xlO7F04kM9fPD2SzvY4gkR4Bb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "com.codeium.enabled": "true",
    "git-widget-placeholder": "dev-mahdi",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "C:/Users/<USER>/Documents/workspace/projects/teno-store",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "ts.external.directory.path": "C:\\Program Files\\JetBrains\\WebStorm 2025.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-WS-251.23774.424" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1bcc7005-04c4-40a5-9ed6-0d4ab7a04e0a" name="Changes" comment="" />
      <created>1756473569818</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756473569818</updated>
      <workItem from="1756473575203" duration="2354000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/ai-agent/examples/info-agent.js" />
        <option value="$PROJECT_DIR$/ai-agent/examples/info-agent.js.map" />
        <option value="$PROJECT_DIR$/ai-agent/examples/info-agent.d.ts" />
        <option value="$PROJECT_DIR$/ai-agent/examples/info-agent.d.ts.map" />
        <option value="$PROJECT_DIR$/ai-agent/examples/sale-agent.js" />
        <option value="$PROJECT_DIR$/ai-agent/examples/sale-agent.js.map" />
        <option value="$PROJECT_DIR$/ai-agent/examples/sale-agent.d.ts" />
        <option value="$PROJECT_DIR$/ai-agent/examples/sale-agent.d.ts.map" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>