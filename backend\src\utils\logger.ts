/**
 * Enhanced logger utility that includes file name and line number information
 * in debug logs for better debugging experience.
 */

interface LogContext {
  file: string;
  line: number;
  function?: string;
}

/**
 * Gets the current call stack information to extract file name and line number
 */
function getCallStackInfo(): LogContext {
  const stack = new Error().stack;
  if (!stack) {
    return { file: 'unknown', line: 0 };
  }

  // Split stack into lines and find the caller (skip Error constructor and this function)
  const lines = stack.split('\n');
  let callerLine = '';
  
  // Find the first line that's not from this logger utility
  for (let i = 2; i < lines.length; i++) {
    const line = lines[i];
    if (line && !line.includes('logger.ts') && !line.includes('getCallStackInfo')) {
      callerLine = line;
      break;
    }
  }

  if (!callerLine) {
    return { file: 'unknown', line: 0 };
  }

  // Parse the stack line to extract file and line information
  // Format: "    at functionName (file:line:column)" or "    at file:line:column"
  const match = callerLine.match(/at\s+(?:(\S+)\s+\()?([^:]+):(\d+):(\d+)\)?/);
  
  if (match) {
    const [, functionName, filePath, lineStr, columnStr] = match;
    const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || filePath;
    
    return {
      file: fileName,
      line: parseInt(lineStr, 10),
      function: functionName || undefined
    };
  }

  return { file: 'unknown', line: 0 };
}

/**
 * Formats the log message with file and line information
 */
function formatMessage(level: string, message: string, context: LogContext): string {
  const { file, line, function: funcName } = context;
  const funcInfo = funcName ? ` (${funcName})` : '';
  return `[${level}] ${file}:${line}${funcInfo} - ${message}`;
}

/**
 * Enhanced console.log with file and line information
 */
export function log(message: string, ...args: any[]): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('LOG', message, context);
  console.log(formattedMessage, ...args);
}

/**
 * Enhanced console.error with file and line information
 */
export function error(message: string, ...args: any[]): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('ERROR', message, context);
  console.error(formattedMessage, ...args);
}

/**
 * Enhanced console.warn with file and line information
 */
export function warn(message: string, ...args: any[]): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('WARN', message, context);
  console.warn(formattedMessage, ...args);
}

/**
 * Enhanced console.info with file and line information
 */
export function info(message: string, ...args: any[]): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('INFO', message, context);
  console.info(formattedMessage, ...args);
}

/**
 * Enhanced console.debug with file and line information
 */
export function debug(message: string, ...args: any[]): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('DEBUG', message, context);
  console.debug(formattedMessage, ...args);
}

/**
 * Enhanced console.trace with file and line information
 */
export function trace(message: string, ...args: any[]): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('TRACE', message, context);
  console.trace(formattedMessage, ...args);
}

/**
 * Enhanced console.table with file and line information
 */
export function table(data: any, columns?: string[]): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('TABLE', 'Table data:', context);
  console.log(formattedMessage);
  console.table(data, columns);
}

/**
 * Enhanced console.group with file and line information
 */
export function group(label: string): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('GROUP', label, context);
  console.group(formattedLabel);
}

/**
 * Enhanced console.groupCollapsed with file and line information
 */
export function groupCollapsed(label: string): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('GROUP', label, context);
  console.groupCollapsed(formattedLabel);
}

/**
 * Enhanced console.groupEnd
 */
export function groupEnd(): void {
  console.groupEnd();
}

/**
 * Enhanced console.time with file and line information
 */
export function time(label: string): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('TIME', label, context);
  console.time(formattedLabel);
}

/**
 * Enhanced console.timeEnd with file and line information
 */
export function timeEnd(label: string): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('TIME', label, context);
  console.timeEnd(formattedLabel);
}

/**
 * Enhanced console.timeLog with file and line information
 */
export function timeLog(label: string, ...args: any[]): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('TIME', label, context);
  console.timeLog(formattedLabel, ...args);
}

/**
 * Enhanced console.count with file and line information
 */
export function count(label: string): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('COUNT', label, context);
  console.count(formattedLabel);
}

/**
 * Enhanced console.countReset with file and line information
 */
export function countReset(label: string): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('COUNT', label, context);
  console.countReset(formattedLabel);
}

/**
 * Enhanced console.assert with file and line information
 */
export function assert(condition: any, message: string, ...args: any[]): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('ASSERT', message, context);
  console.assert(condition, formattedMessage, ...args);
}

/**
 * Enhanced console.clear
 */
export function clear(): void {
  console.clear();
}

/**
 * Enhanced console.dir with file and line information
 */
export function dir(obj: any, options?: any): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('DIR', 'Object inspection:', context);
  console.log(formattedMessage);
  console.dir(obj, options);
}

/**
 * Enhanced console.dirxml with file and line information
 */
export function dirxml(node: any): void {
  const context = getCallStackInfo();
  const formattedMessage = formatMessage('DIRXML', 'XML/HTML inspection:', context);
  console.log(formattedMessage);
  console.dirxml(node);
}

/**
 * Enhanced console.profile with file and line information
 */
export function profile(label: string): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('PROFILE', label, context);
  console.profile(formattedLabel);
}

/**
 * Enhanced console.profileEnd with file and line information
 */
export function profileEnd(label: string): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('PROFILE', label, context);
  console.profileEnd(formattedLabel);
}

/**
 * Enhanced console.timeStamp with file and line information
 */
export function timeStamp(label: string): void {
  const context = getCallStackInfo();
  const formattedLabel = formatMessage('TIMESTAMP', label, context);
  console.timeStamp(formattedLabel);
}

// Export default logger object with all methods
const logger = {
  log,
  error,
  warn,
  info,
  debug,
  trace,
  table,
  group,
  groupCollapsed,
  groupEnd,
  time,
  timeEnd,
  timeLog,
  count,
  countReset,
  assert,
  clear,
  dir,
  dirxml,
  profile,
  profileEnd,
  timeStamp
};

export default logger;
