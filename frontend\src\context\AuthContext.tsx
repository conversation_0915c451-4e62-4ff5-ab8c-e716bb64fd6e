import { createContext, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { AuthUser, getCurrentUser, performLogout, redirectToGoogleAuth, clearClientAuthArtifacts } from '../utils/auth';

interface AuthContextValue {
  user: AuthUser | null;
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  loginWithGoogle: (nextPath?: string) => void;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const storageKey = 'teno:auth:user';
  const lastUserIdRef = useRef<string | null>(null);

  const refresh = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const current = await getCurrentUser();
      setUser(current);
      try {
        if (current) {
          localStorage.setItem(storageKey, JSON.stringify(current));
        } else {
          localStorage.removeItem(storageKey);
        }
      } catch {}
      try {
        const prevId = lastUserIdRef.current;
        const nextId = current?.id ?? null;
        const changed = prevId !== nextId;
        lastUserIdRef.current = nextId;
        if (changed && typeof window !== 'undefined') {
          const evtName = current ? 'teno:auth:login' : 'teno:auth:logout';
          window.dispatchEvent(new CustomEvent('teno:auth:change', { detail: { user: current } }));
          window.dispatchEvent(new CustomEvent(evtName, { detail: { user: current } }));
        }
      } catch {}
    } catch (err) {
      console.warn('[AuthContext] Error during refresh:', err);
      setError('Failed to load user');
      setUser(null);
      try { localStorage.removeItem(storageKey); } catch {}
    } finally {
      setIsLoading(false);
    }
  }, []);

  const loginWithGoogle = useCallback((nextPath?: string) => {
    redirectToGoogleAuth(nextPath);
  }, []);

  const logout = useCallback(async () => {
    // Optimistically clear local state for snappier UX
    setUser(null);
    try { localStorage.removeItem(storageKey); } catch {}
    try {
      await performLogout();
    } finally {
      // Always refresh after logout to ensure backend/session is in sync
      await refresh();
    }
  }, [refresh]);

  useEffect(() => {
    // Optimistically hydrate from localStorage for fast first paint
    try {
      const raw = localStorage.getItem(storageKey);
      if (raw) {
        const cached = JSON.parse(raw) as AuthUser;
        setUser(cached);
        lastUserIdRef.current = cached?.id ?? null;
        // If we have a cached user, try to refresh from backend
        refresh();
      } else {
        // No cached user, just set loading to false
        setIsLoading(false);
      }
    } catch (e) {
      console.warn('[AuthContext] Error reading cached user:', e);
      setIsLoading(false);
    }
    
    // Listen for global unauthorized signals to immediately drop user state
    const onUnauthorized = () => {
      clearClientAuthArtifacts();
      setUser(null);
      lastUserIdRef.current = null;
    };
    if (typeof window !== 'undefined') {
      window.addEventListener('teno:auth:unauthorized', onUnauthorized as EventListener);
    }
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('teno:auth:unauthorized', onUnauthorized as EventListener);
      }
    };
  }, [refresh]);

  const value = useMemo<AuthContextValue>(() => ({
    user,
    isLoading,
    error,
    refresh,
    loginWithGoogle,
    logout,
  }), [user, isLoading, error, refresh, loginWithGoogle, logout]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextValue {
  const ctx = useContext(AuthContext);
  if (!ctx) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return ctx;
}


