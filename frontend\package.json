{"name": "teno-store-frontend", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3000", "lint": "next lint", "start": "next start -p 3000"}, "dependencies": {"@tanstack/react-query": "^4.36.1", "next": "14.0.4", "next-auth": "^4.24.11", "react": "^18", "react-dom": "^18", "superjson": "^2.2.2", "three": "^0.179.1", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/three": "^0.179.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}