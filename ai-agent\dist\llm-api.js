"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LlmApi = void 0;
const openai_1 = __importDefault(require("openai"));
const llm_provider_1 = require("./llm-provider");
const types_1 = require("./types");
/**
 * Executes agents with the OpenAI API.
 *
 * This class handles the execution flow for agents, including:
 * - Setting up the OpenAI client
 * - Managing the conversation
 * - Handling tool calls and responses
 */
class LlmApi {
    /**
     * Generate an LLM response given an agent and a list of chat messages.
     * Messages may be provided as `{ role, message }` or `{ role, content }`.
     * Returns a LlmResponseResult containing both the message response and tool calls.
     */
    static async generateLlmResponse(agent, chatMessages, model) {
        const trace = (process.env.AGENT_TRACE === '1' || process.env.AGENT_TRACE === 'true');
        const apiKey = llm_provider_1.LLMProvider.apiKey;
        const baseUrl = llm_provider_1.LLMProvider.baseUrl;
        const defaultModel = llm_provider_1.LLMProvider.model || 'gpt-4o-mini';
        const selectedModel = model || defaultModel;
        if (!apiKey) {
            throw new Error('API key is not set. Provide via setLlmProvider() or environment variables.');
        }
        const clientConfig = { apiKey };
        if (baseUrl) {
            clientConfig.baseURL = baseUrl;
        }
        const referer = process.env.HTTP_REFERER || process.env.OPENROUTER_SITE_URL;
        const title = process.env.X_TITLE || process.env.OPENROUTER_TITLE || 'teno-store';
        clientConfig.defaultHeaders = {
            ...(clientConfig.defaultHeaders || {}),
            ...(referer ? { 'HTTP-Referer': referer } : {}),
            ...(title ? { 'X-Title': title } : {}),
        };
        const client = new openai_1.default(clientConfig);
        const disableTools = (process.env.DISABLE_TOOLS === '1' || process.env.DISABLE_TOOLS === 'true');
        const tools = disableTools ? [] : agent.getToolSpecs();
        const toolMap = agent.getToolMap();
        // Cost tracking variables - will be populated for each LLM call
        const costBreakdown = [];
        let totalInputTokens = 0;
        let totalOutputTokens = 0;
        let totalCost = 0;
        const normalize = (m) => ({
            role: m.role,
            content: m.content ?? '',
            tool_calls: m.tool_calls,
            tool_call_id: m.tool_call_id,
        });
        const messages = [
            { role: 'system', content: agent.instructions },
            ...chatMessages.map(normalize),
        ];
        const convertToOpenAIMessage = (msg) => {
            if (msg.role === 'system') {
                return { role: 'system', content: msg.content || '' };
            }
            if (msg.role === 'user') {
                return { role: 'user', content: msg.content || '' };
            }
            if (msg.role === 'assistant') {
                const assistantMsg = { role: 'assistant' };
                if (msg.content)
                    assistantMsg.content = msg.content;
                if (msg.tool_calls)
                    assistantMsg.tool_calls = msg.tool_calls;
                return assistantMsg;
            }
            if (msg.role === 'tool') {
                return {
                    role: 'tool',
                    content: msg.content || '',
                    tool_call_id: msg.tool_call_id || ''
                };
            }
            throw new Error(`Unknown message role: ${msg.role}`);
        };
        // Safely serialize tool results that may contain BigInt or Prisma Decimal
        const safeStringify = (value) => {
            const replacer = (_key, val) => {
                if (typeof val === 'bigint') {
                    return val.toString();
                }
                // Prisma Decimal or similar numeric classes
                if (val && typeof val === 'object') {
                    const ctorName = val.constructor?.name;
                    if (ctorName === 'Decimal' && typeof val.toString === 'function') {
                        return val.toString();
                    }
                }
                return val;
            };
            return typeof value === 'string' ? value : JSON.stringify(value, replacer);
        };
        // FIRST LLM CALL
        console.log('\n=== FIRST LLM CALL ===');
        const firstCallStartTime = Date.now();
        let response;
        try {
            response = await client.chat.completions.create({
                model: selectedModel,
                messages: messages.map(convertToOpenAIMessage),
                tools: tools.length > 0 ? tools : undefined,
                tool_choice: tools.length > 0 ? 'auto' : undefined,
            });
        }
        catch (err) {
            const status = err?.status || err?.response?.status;
            const data = err?.response?.data || err?.error || err?.data;
            const code = err?.code || err?.error?.code;
            const type = err?.type || err?.error?.type;
            console.error('[LlmApi.generateLlmResponse] provider error', { status, code, type, message: err?.message, data, baseUrl, selectedModel, disableTools });
            if (trace && err?.stack)
                console.error('[LlmApi.generateLlmResponse] provider error stack', err.stack);
            throw err;
        }
        const firstCallEndTime = Date.now();
        const firstCallExecutionTime = firstCallEndTime - firstCallStartTime;
        const first = response.choices[0]?.message;
        console.log('\nFirst Call Output:');
        console.log(JSON.stringify(first, null, 2));
        // Track costs and timing for first LLM call (initial response with potential tool calls)
        const firstInputTokens = response.usage?.prompt_tokens || 0;
        const firstOutputTokens = response.usage?.completion_tokens || 0;
        const firstCost = (0, types_1.estimateLlmCost)(selectedModel, firstInputTokens, firstOutputTokens);
        costBreakdown.push({
            callType: 'initial',
            inputTokens: firstInputTokens,
            outputTokens: firstOutputTokens,
            cost: firstCost,
            model: selectedModel,
            startTime: firstCallStartTime,
            endTime: firstCallEndTime,
            executionTime: firstCallExecutionTime
        });
        // Always proceed to second LLM call, even if no tools were called
        // This ensures consistent behavior and allows the agent to provide final responses
        // Initialize totals with first call costs
        totalInputTokens = firstInputTokens;
        totalOutputTokens = firstOutputTokens;
        totalCost = firstCost;
        const toolOutputs = [];
        const executionDetails = [];
        for (const toolCall of (first.tool_calls || [])) {
            const fnName = toolCall.function?.name;
            const fnArgs = toolCall.function?.arguments || '{}';
            if (!fnName)
                continue;
            const toolFn = toolMap.get(fnName);
            if (!toolFn) {
                toolOutputs.push({ role: 'tool', content: `Tool not found: ${fnName}`, tool_call_id: toolCall.id });
                continue;
            }
            let parsedArgs = {};
            try {
                parsedArgs = JSON.parse(fnArgs);
            }
            catch { }
            let result;
            const startTime = Date.now();
            let endTime;
            let success = true;
            let errorMessage;
            try {
                // Support both positional and named-args tools
                result = toolFn.length > 1 ? await toolFn(...Object.values(parsedArgs)) : await toolFn(parsedArgs);
                endTime = Date.now();
                const executionTime = endTime - startTime;
                const serialized = safeStringify(result);
                toolOutputs.push({ role: 'tool', content: serialized, tool_call_id: toolCall.id });
                // Track successful execution
                executionDetails.push({
                    toolCallId: toolCall.id,
                    toolName: fnName,
                    executionTime,
                    success: true,
                    startTime,
                    endTime
                });
            }
            catch (err) {
                endTime = Date.now();
                const executionTime = endTime - startTime;
                success = false;
                errorMessage = err?.message || String(err);
                // Enhanced error logging with file context
                const errorStack = err?.stack || '';
                const errorContext = {
                    toolName: fnName,
                    toolCallId: toolCall.id,
                    arguments: fnArgs,
                    error: errorMessage,
                    stack: errorStack,
                    timestamp: new Date().toISOString()
                };
                console.error(`\n🚨 TOOL EXECUTION ERROR in ${fnName}:`);
                console.error(`   Tool Call ID: ${toolCall.id}`);
                console.error(`   Arguments: ${fnArgs}`);
                console.error(`   Error: ${errorMessage}`);
                console.error(`   Execution Time: ${executionTime}ms`);
                if (trace && errorStack) {
                    console.error(`   Stack Trace:`);
                    console.error(errorStack);
                }
                console.error(`   Full Error Context:`, JSON.stringify(errorContext, null, 2));
                console.error(`\n`);
                toolOutputs.push({ role: 'tool', content: `Tool execution error: ${errorMessage}`, tool_call_id: toolCall.id });
                // Track failed execution
                executionDetails.push({
                    toolCallId: toolCall.id,
                    toolName: fnName,
                    executionTime,
                    success: false,
                    errorMessage,
                    startTime,
                    endTime
                });
            }
        }
        // Print tool call outputs clearly
        console.log('\n=== TOOL CALL OUTPUTS ===');
        if (toolOutputs.length === 0) {
            console.log('No tool calls were executed.');
        }
        else {
            toolOutputs.forEach((output, index) => {
                console.log(`\nTool Output ${index + 1}:`);
                console.log(`  Tool Call ID: ${output.tool_call_id}`);
                console.log(`  Content: ${output.content}`);
            });
        }
        // SECOND LLM CALL
        console.log('\n=== SECOND LLM CALL ===');
        // Only include tool messages if there were actual tool calls
        // This ensures the message sequence is valid for the OpenAI API
        const followupMessages = [
            ...messages,
            { role: 'assistant', content: first.content, tool_calls: first.tool_calls },
        ];
        // Only add tool outputs if there were actual tool calls
        if (toolOutputs.length > 0) {
            followupMessages.push(...toolOutputs);
        }
        // Log the followup messages in a more readable, stringified format with extra context
        console.log('\n=== FOLLOWUP MESSAGES FOR SECOND LLM CALL ===');
        followupMessages.map((msg, idx) => {
            const openAIMsg = convertToOpenAIMessage(msg);
            console.log(`Message ${idx + 1}: ${JSON.stringify(openAIMsg, null, 2)}`);
        });
        console.log(`Total followup messages: ${followupMessages.length}`);
        const secondCallStartTime = Date.now();
        let secondResponse;
        try {
            secondResponse = await client.chat.completions.create({
                model: selectedModel,
                messages: followupMessages.map(convertToOpenAIMessage),
                tools: tools.length > 0 ? tools : undefined,
                tool_choice: tools.length > 0 ? 'none' : undefined,
            });
            const secondCallEndTime = Date.now();
            const secondCallExecutionTime = secondCallEndTime - secondCallStartTime;
            // Track costs and timing for second LLM call (followup response after tool execution)
            const secondInputTokens = secondResponse.usage?.prompt_tokens || 0;
            const secondOutputTokens = secondResponse.usage?.completion_tokens || 0;
            const secondCost = (0, types_1.estimateLlmCost)(selectedModel, secondInputTokens, secondOutputTokens);
            costBreakdown.push({
                callType: 'followup',
                inputTokens: secondInputTokens,
                outputTokens: secondOutputTokens,
                cost: secondCost,
                model: selectedModel,
                startTime: secondCallStartTime,
                endTime: secondCallEndTime,
                executionTime: secondCallExecutionTime
            });
            totalInputTokens += secondInputTokens;
            totalOutputTokens += secondOutputTokens;
            totalCost += secondCost;
        }
        catch (err) {
            const status = err?.status || err?.response?.status;
            const data = err?.response?.data || err?.error || err?.data;
            const code = err?.code || err?.error?.code;
            const type = err?.type || err?.error?.type;
            console.error('[LlmApi.generateLlmResponse] provider error (second call)', { status, code, type, message: err?.message, data, baseUrl, selectedModel, disableTools });
            if (trace && err?.stack)
                console.error(`LLM error (second) stack: ${err.stack}`);
            throw err;
        }
        console.log('\nSecond Call Output:');
        console.log(JSON.stringify(secondResponse.choices[0]?.message, null, 2));
        // Calculate totals from cost breakdown
        totalInputTokens = costBreakdown.reduce((sum, call) => sum + call.inputTokens, 0);
        totalOutputTokens = costBreakdown.reduce((sum, call) => sum + call.outputTokens, 0);
        totalCost = costBreakdown.reduce((sum, call) => sum + call.cost, 0);
        console.log('\n=== BOTH CALLS COMPLETE ===');
        // Log detailed timing and cost information
        console.log('\n=== DETAILED METRICS ===');
        costBreakdown.forEach((call, index) => {
            console.log(`\n${call.callType.toUpperCase()} CALL ${index + 1}:`);
            console.log(`  Execution Time: ${call.executionTime}ms`);
            console.log(`  Start Time: ${new Date(call.startTime).toISOString()}`);
            console.log(`  End Time: ${new Date(call.endTime).toISOString()}`);
            console.log(`  Input Tokens: ${call.inputTokens}`);
            console.log(`  Output Tokens: ${call.outputTokens}`);
            console.log(`  Cost: $${call.cost.toFixed(6)}`);
            console.log(`  Model: ${call.model}`);
        });
        console.log(`\nTOTAL METRICS:`);
        console.log(`  Total LLM Execution Time: ${costBreakdown.reduce((sum, call) => sum + call.executionTime, 0)}ms`);
        console.log(`  Total Tool Execution Time: ${executionDetails.reduce((sum, detail) => sum + detail.executionTime, 0)}ms`);
        console.log(`  Total Cost: $${totalCost.toFixed(6)}`);
        console.log(`  Total Input Tokens: ${totalInputTokens}`);
        console.log(`  Total Output Tokens: ${totalOutputTokens}`);
        return new types_1.LlmResponseResult(secondResponse.choices[0]?.message?.content || '', first.tool_calls || [], toolOutputs, executionDetails, {
            model: selectedModel,
            totalInputTokens,
            totalOutputTokens,
            totalCost,
            costBreakdown
        });
    }
}
exports.LlmApi = LlmApi;
//# sourceMappingURL=llm-api.js.map