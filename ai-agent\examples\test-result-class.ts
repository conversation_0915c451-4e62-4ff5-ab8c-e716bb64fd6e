/**
 * Test file to demonstrate the new LlmResponseResult class with execution tracking and cost tracking
 */

import { config } from 'dotenv';
import { LlmResponseResult, ToolOutput, ToolExecutionDetail, LlmCostInfo, LlmCallCost } from '../src/types';
import type { ChatCompletionMessageToolCall } from 'openai/resources/chat/completions';

config();

function testLlmResponseResult() {
  console.log('Testing LlmResponseResult class with execution tracking and cost tracking...\n');

  // Create mock tool calls
  const mockToolCalls: ChatCompletionMessageToolCall[] = [
    {
      id: 'call_1',
      type: 'function',
      function: {
        name: 'calculator',
        arguments: '{"operation": "add", "a": 5, "b": 3}'
      }
    },
    {
      id: 'call_2',
      type: 'function',
      function: {
        name: 'search',
        arguments: '{"query": "weather"}'
      }
    }
  ];

  // Create mock tool outputs
  const mockToolOutputs: ToolOutput[] = [
    {
      role: 'tool',
      content: '8',
      tool_call_id: 'call_1'
    },
    {
      role: 'tool',
      content: 'Sunny, 25°C',
      tool_call_id: 'call_2'
    }
  ];

  // Create mock execution details
  const mockExecutionDetails: ToolExecutionDetail[] = [
    {
      toolCallId: 'call_1',
      toolName: 'calculator',
      executionTime: 150,
      success: true,
      startTime: Date.now() - 150,
      endTime: Date.now()
    },
    {
      toolCallId: 'call_2',
      toolName: 'search',
      executionTime: 300,
      success: false,
      errorMessage: 'API rate limit exceeded',
      startTime: Date.now() - 300,
      endTime: Date.now()
    }
  ];

  // Create mock cost information
  const mockLlmCosts: LlmCostInfo = {
    model: 'gpt-4o-mini',
    totalInputTokens: 1250,
    totalOutputTokens: 450,
    totalCost: 0.005125,
    costBreakdown: [
      {
        callType: 'initial',
        inputTokens: 800,
        outputTokens: 200,
        cost: 0.002,
        model: 'gpt-4o-mini'
      },
      {
        callType: 'followup',
        inputTokens: 450,
        outputTokens: 250,
        cost: 0.003125,
        model: 'gpt-4o-mini'
      }
    ]
  };

  // Create a result instance
  const result = new LlmResponseResult(
    'The calculation result is 8 and the weather search failed due to rate limiting.',
    mockToolCalls,
    mockToolOutputs,
    mockExecutionDetails,
    mockLlmCosts
  );

  // Test all the methods and properties
  console.log('Message Response:', result.messageResponse);
  console.log('Content (getter):', result.content);
  console.log('Has Tool Calls:', result.hasToolCalls);
  console.log('Tool Calls Count:', result.calls.length);
  console.log('Tool Outputs Count:', result.outputs.length);
  console.log('Execution Details Count:', result.executionDetails.length);
  
  console.log('\nTool Calls:');
  result.calls.forEach((call, index) => {
    console.log(`  ${index + 1}. ${call.function?.name}: ${call.function?.arguments}`);
  });

  console.log('\nTool Outputs:');
  result.outputs.forEach((output, index) => {
    console.log(`  ${index + 1}. ${output.tool_call_id}: ${output.content}`);
  });

  console.log('\nExecution Details:');
  result.executionDetails.forEach((detail, index) => {
    console.log(`  ${index + 1}. ${detail.toolName}:`);
    console.log(`     Success: ${detail.success}`);
    console.log(`     Execution Time: ${detail.executionTime}ms`);
    if (detail.errorMessage) {
      console.log(`     Error: ${detail.errorMessage}`);
    }
    console.log(`     Start: ${new Date(detail.startTime).toISOString()}`);
    console.log(`     End: ${new Date(detail.endTime).toISOString()}`);
  });

  console.log('\nCost Information:');
  console.log(`Model: ${result.llmCosts.model}`);
  console.log(`Total Input Tokens: ${result.totalInputTokens.toLocaleString()}`);
  console.log(`Total Output Tokens: ${result.totalOutputTokens.toLocaleString()}`);
  console.log(`Total Cost: $${result.totalCost.toFixed(6)}`);
  
  console.log('\nCost Breakdown:');
  result.llmCosts.costBreakdown.forEach((call, index) => {
    console.log(`  ${index + 1}. ${call.callType} call:`);
    console.log(`     Input: ${call.inputTokens} tokens`);
    console.log(`     Output: ${call.outputTokens} tokens`);
    console.log(`     Cost: $${call.cost.toFixed(6)}`);
  });

  console.log('\nFinding specific tool calls:');
  const calculatorCall = result.getToolCallByName('calculator');
  console.log('Calculator call:', calculatorCall ? 'Found' : 'Not found');
  
  const searchCall = result.getToolCallByName('search');
  console.log('Search call:', searchCall ? 'Found' : 'Not found');

  console.log('\nFinding tool outputs by ID:');
  const output1 = result.getToolOutputById('call_1');
  console.log('Output for call_1:', output1?.content);
  
  const output2 = result.getToolOutputById('call_2');
  console.log('Output for call_2:', output2?.content);

  console.log('\nFinding execution details by ID:');
  const detail1 = result.getExecutionDetail('call_1');
  console.log('Execution detail for call_1:', detail1 ? `${detail1.executionTime}ms, ${detail1.success ? 'success' : 'failed'}` : 'Not found');
  
  const detail2 = result.getExecutionDetail('call_2');
  console.log('Execution detail for call_2:', detail2 ? `${detail2.executionTime}ms, ${detail2.success ? 'success' : 'failed'}` : 'Not found');

  console.log('\nExecution Summary:');
  console.log('Successful executions:', result.successfulExecutions.length);
  console.log('Failed executions:', result.failedExecutions.length);
  console.log('Total execution time:', result.totalExecutionTime, 'ms');

  console.log('\n✅ LlmResponseResult class with execution tracking and cost tracking test completed successfully!');
}

// Run the test
testLlmResponseResult();
