import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
// import { trpc } from '../../utils/trpc'; // tRPC removed, using REST API instead
import { useAuth } from '../../context/AuthContext';
import { useStore } from '../../context/StoreContext';
import TopTaskBar from '../../components/TopTaskBar';
import SideTaskBar from '../../components/SideTaskBar';

interface Conversation {
  id: string;
  uuid: string;
  title: string | null;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    email: string;
    name: string | null;
  };
  store: {
    id: string;
    name: string;
    description: string | null;
  };
  _count?: {
    messages: number;
  };
}

export default function ConversationsListPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { currentStoreId } = useStore();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for conversation info modal
  const [showConversationModal, setShowConversationModal] = useState(false);
  const [newConversation, setNewConversation] = useState<Conversation | null>(null);

  // Fetch conversations
  const fetchConversations = async () => {
    if (!user || !currentStoreId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'}/conversations?page=1&limit=50`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversations');
      }

      const data = await response.json();
      setConversations(data.data || []);
    } catch (err) {
      console.error('Error fetching conversations:', err);
      setError('Failed to load conversations');
    } finally {
      setIsLoading(false);
    }
  };

  // Create new conversation
  const createConversation = async () => {
    if (!user || !currentStoreId) {
      setError('User or store not available');
      return;
    }

    // Show modal immediately with loading state
    setShowConversationModal(true);
    setNewConversation(null);
    setError(null);
    setIsCreating(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'}/conversations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          title: 'New Conversation',
          userId: BigInt(user.id),
          storeId: BigInt(currentStoreId),
          createdBy: BigInt(user.id),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create conversation');
      }

      const newConversationData = await response.json();
      await fetchConversations(); // Refresh the list

      // Set the new conversation for the modal
      setNewConversation(newConversationData);
    } catch (err) {
      console.error('Error creating conversation:', err);
      setError('Failed to create conversation');
      // Hide modal on error
      setShowConversationModal(false);
      setNewConversation(null);
    } finally {
      setIsCreating(false);
    }
  };

  // Go live functionality
  const goLiveConversation = async (conversationUuid: string) => {
    try {
      // Open the conversation in live mode in a new tab
      window.open(`/conversations/${conversationUuid}?mode=live`, '_blank');
    } catch (err) {
      console.error('Error going live:', err);
      setError('Failed to start live conversation');
    }
  };

  // Function to copy live link to clipboard
  const copyLiveLink = async (conversationUuid: string) => {
    try {
      const url = `${window.location.origin}/conversations/${conversationUuid}?mode=live`;
      await navigator.clipboard.writeText(url);
      alert('Live link copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy live link:', err);
      alert('Failed to copy live link.');
    }
  };

  // Load conversations on component mount
  useEffect(() => {
    if (user && currentStoreId) {
      fetchConversations();
    }
  }, [user, currentStoreId]);

  if (!user) {
    return (
      <>
        <Head>
          <title>Authentication Required - Teno Store</title>
          <meta name="description" content="Please log in to view your conversations" />
        </Head>

        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
          <div className="text-center max-w-md w-full">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="mb-4">
                <svg className="h-12 w-12 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">Authentication Required</h2>
              <p className="text-gray-600 mb-6">You need to be logged in to view conversations.</p>
              <button
                className="w-full bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
                onClick={() => router.push('/login')}
              >
                Go to Login
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Conversations - Teno Store</title>
        <meta name="description" content="Browse and manage your agent conversations" />
      </Head>

      <div className="min-h-screen bg-slate-900">
        <TopTaskBar />
        <SideTaskBar />

        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-extrabold tracking-tight text-slate-100">Agent Conversations</h1>
                <p className="mt-2 text-slate-300">Review and resume past or active conversations.</p>
              </div>
              <div className="flex gap-3">
                <Link
                  href="/conversations/debug"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                  Debug Tool
                </Link>
                <button
                  onClick={createConversation}
                  disabled={isCreating || !currentStoreId}
                  className="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {isCreating ? (
                    <>
                      <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating...
                    </>
                  ) : (
                    <>
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      New Conversation
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="bg-slate-800 rounded-lg shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-100 mb-4">Conversations</h2>

            {error && (
              <div className="mb-4 p-4 bg-red-900/50 border border-red-700 rounded-lg">
                <p className="text-red-200">{error}</p>
              </div>
            )}

            {!currentStoreId && (
              <div className="mb-4 p-4 bg-yellow-900/50 border border-yellow-700 rounded-lg">
                <p className="text-yellow-200">Please select a store to view conversations.</p>
              </div>
            )}

            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <svg className="animate-spin h-8 w-8 text-slate-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="ml-3 text-slate-300">Loading conversations...</span>
              </div>
            ) : conversations.length === 0 ? (
              <div className="text-center py-8">
                <svg className="h-12 w-12 text-slate-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p className="text-slate-400 mb-4">No conversations yet.</p>
                <button
                  onClick={createConversation}
                  className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
                >
                  Start your first conversation
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {conversations.map((conversation) => (
                  <div key={conversation.uuid} className="bg-slate-700 rounded-lg p-4 hover:bg-slate-600 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <Link href={`/conversations/${conversation.uuid}`} className="block">
                          <h3 className="text-lg font-medium text-slate-100 hover:text-emerald-400 transition-colors">
                            {conversation.title || 'Untitled Conversation'}
                          </h3>
                        </Link>
                        <div className="mt-2 space-y-1">
                          <p className="text-sm text-slate-400">
                            Created: {new Date(conversation.createdAt).toLocaleDateString()} at {new Date(conversation.createdAt).toLocaleTimeString()}
                          </p>
                          <p className="text-sm text-slate-400">
                            Store: {conversation.store.name}
                          </p>
                          {conversation._count && (
                            <p className="text-sm text-slate-400">
                              Messages: {conversation._count.messages}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Link
                          href={`/conversations/${conversation.uuid}`}
                          className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center gap-1"
                        >
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          View
                        </Link>
                        <button
                          onClick={() => goLiveConversation(conversation.uuid)}
                          className="bg-emerald-600 text-white px-3 py-2 rounded-lg hover:bg-emerald-700 transition-colors text-sm flex items-center gap-1"
                        >
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-7 4h12m-1 4H6a2 2 0 01-2-2V6a2 2 0 012-2h8l4 4v10a2 2 0 01-2 2z" />
                          </svg>
                          Go Live
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Conversation Info Modal */}
        {showConversationModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-slate-800 rounded-lg shadow-lg p-6 max-w-2xl w-full mx-4">
              {isCreating ? (
                // Loading State
                <div className="text-center py-8">
                  <div className="animate-spin h-12 w-12 text-emerald-400 mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-slate-100 mb-2">Creating Conversation...</h3>
                  <p className="text-slate-300">Please wait while we set up your new conversation.</p>
                </div>
              ) : newConversation ? (
                // Success State
                <>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-slate-100">New Conversation Created!</h3>
                    <button
                      onClick={() => setShowConversationModal(false)}
                      className="text-slate-400 hover:text-slate-200 transition-colors"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  
                  {/* Conversation Information */}
                  <div className="bg-slate-700 rounded-lg p-4 mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-slate-300">Title:</span>
                        <span className="ml-2 text-slate-100">{newConversation.title || 'Untitled'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">UUID:</span>
                        <span className="ml-2 text-slate-100 font-mono text-xs">{newConversation.uuid}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">Store:</span>
                        <span className="ml-2 text-slate-100">{newConversation.store?.name || 'Unknown'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">Created:</span>
                        <span className="ml-2 text-slate-100">{new Date(newConversation.createdAt).toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">User:</span>
                        <span className="ml-2 text-slate-100">{newConversation.user?.name || newConversation.user?.email || 'Unknown'}</span>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-3 mb-4">
                    <button
                      onClick={() => {
                        if (newConversation.uuid) {
                          window.open(`/conversations/${newConversation.uuid}?mode=live`, '_blank');
                        }
                      }}
                      className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-emerald-600 text-white text-sm font-medium rounded-md hover:bg-emerald-700 transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-7 4h12m-7-8h1a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V8a2 2 0 012-2h1m4 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v1" />
                      </svg>
                      Go Live
                    </button>
                    <button
                      onClick={() => copyLiveLink(newConversation.uuid)}
                      className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2M15 21h6M3 10h18" />
                      </svg>
                      Copy Live Link
                    </button>
                  </div>

                  <div className="text-center">
                    <button
                      onClick={() => setShowConversationModal(false)}
                      className="text-slate-400 hover:text-slate-200 transition-colors text-sm"
                    >
                      Close
                    </button>
                  </div>
                </>
              ) : null}
            </div>
          </div>
        )}
      </div>
    </>
  );
}
