# Backend Standards & Best Practices

This document outlines the standards and best practices for the Teno Store backend API.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [API Response Format](#api-response-format)
3. [Error Handling](#error-handling)
4. [Validation](#validation)
5. [Authentication & Authorization](#authentication--authorization)
6. [Middleware](#middleware)
7. [Controllers](#controllers)
8. [Database Operations](#database-operations)
9. [Logging](#logging)
10. [Testing](#testing)
11. [Security](#security)
12. [Performance](#performance)

## Architecture Overview

The backend follows a layered architecture pattern:

```
API Routes → Controllers → Services → Repositories → Database
     ↓           ↓          ↓           ↓
  Middleware → Validation → Business Logic → Data Access
```

### Key Components

- **API Routes**: Next.js API routes in `/pages/api/`
- **Controllers**: Handle HTTP requests/responses and orchestrate operations
- **Services**: Business logic layer
- **Repositories**: Data access layer using TypeORM
- **Middleware**: Cross-cutting concerns (CORS, auth, validation)

## API Response Format

All API responses follow a consistent format:

### Success Response
```typescript
{
  "success": true,
  "data": { ... },
  "meta": { ... } // Optional pagination metadata
}
```

### Error Response
```typescript
{
  "success": false,
  "error": "Error message",
  "details": { ... } // Optional validation details
}
```

### Pagination Metadata
```typescript
{
  "page": 1,
  "limit": 20,
  "total": 100,
  "totalPages": 5,
  "hasNext": true,
  "hasPrev": false
}
```

## Error Handling

### Error Classes
Use custom error classes for different error types:

```typescript
import { ValidationError, NotFoundError, UnauthorizedError } from '../controllers/base-controller';

// Validation errors
throw new ValidationError('Invalid input data', validationDetails);

// Not found errors
throw new NotFoundError('Resource not found');

// Authorization errors
throw new UnauthorizedError('Access denied');
```

### Error Response Codes
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing/invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `405` - Method Not Allowed (unsupported HTTP method)
- `500` - Internal Server Error (server-side errors)

## Validation

### Request Validation
Use Zod schemas for request validation:

```typescript
import { z } from 'zod';

const createUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  name: z.string().min(1, 'Name is required'),
  age: z.number().min(18, 'Must be 18 or older').optional()
});
```

### Query Parameter Validation
```typescript
const paginationSchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).default('1'),
  limit: z.string().transform(val => parseInt(val, 10)).default('20')
});
```

## Authentication & Authorization

### JWT Token Structure
```typescript
interface JWTPayload {
  userId: string;
  email: string;
  exp: number;
  iat: number;
}
```

### Protected Routes
```typescript
import { auth } from '../auth/middleware';

// Apply authentication middleware
const handler = withCors(
  withErrorHandling(
    auth(
      async (req: AuthenticatedRequest, res: NextApiResponse) => {
        // req.user is now available
        const userId = req.user.id;
      }
    )
  )
);
```

### Optional Authentication
```typescript
import { optionalAuth } from '../auth/middleware';

// Apply optional authentication
const handler = withCors(
  withErrorHandling(
    optionalAuth(
      async (req: AuthenticatedRequest, res: NextApiResponse) => {
        // req.user may or may not be available
        if (req.user) {
          // User is authenticated
        }
      }
    )
  )
);
```

## Middleware

### CORS Middleware
```typescript
import { withCors } from '../middleware/api-middleware';

const handler = withCors(async (req, res) => {
  // CORS headers automatically applied
});
```

### Error Handling Middleware
```typescript
import { withErrorHandling } from '../middleware/api-middleware';

const handler = withErrorHandling(async (req, res) => {
  // Errors automatically caught and formatted
});
```

### Method Validation Middleware
```typescript
import { withMethodValidation } from '../middleware/api-middleware';

const handler = withMethodValidation(['GET', 'POST'])(
  async (req, res) => {
    // Only GET and POST methods allowed
  }
);
```

### Middleware Composition
```typescript
import { composeMiddleware } from '../middleware/api-middleware';

const handler = composeMiddleware(
  withCors,
  withErrorHandling,
  withMethodValidation(['GET', 'POST'])
)(async (req, res) => {
  // All middleware applied in order
});
```

## Controllers

### Base Controller
Extend the `BaseController` class for consistent behavior:

```typescript
import { BaseController } from '../controllers/base-controller';

export class UserController extends BaseController {
  constructor() {
    super({
      entityName: 'User',
      allowedMethods: ['GET', 'POST', 'PUT', 'DELETE']
    });
  }

  protected async handleGet(req: NextApiRequest, res: NextApiResponse): Promise<void> {
    // Implementation
  }

  protected async handlePost(req: NextApiRequest, res: NextApiResponse): Promise<void> {
    // Implementation
  }

  // ... other methods
}
```

### Controller Methods
- `handleGet()` - Handle GET requests
- `handlePost()` - Handle POST requests
- `handlePut()` - Handle PUT requests
- `handleDelete()` - Handle DELETE requests

### Response Helpers
```typescript
// Success response
this.sendSuccess(res, data, 201);

// Error response
this.sendError(res, 'Not found', 404);

// Paginated response
this.sendPaginated(res, items, page, limit, total);
```

## Database Operations

### TypeORM Best Practices
```typescript
// Use repositories
const userRepo = await getRepository('User');

// Include relations when needed
const user = await userRepo.findOne({
  where: { id: userId },
  relations: ['profile', 'orders'],
  select: ['id', 'email', 'name']
});

// Handle BigInt values
import { safeCountResult } from '../utils/serialization';
const total = safeCountResult(await repo.count(where));
```

### Transaction Handling
```typescript
const queryRunner = AppDataSource.createQueryRunner();
await queryRunner.connect();
await queryRunner.startTransaction();

try {
  // Database operations
  await queryRunner.commitTransaction();
} catch (err) {
  await queryRunner.rollbackTransaction();
  throw err;
} finally {
  await queryRunner.release();
}
```

## Logging

### Logger Usage
```typescript
import { log, error, warn, info } from '../utils/logger';

log('User created successfully', { userId, email });
error('Database connection failed', err);
warn('Rate limit approaching', { current, limit });
info('Application started', { version, environment });
```

### Log Levels
- `log()` - General information
- `error()` - Error conditions
- `warn()` - Warning conditions
- `info()` - Informational messages
- `debug()` - Debug information

## Testing

### Test Structure
```typescript
import { describe, it, expect, beforeEach } from 'vitest';

describe('UserController', () => {
  let controller: UserController;

  beforeEach(() => {
    controller = new UserController();
  });

  it('should create user successfully', async () => {
    // Test implementation
  });
});
```

### Mocking
```typescript
import { vi } from 'vitest';

// Mock services
vi.mock('../services/user.service');
vi.mock('../auth/middleware');
```

## Security

### Input Sanitization
- Validate all inputs using Zod schemas
- Sanitize user-generated content
- Use parameterized queries (TypeORM handles this)

### Rate Limiting
```typescript
// TODO: Implement rate limiting middleware
const rateLimit = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
};
```

### CORS Configuration
```typescript
const corsConfig = {
  allowedOrigin: process.env.FRONTEND_URL || 'http://localhost:3000',
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};
```

## Performance

### Database Optimization
- Use proper indexes
- Limit result sets with pagination
- Select only needed fields
- Use relations sparingly

### Caching Strategy
```typescript
// TODO: Implement Redis caching
const cacheKey = `user:${userId}`;
const cachedUser = await redis.get(cacheKey);
if (cachedUser) {
  return JSON.parse(cachedUser);
}
```

### Response Optimization
- Compress responses
- Use appropriate HTTP status codes
- Implement ETags for caching
- Consider GraphQL for complex queries

## Code Examples

### Complete Endpoint Example
```typescript
// pages/api/users/index.ts
import { withCors, withErrorHandling, withMethodValidation } from '../../../middleware/api-middleware';
import { UserController } from '../../../controllers/user-controller';

const userController = new UserController();

const handler = withCors(
  withErrorHandling(
    withMethodValidation(['GET', 'POST'])(
      async (req: NextApiRequest, res: NextApiResponse) => {
        await userController.handle(req, res);
      }
    )
  )
);

export default handler;
```

### Controller Implementation
```typescript
export class UserController extends BaseController {
  constructor() {
    super({
      entityName: 'User',
      allowedMethods: ['GET', 'POST']
    });
  }

  protected async handleGet(req: NextApiRequest, res: NextApiResponse): Promise<void> {
    const query = this.validateQuery(req, paginationSchema);
    const { page, limit } = query;

    const { users, total } = await this.userService.getUsers(page, limit);
    this.sendPaginated(res, users, page, limit, total);
  }

  protected async handlePost(req: NextApiRequest, res: NextApiResponse): Promise<void> {
    const body = this.validateBody(req, createUserSchema);
    const user = await this.userService.createUser(body);
    this.sendSuccess(res, user, 201);
  }
}
```

## Migration Guide

### From Old Pattern to New Pattern

1. **Replace manual CORS handling** with `withCors` middleware
2. **Replace manual error handling** with `withErrorHandling` middleware
3. **Replace manual method validation** with `withMethodValidation` middleware
4. **Replace manual validation** with controller validation methods
5. **Replace manual response formatting** with controller response helpers

### Example Migration

**Before:**
```typescript
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Manual CORS
  res.setHeader('Access-Control-Allow-Origin', allowedOrigin);
  
  try {
    // Manual validation
    const body = schema.parse(req.body);
    
    // Manual response
    res.status(200).json(data);
  } catch (err) {
    // Manual error handling
    res.status(500).json({ error: 'Internal error' });
  }
}
```

**After:**
```typescript
const handler = withCors(
  withErrorHandling(
    withValidation(schema)(
      async (req: NextApiRequest, res: NextApiResponse) => {
        const controller = new UserController();
        await controller.handle(req, res);
      }
    )
  )
);
```

## Best Practices Summary

1. **Always use middleware composition** for common concerns
2. **Extend BaseController** for consistent behavior
3. **Validate all inputs** using Zod schemas
4. **Use consistent response formats** via response helpers
5. **Handle errors consistently** using custom error classes
6. **Log appropriately** using the enhanced logger
7. **Follow TypeScript best practices** and enable strict mode
8. **Implement proper authentication** for protected routes
9. **Use transactions** for multi-step database operations
10. **Test thoroughly** with unit and integration tests

## Next Steps

1. Gradually migrate existing endpoints to use the new pattern
2. Enable strict TypeScript mode (`tsconfig.strict.json`)
3. Implement comprehensive testing suite
4. Add API documentation using OpenAPI/Swagger
5. Implement monitoring and observability
6. Add performance monitoring and metrics
