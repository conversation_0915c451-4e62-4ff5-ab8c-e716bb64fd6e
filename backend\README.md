# Teno Store Backend - NestJS

This is the NestJS version of the Teno Store backend API, migrated from the original Next.js implementation.

## Features

- **Authentication**: JWT-based authentication with Google OAuth support
- **User Management**: Complete user CRUD operations
- **Store Management**: Multi-store support with user ownership
- **Product Management**: Product catalog with inventory tracking
- **Customer Management**: Customer database and relationships
- **Order Management**: Order processing and tracking
- **Conversation System**: AI-powered conversation management
- **Agent System**: AI agent configuration and management
- **Image Upload**: File upload and processing with Sharp
- **Tool Calls**: AI tool execution tracking
- **Swagger Documentation**: Auto-generated API documentation

## Tech Stack

- **Framework**: NestJS 10.x
- **Database**: PostgreSQL with TypeORM
- **Authentication**: Passport.js with JWT and Google OAuth
- **Validation**: Class-validator and class-transformer
- **Documentation**: Swagger/OpenAPI
- **File Processing**: Sharp for image optimization
- **Testing**: Jest

## Prerequisites

- Node.js 18+ 
- PostgreSQL 12+
- npm or yarn

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd teno-store/backend_nest
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   # Create PostgreSQL database
   createdb teno_store
   
   # Run migrations (if available)
   npm run db:migrate
   ```

5. **Build the project**
   ```bash
   npm run build
   ```

## Running the Application

### Development Mode
```bash
npm run start:dev
```

### Production Mode
```bash
npm run build
npm run start:prod
```

### Debug Mode
```bash
npm run start:debug
```



## Authentication

The application supports multiple authentication methods:

1. **JWT Authentication**: Token-based authentication for API access
2. **Google OAuth**: Social login via Google
3. **Local Authentication**: Email/password authentication

### Protected Routes

Most API endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | PostgreSQL host | `localhost` |
| `DB_PORT` | PostgreSQL port | `5432` |
| `DB_USER` | Database username | `postgres` |
| `DB_PASSWORD` | Database password | - |
| `DB_NAME` | Database name | `teno_store` |
| `JWT_SECRET` | JWT signing secret | - |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | - |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | - |
| `PORT` | Application port | `8000` |
| `NODE_ENV` | Environment | `development` |
| `FRONTEND_URL` | Frontend URL for CORS | `http://localhost:3000` |



## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.
