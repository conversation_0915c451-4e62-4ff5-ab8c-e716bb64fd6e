# Logger Functions

## Import
```typescript
import { log, error, warn, info, debug } from '../utils/logger';
// or
import logger from '../utils/logger';
```

## Functions
- `log(message, ...args)` - console.log with file:line
- `error(message, ...args)` - console.error with file:line  
- `warn(message, ...args)` - console.warn with file:line
- `info(message, ...args)` - console.info with file:line
- `debug(message, ...args)` - console.debug with file:line
- `time(label)` / `timeEnd(label)` - performance timing
- `group(label)` / `groupEnd()` - grouped logging

## Usage
```typescript
log('User authenticated:', userId);
error('Database failed:', error);
logger.warn('Deprecated endpoint');
```

## Output
```
[LOG] filename.ts:45 - User authenticated: user123
[ERROR] filename.ts:52 - Database failed: connection error
```
