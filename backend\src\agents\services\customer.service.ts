import { CustomersService } from '../../customers/customers.service';
import { Customer } from '../../customers/customer.entity';
import { Repository } from 'typeorm';

export interface CustomerFilter {
  storeId?: string | bigint;
  email?: string;
  name?: string;
  phone?: string;
}

export interface CreateCustomerInput {
  email?: string;
  name: string;
  phone?: string;
  address?: string;
  storeId: string | bigint;
  createdBy?: string;
}

/**
 * Filter customers based on provided criteria
 */
export async function filterCustomers(filter: CustomerFilter, db: any): Promise<Customer[]> {
  const customerRepo = db.getRepository('Customer') as Repository<Customer>;
  
  const where: any = { isDeleted: false };
  
  if (filter.storeId) {
    where.storeId = filter.storeId.toString();
  }
  
  if (filter.email) {
    where.email = filter.email;
  }
  
  if (filter.name) {
    // Use LIKE for partial name matching
    where.name = filter.name;
  }
  
  if (filter.phone) {
    where.phone = filter.phone;
  }
  
  return customerRepo.find({
    where,
    order: { createdAt: 'DESC' }
  });
}

/**
 * Create a new customer
 */
export async function createCustomer(input: CreateCustomerInput, db: any): Promise<Customer> {
  const customerRepo = db.getRepository('Customer') as Repository<Customer>;
  
  const customer = customerRepo.create({
    name: input.name,
    email: input.email,
    phone: input.phone,
    address: input.address,
    storeId: input.storeId.toString(),
    createdBy: input.createdBy,
  });
  
  return customerRepo.save(customer);
}

/**
 * Update an existing customer
 */
export async function updateCustomer(customerId: string, updateData: Partial<Customer>, db: any): Promise<Customer> {
  const customerRepo = db.getRepository('Customer') as Repository<Customer>;
  
  const customer = await customerRepo.findOne({
    where: { id: customerId, isDeleted: false }
  });
  
  if (!customer) {
    throw new Error(`Customer with ID ${customerId} not found`);
  }
  
  Object.assign(customer, updateData);
  return customerRepo.save(customer);
}
