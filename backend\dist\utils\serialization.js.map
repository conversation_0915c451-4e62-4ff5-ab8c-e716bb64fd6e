{"version": 3, "file": "serialization.js", "sourceRoot": "", "sources": ["../../src/utils/serialization.ts"], "names": [], "mappings": ";;AAUA,4CAEC;AAMD,8CAEC;AAMD,0CAmBC;AAMD,0CAYC;AAKD,wCAcC;AAKD,0CAEC;AAKD,wCA+BC;AAKD,0CAEC;AAKD,4CAaC;AAKD,8CAEC;AAKD,8CAYC;AAKD,gDAEC;AAKD,wCAwBC;AAKD,0CAEC;AAKD,sDAuBC;AAKD,wDAEC;AAxPD,qDAA0E;AAM1E,SAAgB,gBAAgB,CAAI,GAAM;IACxC,OAAO,IAAA,oCAAmB,EAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAMD,SAAgB,iBAAiB,CAAI,GAAM;IACzC,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC;AAMD,SAAgB,eAAe,CAAI,MAAS;IAC1C,IAAI,CAAC;QAEH,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC,MAAM,CAAiB,CAAC;QACxC,CAAC;QAGD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO,MAAM,CAAC;IAChB,CAAC;AACH,CAAC;AAMD,SAAgB,eAAe,CAAC,KAAU;IACxC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACnC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACpC,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAKD,SAAgB,cAAc,CAAC,KAAU;IACvC,IAAI,CAAC,KAAK;QAAE,OAAO,KAAK,CAAC;IAEzB,OAAO;QACL,GAAG,KAAK;QACR,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE;QACxB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE;QAChC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE;QACtC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE;QACtC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACnB,GAAG,KAAK,CAAC,KAAK;YACd,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE;SAC/B,CAAC,CAAC,CAAC,IAAI;KACT,CAAC;AACJ,CAAC;AAKD,SAAgB,eAAe,CAAC,MAAa;IAC3C,OAAO,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACpC,CAAC;AAKD,SAAgB,cAAc,CAAC,KAAU;IACvC,IAAI,CAAC,KAAK;QAAE,OAAO,KAAK,CAAC;IAEzB,OAAO;QACL,GAAG,KAAK;QACR,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE;QACxB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE;QAChC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE;QAClC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE;QACtC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE;QACtC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;YACjB,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE;YAC7B,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;YACrB,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;SACxB,CAAC,CAAC,CAAC,IAAI;QACR,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACnB,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE;YAC9B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI;YACtB,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI;SACvB,CAAC,CAAC,CAAC,IAAI;QACR,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;YACnC,EAAE,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,EAAE,QAAQ,EAAE;YACtC,IAAI,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI;YAC9B,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,KAAK;SACjC,CAAC,CAAC,CAAC,IAAI;QACR,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;YACnC,EAAE,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,EAAE,QAAQ,EAAE;YACtC,IAAI,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI;YAC9B,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,KAAK;SACjC,CAAC,CAAC,CAAC,IAAI;KACT,CAAC;AACJ,CAAC;AAKD,SAAgB,eAAe,CAAC,MAAa;IAC3C,OAAO,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACpC,CAAC;AAKD,SAAgB,gBAAgB,CAAC,OAAY;IAC3C,IAAI,CAAC,OAAO;QAAE,OAAO,OAAO,CAAC;IAE7B,OAAO;QACL,GAAG,OAAO;QACV,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE;QAC1B,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE;QAClC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE;QACpC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,QAAQ,EAAE;QACxC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,QAAQ,EAAE;QACxC,KAAK,EAAE,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK;QACpF,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;KAC5D,CAAC;AACJ,CAAC;AAKD,SAAgB,iBAAiB,CAAC,QAAe;IAC/C,OAAO,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AACxC,CAAC;AAKD,SAAgB,iBAAiB,CAAC,QAAa;IAC7C,IAAI,CAAC,QAAQ;QAAE,OAAO,QAAQ,CAAC;IAE/B,OAAO;QACL,GAAG,QAAQ;QACX,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE;QAC3B,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE;QACnC,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE,QAAQ,EAAE;QACrC,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE;QACzC,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE;QACzC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;KAC9D,CAAC;AACJ,CAAC;AAKD,SAAgB,kBAAkB,CAAC,SAAgB;IACjD,OAAO,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAC1C,CAAC;AAKD,SAAgB,cAAc,CAAC,KAAU;IACvC,IAAI,CAAC,KAAK;QAAE,OAAO,KAAK,CAAC;IAEzB,OAAO;QACL,GAAG,KAAK;QACR,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE;QACxB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE;QAChC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE;QAClC,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE;QACxC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE;QACtC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE;QACtC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QACvD,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;QACnE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;YACnD,GAAG,IAAI;YACP,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE;YACrC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtB,GAAG,IAAI,CAAC,OAAO;gBACf,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE;aAChC,CAAC,CAAC,CAAC,IAAI;SACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACT,CAAC;AACJ,CAAC;AAKD,SAAgB,eAAe,CAAC,MAAa;IAC3C,OAAO,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACpC,CAAC;AAKD,SAAgB,qBAAqB,CAAC,YAAiB;IACrD,IAAI,CAAC,YAAY;QAAE,OAAO,YAAY,CAAC;IAEvC,OAAO;QACL,GAAG,YAAY;QACf,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,QAAQ,EAAE;QAC/B,MAAM,EAAE,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE;QACvC,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE;QACzC,UAAU,EAAE,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE;QAC/C,SAAS,EAAE,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE;QAC7C,SAAS,EAAE,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE;QAE7C,SAAS,EAAE,YAAY,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI;QAClF,kBAAkB,EAAE,YAAY,CAAC,kBAAkB,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI;QAC7G,gBAAgB,EAAE,YAAY,CAAC,gBAAgB,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI;QACvG,iBAAiB,EAAE,YAAY,CAAC,iBAAiB,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI;QAC1G,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QACrE,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;YACxB,GAAG,YAAY,CAAC,IAAI;YACpB,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE;SACrC,CAAC,CAAC,CAAC,IAAI;QACR,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;KAClF,CAAC;AACJ,CAAC;AAKD,SAAgB,sBAAsB,CAAC,aAAoB;IACzD,OAAO,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AAClD,CAAC"}