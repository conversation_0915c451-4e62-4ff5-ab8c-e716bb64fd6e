{"version": 3, "file": "migrate.js", "sourceRoot": "", "sources": ["../../src/scripts/migrate.ts"], "names": [], "mappings": ";;;AAEA,qCAAqC;AACrC,mCAAgC;AAChC,+BAA4B;AAG5B,IAAA,eAAM,GAAE,CAAC;AAET,KAAK,UAAU,aAAa;IAC1B,MAAM,UAAU,GAAG,IAAI,oBAAU,CAAC;QAChC,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;QACxC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,CAAC;QAC7C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,UAAU;QAC3C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;QACvC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,eAAe;QAChD,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,yBAAyB,CAAC,CAAC;QACtD,UAAU,EAAE,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,0BAA0B,CAAC,CAAC;QACzD,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;KACnF,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,MAAM,iBAAiB,GAAG,MAAM,UAAU,CAAC,cAAc,EAAE,CAAC;QAC5D,IAAI,iBAAiB,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,MAAM,UAAU,CAAC,aAAa,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC9C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC5C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,aAAa,EAAE,CAAC"}