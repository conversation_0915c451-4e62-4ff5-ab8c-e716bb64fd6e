import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
// import { trpc } from '../../utils/trpc'; // tRPC removed, using REST API instead
import { useAuth } from '../../context/AuthContext';
import TopTaskBar from '../../components/TopTaskBar';
import SideTaskBar from '../../components/SideTaskBar';

export default function ConversationsListPage() {
  const router = useRouter();
  const { user } = useAuth();

  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const limit = 10;

  const userIdAsBigInt = useMemo(() => {
    if (!user?.id) return null;
    try {
      return BigInt(user.id);
    } catch {
      return null;
    }
  }, [user?.id]);

  if (!user) {
    return (
      <div>
        <h1>Authentication Required</h1>
        <p>Please log in to view conversations.</p>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Conversations - Teno Store</title>
        <meta name="description" content="Browse and manage your agent conversations" />
      </Head>

      <div className="min-h-screen bg-slate-900">
        <TopTaskBar />
        <SideTaskBar />

        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-extrabold tracking-tight text-slate-100">Agent Conversations</h1>
                <p className="mt-2 text-slate-300">Review and resume past or active conversations.</p>
              </div>
              <div className="flex gap-3">
                <Link
                  href="/conversations/debug"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                  Debug Tool
                </Link>
                <button className="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors">
                  New Conversation
                </button>
              </div>
            </div>
          </div>

          <div className="bg-slate-800 rounded-lg shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-100 mb-4">Conversations</h2>
            {isLoading ? (
              <p className="text-slate-300">Loading conversations...</p>
            ) : (
              <p className="text-slate-300">Conversations will be displayed here.</p>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
