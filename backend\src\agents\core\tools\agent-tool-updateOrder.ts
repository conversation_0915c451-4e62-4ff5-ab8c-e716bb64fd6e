import { createOrder, updateOrder } from '../../services/order.service';
import { OrderStatusService, OrderPriorityService } from '../../services/order.service';
import { resolveItemsAgainstCatalog } from './shared';

export async function updateOrderTool(
	params: {
		orderNumber: string;
		customerName?: string;
		customerEmail?: string;
		customerPhone?: string;
		itemsToAdd?: Array<{ productId: string | number | bigint; productName?: string; quantity: number; taxAmount?: number }>;
		itemsToRemove?: Array<{ productId: string | number | bigint; quantity?: number }>;
		removeAllItems?: boolean;
		replaceQuantities?: boolean; // New parameter: if true, replace quantities; if false, add to existing
		useTax?: boolean;
		taxRate?: number;
		priority?: 'low' | 'normal' | 'high' | 'urgent';
		status?: 'draft' | 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
		expectedDeliveryDate?: string;
		preferredDeliveryLocation?: string;
	},
	db: any,
	conversationUuid: string
) {
	// Validate database connection and get repositories
	if (!db || !db.isInitialized) {
		throw new Error('Database connection is not properly initialized');
	}

	const conversationRepo = db.getRepository('Conversation');
	const orderRepo = db.getRepository('Order');

	// Read conversation ownership to derive store/user context
	const convo = await conversationRepo.findOne({
		where: { uuid: conversationUuid, isDeleted: false },
		select: ['id', 'userId', 'storeId', 'createdBy', 'context'],
	});
	if (!convo) throw new Error('Conversation not found');

	// Check if order exists
	const existingOrder = await orderRepo.findOne({
		where: { orderNumber: params.orderNumber, storeId: convo.storeId, isDeleted: false },
	});

	if (!existingOrder) {
		throw new Error(`Order ${params.orderNumber} not found or does not belong to this store`);
	}

	// Check if order is in a modifiable state
	const modifiableStatuses = ['draft', 'pending', 'confirmed', 'processing'];
	if (!modifiableStatuses.includes(existingOrder.status?.toLowerCase())) {
		throw new Error(`Cannot modify order ${params.orderNumber} in status ${existingOrder.status}`);
	}

	// Prepare final items array
	let finalItems: any[] = [];

	// If removeAllItems is true, start with empty array
	if (params.removeAllItems) {
		finalItems = [];
	} else {
		// Start with existing items
		finalItems = existingOrder.items.map((item: any) => ({
			productId: item.productId,
			productName: item.productName,
			quantity: Number(item.quantity),
			unitPrice: item.unitPrice,
			taxAmount: item.taxAmount,
		}));
	}

	// Process items to remove (if not removing all)
	if (!params.removeAllItems && params.itemsToRemove && params.itemsToRemove.length > 0) {
		const removeMap = new Map();
		params.itemsToRemove.forEach((item) => {
			// Use only productId for identification
			const key = item.productId?.toString();
			if (key) {
				const existing = removeMap.get(key) || 0;
				removeMap.set(key, existing + (item.quantity || 0));
			}
		});

		// Apply removals using productId as the only key
		finalItems = finalItems.filter((item) => {
			// Use only productId for identification
			const key = item.productId?.toString();
			const removeQuantity = removeMap.get(key) || 0;
			
			if (removeQuantity === 0) {
				return true; // Keep item
			}
			
			if (removeQuantity >= item.quantity) {
				return false; // Remove item completely
			}
			
			// Reduce quantity
			item.quantity = item.quantity - removeQuantity;
			return item.quantity > 0; // Only keep if quantity is still positive
		});
	}

	// Process items to add
	if (params.itemsToAdd && params.itemsToAdd.length > 0) {
		let newItems: any[] = [];
		try {
			// Validate that each item has either productId or productName
			for (let i = 0; i < params.itemsToAdd.length; i++) {
				const item = params.itemsToAdd[i];
				if (!item.productId && !item.productName) {
					throw new Error(`Item ${i + 1}: Either productId or productName is required for proper identification`);
				}
			}
			
			const resolved = await resolveItemsAgainstCatalog(params.itemsToAdd, convo.storeId, db);
			newItems = resolved.map((it) => ({
				productId: it.productId as bigint,
				productName: it.productName,
				quantity: it.quantity,
				unitPrice: it.unitPrice,
				taxAmount: it.taxAmount,
			}));

			// Merge new items with existing items using productId as the only key
			const itemMap = new Map();
			
			// Add existing items to map using productId as key
			finalItems.forEach((item: any) => {
				if (item.productId) {
					itemMap.set(item.productId.toString(), { ...item });
				}
			});

			// Add new items, using replaceQuantities parameter to control behavior
			newItems.forEach((item: any) => {
				const productIdKey = item.productId.toString();
				
				if (itemMap.has(productIdKey)) {
					// Product already exists - check if we should replace or add quantities
					const existing = itemMap.get(productIdKey);
					const oldQuantity = existing.quantity;
					const newQuantity = params.replaceQuantities 
						? item.quantity  // Replace quantity
						: existing.quantity + item.quantity; // Add to existing quantity
					
					console.log(`[updateOrderTool] Merging item ${item.productName} (ID: ${productIdKey}): ${oldQuantity} + ${item.quantity} = ${newQuantity} (replaceQuantities: ${params.replaceQuantities})`);
					
					itemMap.set(productIdKey, {
						...existing,
						quantity: newQuantity,
						unitPrice: item.unitPrice, // Use new unit price
						taxAmount: item.taxAmount, // Use new tax amount
					});
				} else {
					// New product, add to map
					console.log(`[updateOrderTool] Adding new item ${item.productName} (ID: ${productIdKey}): quantity ${item.quantity}`);
					itemMap.set(productIdKey, { ...item });
				}
			});

			finalItems = Array.from(itemMap.values());
		} catch (error) {
			throw new Error(`Failed to resolve items to add: ${error}`);
		}
	}

	// Filter out any items with quantity 0 or negative (safety check)
	finalItems = finalItems.filter((item: any) => item.quantity > 0);

	// Prepare update data
	const updateData: any = {
		id: existingOrder.id,
		updatedBy: convo.createdBy as unknown as bigint,
	};

	// Add optional fields if provided
	if (params.customerName !== undefined) updateData.customerName = params.customerName;
	if (params.customerEmail !== undefined) updateData.customerEmail = params.customerEmail;
	if (params.customerPhone !== undefined) updateData.customerPhone = params.customerPhone;
	if (params.useTax !== undefined) updateData.useTax = params.useTax;
	if (params.taxRate !== undefined) updateData.taxRate = params.taxRate;
	if (params.expectedDeliveryDate !== undefined) updateData.expectedDeliveryDate = new Date(params.expectedDeliveryDate);
	if (params.preferredDeliveryLocation !== undefined) updateData.preferredDeliveryLocation = params.preferredDeliveryLocation;

	// Handle priority
	if (params.priority) {
		const priorityKey = params.priority.toUpperCase() as keyof typeof OrderPriorityService;
		if (priorityKey in OrderPriorityService) {
			updateData.priority = (OrderPriorityService as any)[priorityKey];
		}
	}

	// Handle status
	if (params.status) {
		const statusKey = params.status.toUpperCase() as keyof typeof OrderStatusService;
		if (statusKey in OrderStatusService) {
			updateData.status = (OrderStatusService as any)[statusKey];
		}
	}

	// Always include items (even if empty) to ensure proper recalculation
	updateData.items = finalItems;

	// Update the order
	const updatedOrder = await updateOrder(db, updateData);

	// Update conversation context with order info
	const currentCtx = await conversationRepo.findOne({
		where: { id: convo.id as unknown as bigint },
		select: ['context'],
	});

	if (currentCtx && currentCtx.context) {
		const updatedContext = {
			...currentCtx.context,
			order: {
				id: existingOrder.id.toString(),
				orderNumber: existingOrder.orderNumber,
				status: existingOrder.status,
				priority: existingOrder.priority,
				total: existingOrder.total,
				currency: existingOrder.currency,
				updatedAt: existingOrder.updatedAt,
			},
		};

		await conversationRepo.update(
			{ id: convo.id as unknown as bigint },
			{ context: updatedContext }
		);
	}

	// Prepare summary message
	let summaryMessage = `Order ${updatedOrder.orderNumber} updated successfully. `;
	
	if (params.removeAllItems) {
		summaryMessage += `All items removed. `;
	} else if (params.itemsToRemove && params.itemsToRemove.length > 0) {
		summaryMessage += `Items removed. `;
	}
	
	if (params.itemsToAdd && params.itemsToAdd.length > 0) {
		const behavior = params.replaceQuantities ? 'replaced' : 'added to';
		summaryMessage += `Items ${behavior} existing quantities. `;
	}
	
	summaryMessage += `New total: ${updatedOrder.total} ${updatedOrder.currency || 'USD'}`;

	return {
		message: summaryMessage,
		orderId: updatedOrder.id,
		orderNumber: updatedOrder.orderNumber,
		total: updatedOrder.total,
		status: updatedOrder.status,
		items: [], // orderItems relation would need to be loaded
		itemsAdded: params.itemsToAdd?.length || 0,
		itemsRemoved: params.removeAllItems ? 'all' : (params.itemsToRemove?.length || 0),
		totalItems: 0, // orderItems relation would need to be loaded
	};
}
