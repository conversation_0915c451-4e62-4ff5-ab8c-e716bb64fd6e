#!/usr/bin/env tsx
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const dotenv_1 = require("dotenv");
const path_1 = require("path");
const promises_1 = require("fs/promises");
(0, dotenv_1.config)();
async function checkMigrations() {
    const dataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        username: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'teno_store_db',
        synchronize: false,
        logging: false,
        entities: [(0, path_1.join)(__dirname, '../**/*.entity{.ts,.js}')],
        migrations: [(0, path_1.join)(__dirname, '../migrations/*{.ts,.js}')],
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    });
    try {
        await dataSource.initialize();
        console.log('✅ Database connection established\n');
        const pendingMigrations = await dataSource.showMigrations();
        const migrationsDir = (0, path_1.join)(__dirname, '../migrations');
        let availableMigrations = [];
        try {
            const files = await (0, promises_1.readdir)(migrationsDir);
            availableMigrations = files.filter(file => file.endsWith('.ts') || file.endsWith('.js')).map(file => file.replace(/\.(ts|js)$/, ''));
        }
        catch (error) {
            console.log('⚠️  Migrations directory not found or empty');
        }
        let executedMigrations = [];
        try {
            const queryRunner = dataSource.createQueryRunner();
            const result = await queryRunner.query(`
        SELECT name, timestamp 
        FROM migrations 
        ORDER BY timestamp ASC
      `);
            executedMigrations = result || [];
            await queryRunner.release();
        }
        catch (error) {
            console.log('⚠️  Could not retrieve executed migrations from database');
        }
        console.log('📊 Migration Status:');
        console.log('===================');
        console.log(`✅ Executed migrations: ${executedMigrations.length}`);
        console.log(`⏳ Pending migrations: ${pendingMigrations ? 'Yes' : 'No'}`);
        console.log(`📁 Available migrations: ${availableMigrations.length}\n`);
        if (executedMigrations.length > 0) {
            console.log('✅ Executed Migrations:');
            executedMigrations.forEach((migration, index) => {
                const timestamp = migration.timestamp ? new Date(parseInt(migration.timestamp)).toISOString() : 'Unknown';
                console.log(`   ${index + 1}. ${migration.name} (${timestamp})`);
            });
            console.log('');
        }
        if (availableMigrations.length > 0) {
            console.log('📁 Available Migrations:');
            availableMigrations.forEach((migration, index) => {
                const isExecuted = executedMigrations.some(em => em.name === migration);
                const status = isExecuted ? '✅' : '⏳';
                console.log(`   ${index + 1}. ${status} ${migration}`);
            });
            console.log('');
        }
        if (pendingMigrations) {
            console.log('🔄 Pending migrations detected!');
            console.log('   Run: npm run db:migrate');
        }
        else {
            console.log('✨ Database is up to date!');
        }
        await dataSource.destroy();
        console.log('\n✅ Database connection closed');
    }
    catch (error) {
        console.error('❌ Migration check failed:', error);
        process.exit(1);
    }
}
checkMigrations();
//# sourceMappingURL=check-migrations.js.map