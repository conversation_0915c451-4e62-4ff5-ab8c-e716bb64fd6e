/**
 * Example usage of the enhanced logger utility
 * This file demonstrates how to use the logger with file and line information
 */

import logger, { log, error, warn, info, debug } from './logger';

// Example function to demonstrate logging
function exampleFunction() {
  // Basic logging with file and line information
  log('This is a log message');
  error('This is an error message');
  warn('This is a warning message');
  info('This is an info message');
  debug('This is a debug message');
  
  // Logging with additional data
  log('User data:', { id: 123, name: '<PERSON>' });
  
  // Using the default logger object
  logger.log('Using default logger object');
  logger.error('Error with default logger');
  
  // Performance timing
  logger.time('database-query');
  // Simulate some work
  setTimeout(() => {
    logger.timeEnd('database-query');
  }, 100);
  
  // Grouped logging
  logger.group('API Request Details');
  logger.log('Method: GET');
  logger.log('URL: /api/users');
  logger.log('Status: 200');
  logger.groupEnd();
}

// Example async function
async function asyncExample() {
  try {
    logger.info('Starting async operation');
    // Simulate async work
    await new Promise(resolve => setTimeout(resolve, 100));
    logger.info('Async operation completed');
  } catch (err) {
    error('Async operation failed:', err);
  }
}

// Example with conditional logging
function conditionalLogging(userId?: string) {
  if (userId) {
    logger.info('User authenticated:', userId);
  } else {
    logger.warn('No user ID provided');
  }
}

// Export for testing
export { exampleFunction, asyncExample, conditionalLogging };

// Run examples if this file is executed directly
if (require.main === module) {
  console.log('=== Logger Example ===\n');
  exampleFunction();
  asyncExample();
  conditionalLogging('user123');
  conditionalLogging();
}
