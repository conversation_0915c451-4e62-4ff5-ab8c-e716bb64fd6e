#!/usr/bin/env tsx

import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { join } from 'path';
import { readdir } from 'fs/promises';

// Load environment variables
config();

async function checkMigrations() {
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'teno_store_db',
    synchronize: false,
    logging: false,
    entities: [join(__dirname, '../**/*.entity{.ts,.js}')],
    migrations: [join(__dirname, '../migrations/*{.ts,.js}')],
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    await dataSource.initialize();
    console.log('✅ Database connection established\n');

    // Check migration status
    const pendingMigrations = await dataSource.showMigrations();
    
    // Get available migrations from the filesystem
    const migrationsDir = join(__dirname, '../migrations');
    let availableMigrations: string[] = [];
    try {
      const files = await readdir(migrationsDir);
      availableMigrations = files.filter(file => 
        file.endsWith('.ts') || file.endsWith('.js')
      ).map(file => file.replace(/\.(ts|js)$/, ''));
    } catch (error) {
      console.log('⚠️  Migrations directory not found or empty');
    }

    // Get executed migrations from the database
    let executedMigrations: any[] = [];
    try {
      const queryRunner = dataSource.createQueryRunner();
      const result = await queryRunner.query(`
        SELECT name, timestamp 
        FROM migrations 
        ORDER BY timestamp ASC
      `);
      executedMigrations = result || [];
      await queryRunner.release();
    } catch (error) {
      console.log('⚠️  Could not retrieve executed migrations from database');
    }

    console.log('📊 Migration Status:');
    console.log('===================');
    console.log(`✅ Executed migrations: ${executedMigrations.length}`);
    console.log(`⏳ Pending migrations: ${pendingMigrations ? 'Yes' : 'No'}`);
    console.log(`📁 Available migrations: ${availableMigrations.length}\n`);

    if (executedMigrations.length > 0) {
      console.log('✅ Executed Migrations:');
      executedMigrations.forEach((migration, index) => {
        const timestamp = migration.timestamp ? new Date(parseInt(migration.timestamp)).toISOString() : 'Unknown';
        console.log(`   ${index + 1}. ${migration.name} (${timestamp})`);
      });
      console.log('');
    }

    if (availableMigrations.length > 0) {
      console.log('📁 Available Migrations:');
      availableMigrations.forEach((migration, index) => {
        const isExecuted = executedMigrations.some(em => em.name === migration);
        const status = isExecuted ? '✅' : '⏳';
        console.log(`   ${index + 1}. ${status} ${migration}`);
      });
      console.log('');
    }

    if (pendingMigrations) {
      console.log('🔄 Pending migrations detected!');
      console.log('   Run: npm run db:migrate');
    } else {
      console.log('✨ Database is up to date!');
    }

    await dataSource.destroy();
    console.log('\n✅ Database connection closed');
  } catch (error) {
    console.error('❌ Migration check failed:', error);
    process.exit(1);
  }
}

checkMigrations();
