import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';

@Entity('posts')
export class Post {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar' })
  title: string;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({ type: 'boolean', default: false })
  published: boolean;

  @Column({ type: 'bigint' })
  authorId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint' })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.posts)
  author: any;

  @ManyToOne('User', (user: any) => user.createdPosts)
  createdByUser: any;

  @ManyToOne('User', (user: any) => user.updatedPosts)
  updatedByUser: any;
}
