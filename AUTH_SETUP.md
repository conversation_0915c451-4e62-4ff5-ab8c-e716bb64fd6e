# Google OAuth Authentication Setup

This guide explains how to set up Google OAuth authentication for the Teno Store application.

## Features

- ✅ Google OAuth 2.0 authentication
- ✅ Automatic user registration on first login
- ✅ JWT-based session management
- ✅ Secure cookie handling
- ✅ Frontend login/register pages
- ✅ Protected dashboard route
- ✅ Backend authentication middleware

## Backend Setup

### 1. Environment Variables

Add the following variables to your backend `.env` file:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:5000/auth/google/callback

# Existing variables
DATABASE_URL="postgresql://username:password@localhost:5432/teno_store_db?schema=public"
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:5000"
FRONTEND_URL="http://localhost:3000"
```

### 2. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add authorized redirect URIs:
   - `http://localhost:5000/api/auth/google/callback` (development)
   - `https://yourdomain.com/api/auth/google/callback` (production)
7. Copy the Client ID and Client Secret to your `.env` file

### 3. Database Migration

Run the database migration to update the schema:

```bash
cd backend
npx prisma db push
npx prisma generate
```

### 4. Install Dependencies

The following packages are already installed:
- `passport`
- `passport-google-oauth20`
- `express-session`
- `jsonwebtoken`
- `bcryptjs`
- `cookie-parser`

## Frontend Setup

### 1. Environment Variables

Create a `.env.local` file in the frontend directory:

```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000
```

### 2. Install Dependencies

The following packages are already installed:
- `next-auth`
- `@next-auth/prisma-adapter`

## File Structure

### Backend Files

```
backend/src/
├── auth/
│   ├── google-strategy.ts      # Google OAuth strategy configuration
│   ├── jwt-utils.ts           # JWT token utilities
│   └── middleware.ts          # Authentication middleware
├── pages/api/
│   ├── auth/
│   │   ├── google.ts          # Google OAuth initiation
│   │   ├── google/
│   │   │   └── callback.ts    # OAuth callback handler
│   │   ├── logout.ts          # Logout endpoint
│   │   └── me.ts              # Get current user
│   ├── login.ts               # Login endpoint info
│   └── register.ts            # Register endpoint info
```

### Frontend Files

```
frontend/src/pages/
├── login.tsx                  # Login page
├── register.tsx               # Registration page
├── dashboard.tsx              # Protected dashboard
└── index.tsx                  # Updated home page
```

## API Endpoints

### Authentication Endpoints

- `GET /api/login` - Get login information
- `GET /api/register` - Get registration information
- `GET /api/auth/google` - Initiate Google OAuth
- `GET /api/auth/google/callback` - OAuth callback
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user (protected)

## Usage

### 1. Start the Applications

Backend:
```bash
cd backend
npm run dev
```

Frontend:
```bash
cd frontend
npm run dev
```

### 2. Access the Application

- Frontend: http://localhost:3000
- Backend: http://localhost:5000
- API Documentation: http://localhost:5000/docs

### 3. Authentication Flow

1. User visits `/login` or `/register`
2. Clicks "Continue with Google"
3. Redirected to Google OAuth consent screen
4. After approval, redirected to backend callback
5. Backend creates/updates user and generates JWT tokens
6. User redirected to frontend dashboard with authentication cookies

### 4. Protected Routes

To protect API routes, use the authentication middleware:

```typescript
import { authenticateToken } from '../../../auth/middleware';

export default function protectedHandler(req, res) {
  authenticateToken(req, res, () => {
    // Your protected route logic here
    res.json({ message: 'Protected data', user: req.user });
  });
}
```

## Security Features

- **Secure Cookies**: HTTPOnly, Secure, SameSite cookies
- **JWT Tokens**: 7-day access tokens, 30-day refresh tokens
- **User Validation**: Checks for deleted users on each request
- **CORS Protection**: Configured for frontend domain only
- **Environment Variables**: Sensitive data stored in environment

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI"**: Ensure the callback URL in Google Console matches exactly
2. **CORS errors**: Check FRONTEND_URL environment variable
3. **Database errors**: Run `npx prisma db push` to update schema
4. **Token issues**: Verify NEXTAUTH_SECRET is set

### Debug Mode

Enable debug logging by setting:
```env
DEBUG=passport*
```

## Production Deployment

1. Update environment variables for production domains
2. Use HTTPS for all URLs
3. Set secure cookie flags appropriately
4. Configure Google OAuth for production domain
5. Use strong secrets for JWT signing

## Next Steps

- Implement role-based access control
- Add email verification
- Implement password reset (for manual accounts)
- Add social login with other providers
- Implement refresh token rotation

