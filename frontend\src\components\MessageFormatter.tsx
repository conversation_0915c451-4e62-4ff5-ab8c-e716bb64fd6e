import React, { memo } from 'react';

interface MessageFormatterProps {
  content: string;
  variant?: 'dark' | 'light';
}

// Enhanced style set for better AI text rendering
type StyleSet = {
  link: string;
  inlineCode: string;
  heading: string;
  olLi: string;
  ulLi: string;
  paragraph: string;
  codeBlockWrapper: string;
  codeBlockPre: string;
  codeBlockLanguage: string;
  blockquote: string;
  blockquoteBar: string;
  strikethrough: string;
  table: string;
  tableHeader: string;
  tableCell: string;
  hr: string;
  mention: string;
  hashtag: string;
  command: string;
  underline: string;
  spoiler: string;
  spoilerReveal: string;
  image: string;
};

// Inline spoiler component for Telegram-like spoilers: ||hidden||
const Spoiler: React.FC<{ children: React.ReactNode; styles: StyleSet }> = ({ children, styles }) => {
  const [revealed, setRevealed] = React.useState(false);
  return (
    <span
      onClick={() => setRevealed(true)}
      className={revealed ? styles.spoilerReveal : styles.spoiler}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') setRevealed(true);
      }}
    >
      {children}
    </span>
  );
};

function renderInline(text: string, keyPrefix: string, styles: StyleSet): React.ReactNode[] {
  const nodes: React.ReactNode[] = [];

  const processText = (input: string, depth = 0): React.ReactNode[] => {
    if (depth > 10) return [input];
    const result: React.ReactNode[] = [];

    // Images first: ![alt](url)
    const imageRegex = /(!\[[^\]]*\]\([^\)]+\))/g;
    const imageParts = input.split(imageRegex);
    if (imageParts.length > 1) {
      imageParts.forEach((imgPart, imgIndex) => {
        const imgMatch = imgPart.match(/^!\[([^\]]*)\]\(([^\)]+)\)$/);
        if (imgMatch) {
          const [, alt, src] = imgMatch;
          result.push(<img key={`${keyPrefix}-img-${depth}-${imgIndex}`} src={src} alt={alt} className={styles.image} />);
        } else {
          result.push(...processText(imgPart, depth + 1));
        }
      });
      return result;
    }

    // Markdown links [text](url)
    const linkRegex = /(\[[^\]]+\]\([^\)]+\))/g;
    const linkParts = input.split(linkRegex);
    linkParts.forEach((part, i) => {
      const linkMatch = part.match(/^\[([^\]]+)\]\(([^\)]+)\)$/);
      if (linkMatch) {
        const [, linkText, href] = linkMatch;
        result.push(
          <a key={`${keyPrefix}-link-${depth}-${i}`} href={href} target="_blank" rel="noopener noreferrer" className={styles.link}>
            {linkText}
          </a>
        );
        return;
      }

      // Inline code `code`
      const codeRegex = /(`[^`\n]+`)/g;
      const codeParts = part.split(codeRegex);
      codeParts.forEach((codePart, j) => {
        const codeMatch = codePart.match(/^`([^`\n]+)`$/);
        if (codeMatch) {
          result.push(
            <code key={`${keyPrefix}-code-${depth}-${i}-${j}`} className={styles.inlineCode}>
              {codeMatch[1]}
            </code>
          );
          return;
        }

        // Strikethrough ~~text~~
        const strikeRegex = /(~~[^~]+?~~)/g;
        const strikeParts = codePart.split(strikeRegex);
        strikeParts.forEach((strikePart, k) => {
          const strikeMatch = strikePart.match(/^~~([^~]+?)~~$/);
          if (strikeMatch) {
            result.push(
              <span key={`${keyPrefix}-strike-${depth}-${i}-${j}-${k}`} className={styles.strikethrough}>{strikeMatch[1]}</span>
            );
            return;
          }

          // Underline __text__
          const underlineRegex = /(__[^_\n]+?__)/g;
          const underlineParts = strikePart.split(underlineRegex);
          underlineParts.forEach((underlinePart, u) => {
            const underlineMatch = underlinePart.match(/^__([^_\n]+?)__$/);
            if (underlineMatch) {
              result.push(
                <span key={`${keyPrefix}-underline-${depth}-${i}-${j}-${k}-${u}`} className={styles.underline}>{underlineMatch[1]}</span>
              );
              return;
            }

            // Bold **text**
            const boldRegex = /(\*\*[^*\n]+?\*\*)/g;
            const boldParts = underlinePart.split(boldRegex);
            boldParts.forEach((boldPart, l) => {
              const boldMatch = boldPart.match(/^\*\*([^*\n]+?)\*\*$/);
              if (boldMatch) {
                result.push(<strong key={`${keyPrefix}-bold-${depth}-${i}-${j}-${k}-${l}`} className="font-semibold">{boldMatch[1]}</strong>);
                return;
              }

              // Italic *text* or _text_
              const italicRegex = /(\*[^*\n]+?\*|_[^_\n]+?_)/g;
              const italicParts = boldPart.split(italicRegex);
              italicParts.forEach((italicPart, m) => {
                const italicMatch = italicPart.match(/^\*([^*\n]+?)\*$/) || italicPart.match(/^_([^_\n]+?)_$/);
                if (italicMatch) {
                  result.push(<em key={`${keyPrefix}-italic-${depth}-${i}-${j}-${k}-${l}-${m}`} className="italic opacity-90">{italicMatch[1]}</em>);
                  return;
                }

                // Plain text with autolinks/mentions/hashtags/commands/spoilers and preserved line breaks
                if (italicPart) {
                  const lines = italicPart.split('\n');
                  lines.forEach((line, n) => {
                    if (n > 0) result.push(<br key={`${keyPrefix}-br-${depth}-${i}-${j}-${k}-${l}-${m}-${n}`} />);
                    if (line) {
                      const tokenRegex = /(https?:\/\/[\w.-]+(?:\/[\w\-.~:/?#[\]@!$&'()*+,;=%]*)?|\b[\w.+-]+@[\w.-]+\.[A-Za-z]{2,}\b|\B@[A-Za-z0-9_]{3,}|\B#[A-Za-z0-9_]+|(?<=\s|^)[\/][A-Za-z0-9_]+\b|\|\|[^|\n]+\|\|)/g;
                      const tokens = line.split(tokenRegex);
                      tokens.forEach((tok, t) => {
                        if (!tok) return;
                        if (/^https?:\/\//.test(tok)) {
                          result.push(<a key={`${keyPrefix}-auto-url-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`} href={tok} className={styles.link} target="_blank" rel="noopener noreferrer">{tok}</a>);
                          return;
                        }
                        if (/^[\w.+-]+@[\w.-]+\.[A-Za-z]{2,}$/.test(tok)) {
                          result.push(<a key={`${keyPrefix}-auto-email-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`} href={`mailto:${tok}`} className={styles.link}>{tok}</a>);
                          return;
                        }
                        if (/^@[A-Za-z0-9_]{3,}$/.test(tok)) {
                          result.push(<span key={`${keyPrefix}-mention-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`} className={styles.mention}>{tok}</span>);
                          return;
                        }
                        if (/^#[A-Za-z0-9_]+$/.test(tok)) {
                          result.push(<span key={`${keyPrefix}-hashtag-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`} className={styles.hashtag}>{tok}</span>);
                          return;
                        }
                        if (/^\/[A-Za-z0-9_]+$/.test(tok)) {
                          result.push(<span key={`${keyPrefix}-command-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`} className={styles.command}>{tok}</span>);
                          return;
                        }
                        const spoilerMatch = tok.match(/^\|\|([^|\n]+)\|\|$/);
                        if (spoilerMatch) {
                          result.push(
                            <Spoiler key={`${keyPrefix}-spoiler-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`} styles={styles}>{spoilerMatch[1]}</Spoiler>
                          );
                          return;
                        }
                        result.push(<span key={`${keyPrefix}-text-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`}>{tok}</span>);
                      });
                    }
                  });
                }
              });
            });
          });
        });
      });
    });

    return result;
  };

  return processText(text);
}

// Enhanced block rendering with better AI text support
function renderBlocks(text: string, styles: StyleSet): React.ReactNode {
  const lines = text.split(/\r?\n/);
  const blocks: React.ReactNode[] = [];

  let i = 0;
  while (i < lines.length) {
    const line = lines[i];

    // Skip extra blank lines but preserve paragraph separation
    if (!line.trim()) {
      i++;
      continue;
    }

    const trimmed = line.trim();

    // Horizontal rule --- or ***
    if (/^(-{3,}|\*{3,}|_{3,})$/.test(trimmed)) {
      blocks.push(<hr key={`hr-${i}`} className={styles.hr} />);
      i++;
      continue;
    }

    // Code block with language detection ```lang
    if (/^```/.test(trimmed)) {
      const langMatch = trimmed.match(/^```(\w+)?/);
      const language = langMatch?.[1] || '';
      const codeLines: string[] = [];
      i++;
      
      while (i < lines.length && !/^```/.test(lines[i].trim())) {
        codeLines.push(lines[i]);
        i++;
      }
      // Skip closing ``` if present
      if (i < lines.length && /^```/.test(lines[i].trim())) i++;

      blocks.push(
        <div key={`code-${i}`} className={styles.codeBlockWrapper}>
          {language && (
            <div className={styles.codeBlockLanguage}>
              {language}
            </div>
          )}
          <pre className={styles.codeBlockPre}>
            <code>{codeLines.join('\n')}</code>
          </pre>
        </div>
      );
      continue;
    }

    // Image-only line
    const imageLineMatch = trimmed.match(/^!\[([^\]]*)\]\(([^\)]+)\)$/);
    if (imageLineMatch) {
      const [, alt, src] = imageLineMatch;
      blocks.push(
        <div key={`img-${i}`} className="my-3">
          <img src={src} alt={alt} className={styles.image} />
        </div>
      );
      i++;
      continue;
    }

    // GitHub-style tables
    if (/^\|?.*\|.*$/.test(trimmed) && i + 1 < lines.length && /^\|?\s*:?[-]{3,}.*\|.*$/.test(lines[i + 1].trim())) {
      const headerCells = trimmed.replace(/^\||\|$/g, '').split('|').map((c) => c.trim());
      i += 2; // skip separator
      const bodyRows: string[][] = [];
      while (i < lines.length && /\|/.test(lines[i])) {
        const rowCells = lines[i].replace(/^\||\|$/g, '').split('|').map((c) => c.trim());
        bodyRows.push(rowCells);
        i++;
      }
      blocks.push(
        <table key={`table-${i}`} className={styles.table}>
          <thead>
            <tr>
              {headerCells.map((cell, idx) => (
                <th key={`th-${idx}`} className={styles.tableHeader}>{cell}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {bodyRows.map((row, r) => (
              <tr key={`tr-${r}`}>
                {row.map((cell, c) => (
                  <td key={`td-${r}-${c}`} className={styles.tableCell}>{cell}</td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      );
      continue;
    }

    // Blockquote > text
    if (/^>\s*/.test(trimmed)) {
      const quoteLines: string[] = [];
      while (i < lines.length && /^>\s*/.test(lines[i].trim())) {
        const quoteLine = lines[i].replace(/^>\s*/, '');
        quoteLines.push(quoteLine);
        i++;
      }
      
      blocks.push(
        <blockquote key={`quote-${i}`} className={styles.blockquote}>
          <div className={styles.blockquoteBar}></div>
          <div className="flex-1">
            {renderBlocks(quoteLines.join('\n'), styles)}
          </div>
        </blockquote>
      );
      continue;
    }

    // Headings #, ##, ###, ####, #####, ######
    const heading = trimmed.match(/^(#{1,6})\s+(.*)$/);
    if (heading) {
      const level = heading[1].length;
      const content = heading[2];
      const Tag = (`h${Math.min(level, 6)}` as unknown) as keyof JSX.IntrinsicElements;
      blocks.push(
        <Tag key={`h-${i}`} className={`${styles.heading} ${getHeadingSize(level)}`}>
          {renderInline(content, `h-${i}`, styles)}
        </Tag>
      );
      i++;
      continue;
    }

    // Enhanced ordered list with nested support
    const orderedItem = trimmed.match(/^(\s*)(\d+)\.\s+(.*)$/);
    if (orderedItem) {
      const baseIndent = orderedItem[1].length;
      const items: React.ReactNode[] = [];
      
      while (i < lines.length) {
        const li = lines[i];
        const liTrimmed = li.trim();
        const m = li.match(/^(\s*)(\d+)\.\s+(.*)$/);
        
        if (!m || !liTrimmed) break;
        
        const indent = m[1].length;
        if (indent < baseIndent) break; // Less indented = end of this list
        
        const content = m[3];
        items.push(
          <li key={`ol-li-${i}`} className={styles.olLi}>
            {renderInline(content, `ol-li-${i}`, styles)}
          </li>
        );
        i++;
      }
      
      blocks.push(
        <ol key={`ol-${i}`} className="mb-3 pl-6 space-y-1 list-decimal">
          {items}
        </ol>
      );
      continue;
    }

    // Enhanced unordered list with nested support
    const unorderedItem = trimmed.match(/^(\s*)[-*+]\s+(.*)$/);
    if (unorderedItem) {
      const baseIndent = unorderedItem[1].length;
      const items: React.ReactNode[] = [];
      
      while (i < lines.length) {
        const li = lines[i];
        const liTrimmed = li.trim();
        const m = li.match(/^(\s*)[-*+]\s+(.*)$/);
        
        if (!m || !liTrimmed) break;
        
        const indent = m[1].length;
        if (indent < baseIndent) break; // Less indented = end of this list
        
        const content = m[2];
        items.push(
          <li key={`ul-li-${i}`} className={styles.ulLi}>
            {renderInline(content, `ul-li-${i}`, styles)}
          </li>
        );
        i++;
      }
      
      blocks.push(
        <ul key={`ul-${i}`} className="mb-3 pl-6 space-y-1 list-disc">
          {items}
        </ul>
      );
      continue;
    }

    // Task list [ ] and [x]
    const taskItem = trimmed.match(/^(\s*)[-*+]\s+\[([ x])\]\s+(.*)$/i);
    if (taskItem) {
      const baseIndent = taskItem[1].length;
      const items: React.ReactNode[] = [];
      
      while (i < lines.length) {
        const li = lines[i];
        const liTrimmed = li.trim();
        const m = li.match(/^(\s*)[-*+]\s+\[([ x])\]\s+(.*)$/i);
        
        if (!m || !liTrimmed) break;
        
        const indent = m[1].length;
        if (indent < baseIndent) break;
        
        const checked = m[2].toLowerCase() === 'x';
        const content = m[3];
        
        items.push(
          <li key={`task-li-${i}`} className={`${styles.ulLi} flex items-start gap-2`}>
            <input 
              type="checkbox" 
              checked={checked} 
              readOnly 
              className="mt-1 pointer-events-none" 
            />
            <span className={checked ? 'line-through opacity-60' : ''}>
              {renderInline(content, `task-li-${i}`, styles)}
            </span>
          </li>
        );
        i++;
      }
      
      blocks.push(
        <ul key={`task-${i}`} className="mb-3 pl-6 space-y-1 list-none">
          {items}
        </ul>
      );
      continue;
    }

    // Paragraph - collect consecutive non-empty, non-special lines
    const para: string[] = [line];
    i++;
    while (i < lines.length && 
           lines[i].trim() && 
           !/^```/.test(lines[i].trim()) && 
           !/^#{1,6}\s+/.test(lines[i].trim()) && 
           !/^(\s*)\d+\.\s+/.test(lines[i]) && 
           !/^(\s*)[-*+]\s+/.test(lines[i]) && 
           !/^>\s*/.test(lines[i].trim()) &&
           !/^(-{3,}|\*{3,}|_{3,})$/.test(lines[i].trim())) {
      para.push(lines[i]);
      i++;
    }
    
    blocks.push(
      <p key={`p-${i}`} className={styles.paragraph}>
        {renderInline(para.join('\n'), `p-${i}`, styles)}
      </p>
    );
  }

  return <div className="space-y-2">{blocks}</div>;
}

// Helper function for heading sizes
function getHeadingSize(level: number): string {
  const sizes = {
    1: 'text-2xl',
    2: 'text-xl', 
    3: 'text-lg',
    4: 'text-base',
    5: 'text-sm',
    6: 'text-xs'
  };
  return sizes[level as keyof typeof sizes] || 'text-base';
}

const MessageFormatter: React.FC<MessageFormatterProps> = memo(({ content, variant = 'dark' }) => {
  const styles: StyleSet = variant === 'light'
    ? {
        link: 'text-indigo-600 hover:text-indigo-800 underline transition-colors',
        inlineCode: 'bg-gray-100 text-emerald-700 px-1.5 py-0.5 rounded text-[0.85em] font-mono border',
        heading: 'text-gray-900 font-semibold mt-4 mb-3 border-b border-gray-200 pb-1',
        olLi: 'text-gray-800 text-sm leading-relaxed mb-1',
        ulLi: 'text-gray-800 text-sm leading-relaxed mb-1',
        paragraph: 'mb-4 text-gray-800 text-sm leading-relaxed',
        codeBlockWrapper: 'my-4 bg-gray-50 border border-gray-200 rounded-lg font-mono text-sm overflow-hidden',
        codeBlockPre: 'text-gray-800 p-4 overflow-x-auto whitespace-pre',
        codeBlockLanguage: 'bg-gray-100 text-gray-600 px-3 py-1 text-xs font-medium border-b border-gray-200',
        blockquote: 'my-4 flex gap-3 p-3 bg-gray-50 border-l-4 border-indigo-400 rounded-r-lg',
        blockquoteBar: 'w-1 bg-indigo-400 rounded-full flex-shrink-0',
        strikethrough: 'line-through opacity-60',
        table: 'my-4 border-collapse border border-gray-300 rounded-lg overflow-hidden',
        tableHeader: 'bg-gray-100 border border-gray-300 px-3 py-2 font-medium text-gray-900',
        tableCell: 'border border-gray-300 px-3 py-2 text-gray-800',
        hr: 'my-6 border-0 h-px bg-gray-300',
        mention: 'text-blue-700 bg-blue-50 px-1 rounded',
        hashtag: 'text-purple-700 bg-purple-50 px-1 rounded',
        command: 'text-emerald-700 bg-emerald-50 px-1 rounded font-medium',
        underline: 'underline underline-offset-2',
        spoiler: 'bg-gray-300 text-gray-300 rounded px-1 cursor-pointer select-none',
        spoilerReveal: 'bg-gray-100 text-gray-800 rounded px-1',
        image: 'max-w-full h-auto rounded border border-gray-200'
      }
    : {
        link: 'text-blue-400 hover:text-blue-300 underline transition-colors',
        inlineCode: 'bg-slate-700/50 text-green-300 px-1.5 py-0.5 rounded text-[0.85em] font-mono border border-slate-600/30',
        heading: 'text-slate-100 font-semibold mt-4 mb-3 border-b border-slate-600/40 pb-1',
        olLi: 'text-slate-200 text-sm leading-relaxed mb-1',
        ulLi: 'text-slate-200 text-sm leading-relaxed mb-1',
        paragraph: 'mb-4 text-slate-200 text-sm leading-relaxed',
        codeBlockWrapper: 'my-4 bg-slate-800/60 border border-slate-600/40 rounded-lg font-mono text-sm overflow-hidden',
        codeBlockPre: 'text-green-300 p-4 overflow-x-auto whitespace-pre',
        codeBlockLanguage: 'bg-slate-700/50 text-slate-300 px-3 py-1 text-xs font-medium border-b border-slate-600/40',
        blockquote: 'my-4 flex gap-3 p-3 bg-slate-800/40 border-l-4 border-blue-400 rounded-r-lg',
        blockquoteBar: 'w-1 bg-blue-400 rounded-full flex-shrink-0',
        strikethrough: 'line-through opacity-60',
        table: 'my-4 border-collapse border border-slate-600 rounded-lg overflow-hidden',
        tableHeader: 'bg-slate-700 border border-slate-600 px-3 py-2 font-medium text-slate-200',
        tableCell: 'border border-slate-600 px-3 py-2 text-slate-300',
        hr: 'my-6 border-0 h-px bg-slate-600',
        mention: 'text-blue-300 bg-slate-700/50 px-1 rounded',
        hashtag: 'text-fuchsia-300 bg-slate-700/50 px-1 rounded',
        command: 'text-emerald-300 bg-slate-700/50 px-1 rounded font-medium',
        underline: 'underline underline-offset-2 decoration-slate-400',
        spoiler: 'bg-slate-500 text-slate-500 rounded px-1 cursor-pointer select-none',
        spoilerReveal: 'bg-slate-800 text-slate-100 rounded px-1',
        image: 'max-w-full h-auto rounded border border-slate-600/40'
      };
  const formatMessage = (text: string): React.ReactNode => {
    try {
      // Preprocess text to normalize line endings and handle edge cases
      const normalizedText = text
        .replace(/\r\n/g, '\n')  // Normalize line endings
        .replace(/\r/g, '\n')    // Handle old Mac line endings
        .trim();                 // Remove leading/trailing whitespace
      
      if (!normalizedText) {
        return <div className="text-gray-500 italic">Empty message</div>;
      }
      
      return renderBlocks(normalizedText, styles);
    } catch (error) {
      console.error('MessageFormatter error:', error);
      return (
        <div className={`whitespace-pre-wrap text-sm leading-relaxed ${
          variant === 'light' ? 'text-gray-800' : 'text-slate-200'
        }`}>
          {text}
        </div>
      );
    }
  };

  return (
    <div className={`message-formatter ${
      variant === 'light' ? 'prose prose-sm max-w-none' : 'prose prose-sm prose-invert max-w-none'
    }`}>
      {formatMessage(content)}
    </div>
  );
});

MessageFormatter.displayName = 'MessageFormatter';

export default MessageFormatter;
