import { useEffect, useState, useMemo, useRef } from 'react';
import Head from 'next/head';
import { orderApi, storeApi } from '../../utils/api';
import { useAuth } from '../../context/AuthContext';
import { useStore } from '../../context/StoreContext';
import { usePreferences } from '../../context/PreferencesContext';
import TopTaskBar from '../../components/TopTaskBar';
import SideTaskBar from '../../components/SideTaskBar';
import EntityTable from '../../components/EntityTable';
import { useRouter } from 'next/router';
import OrderPrint from '../../components/OrderPrint';

// Custom hook for F1 keyboard shortcut
function useAddEntityShortcut(isEnabled: boolean, onOpen: () => void) {
  useEffect(() => {
    if (!isEnabled) return;

    const handler = (event: KeyboardEvent) => {
      if (event.key !== 'F1') return;

      const target = event.target as HTMLElement | null;
      const tag = target?.tagName?.toLowerCase();
      const isFormField = tag === 'input' || tag === 'textarea' || tag === 'select' || (target?.isContentEditable ?? false);
      if (isFormField) return;

      event.preventDefault();
      onOpen();
    };

    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [isEnabled, onOpen]);
}

export default function OrdersPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { currentStoreId } = useStore();
  const { language, currency } = usePreferences();

  const [page, setPage] = useState(1);
  const limit = 20;

  const [ordersData, setOrdersData] = useState<any[]>([]);
  const [ordersMeta, setOrdersMeta] = useState<any | null>(null);
  const [selectedOrderId, setSelectedOrderId] = useState<any | null>(null);
  const [printRequested, setPrintRequested] = useState(false);
  const [cancelOrderId, setCancelOrderId] = useState<any | null>(null);
  const [cancelReason, setCancelReason] = useState<string>("");
  const [showAddModal, setShowAddModal] = useState(false);

  // State for API calls
  const [currentStore, setCurrentStore] = useState<any>(null);
  const [isLoadingStore, setIsLoadingStore] = useState(false);
  const [isLoadingOrders, setIsLoadingOrders] = useState(false);

  // Fetch current store information to get store currency
  useEffect(() => {
    if (currentStoreId) {
      setIsLoadingStore(true);
      storeApi.getById(currentStoreId)
        .then((data) => {
          setCurrentStore(data);
        })
        .catch((error) => {
          console.error('Failed to fetch store:', error);
        })
        .finally(() => {
          setIsLoadingStore(false);
        });
    }
  }, [currentStoreId]);

  // Fetch orders when store changes
  useEffect(() => {
    if (user && currentStoreId) {
      fetchOrders();
    }
  }, [user, currentStoreId, page]);

  const fetchOrders = async () => {
    if (!user || !currentStoreId) return;
    
    setIsLoadingOrders(true);
    try {
      const resp = await orderApi.filter({
        page,
        limit,
        storeId: currentStoreId,
      });
      setOrdersData((resp as any)?.data ?? []);
      setOrdersMeta((resp as any)?.meta ?? null);
    } catch (error) {
      console.error('Failed to fetch orders:', error);
    } finally {
      setIsLoadingOrders(false);
    }
  };

  const handleUpdateStatus = async (
    orderId: any,
    newStatus: string,
    totalAmount?: number
  ) => {
    try {
      await orderApi.update(orderId, {
        status: newStatus,
        totalAmount: totalAmount,
        userId: user!.id,
      });
      // Refresh orders after update
      fetchOrders();
    } catch (error) {
      console.error('Failed to update order:', error);
    }
  };

  useEffect(() => {
    setPage(1);
  }, []);

  // F1 keyboard shortcut for adding new order
  const isShortcutEnabled = Boolean(currentStoreId) && !showAddModal;
  useAddEntityShortcut(isShortcutEnabled, () => setShowAddModal(true));

  const isLoading = isLoadingOrders;
  const error = null; // No direct error state from tRPC, but can be derived from fetch calls
  const data = ordersData;
  const meta = ordersMeta;

  // Use store currency if available, fallback to preferences currency
  const storeCurrency = currentStore?.currency || currency;
  const currencyFormatter = new Intl.NumberFormat(language, { style: 'currency', currency: storeCurrency });

  // Order details query for modal
  const queryEnabled = useMemo(() => !!selectedOrderId && !!user, [selectedOrderId, user]);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [isLoadingOrder, setIsLoadingOrder] = useState(false);
  const [orderError, setOrderError] = useState<any>(null);

  useEffect(() => {
    if (queryEnabled && selectedOrderId) {
      setIsLoadingOrder(true);
      setOrderError(null);
      orderApi.getById(selectedOrderId)
        .then((data) => {
          setSelectedOrder(data);
        })
        .catch((error) => {
          setOrderError(error);
        })
        .finally(() => {
          setIsLoadingOrder(false);
        });
    }
  }, [queryEnabled, selectedOrderId]);

  const refetchSelectedOrder = () => {
    if (queryEnabled && selectedOrderId) {
      fetchOrders();
    }
  };

  // Modal focus management per UI guidelines
  const closeBtnRef = useRef<HTMLButtonElement | null>(null);
  useEffect(() => {
    if (selectedOrderId) {
      const t = setTimeout(() => closeBtnRef.current?.focus(), 0);
      return () => clearTimeout(t);
    }
  }, [selectedOrderId]);

  // Track-like helpers
  const ORDER_STATUSES = [
    { value: 'draft', label: 'Order Received', color: 'text-slate-300', description: 'Your order has been received and is being processed.' },
    { value: 'confirmed', label: 'Order Confirmed', color: 'text-emerald-300', description: 'Your order has been confirmed and is being prepared.' },
    { value: 'processing', label: 'Preparing Order', color: 'text-cyan-300', description: 'Your order is being prepared for shipment.' },
    { value: 'shipped', label: 'Shipped', color: 'text-indigo-300', description: 'Your order has been shipped and is on its way.' },
    { value: 'delivered', label: 'Delivered', color: 'text-emerald-300', description: 'Your order has been successfully delivered.' },
    { value: 'cancelled', label: 'Cancelled', color: 'text-red-300', description: 'Your order has been cancelled.' },
    { value: 'returned', label: 'Returned', color: 'text-orange-300', description: 'Your order has been returned.' },
  ];
  const getStatusStep = (status: string) => {
    const idx = ORDER_STATUSES.findIndex(s => s.value === status);
    return idx >= 0 ? idx : 0;
  };
  const isCompleted = (currentStep: number, step: number) => step <= currentStep;
  const isCurrent = (currentStep: number, step: number) => step === currentStep;

  const getStatusStyle = (status: string) => {
    const statusConfig = ORDER_STATUSES.find(s => s.value === status);
    if (!statusConfig) return 'bg-slate-500/10 text-slate-300 border-slate-500/20';

    switch (statusConfig.color) {
      case 'text-emerald-300':
        return 'bg-emerald-500/10 text-emerald-300 border-emerald-500/20';
      case 'text-cyan-300':
        return 'bg-cyan-500/10 text-cyan-300 border-cyan-500/20';
      case 'text-indigo-300':
        return 'bg-indigo-500/10 text-indigo-300 border-indigo-500/20';
      case 'text-red-300':
        return 'bg-red-500/10 text-red-300 border-red-500/20';
      case 'text-orange-300':
        return 'bg-orange-500/10 text-orange-300 border-orange-500/20';
      default:
        return 'bg-slate-500/10 text-slate-300 border-slate-500/20';
    }
  };

  // Define table columns for EntityTable
  const columns = [
    {
      key: 'order',
      header: 'Order',
      render: (value: any, row: any) => (
        <div>
          <div className="text-slate-100">#{row.id.toString()}</div>
          {row.orderNumber && (
            <div className="text-xs text-slate-400">{row.orderNumber}</div>
          )}
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (value: any, row: any) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium border capitalize ${getStatusStyle(row.status)}`}>
          {row.status}
        </span>
      ),
    },
    {
      key: 'priority',
      header: 'Priority',
      render: (value: any, row: any) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          row.priority === 'urgent' ? 'bg-red-500/10 text-red-300 border border-red-500/20' :
          row.priority === 'high' ? 'bg-orange-500/10 text-orange-300 border border-orange-500/20' :
          row.priority === 'low' ? 'bg-slate-500/10 text-slate-300 border border-slate-500/20' :
          'bg-emerald-500/10 text-emerald-300 border border-emerald-500/20'
        }`}>
          {row.priority}
        </span>
      ),
    },
    {
      key: 'items',
      header: 'Items',
      render: (value: any, row: any) => (row.items || row.orderItems)?.length ?? 0,
    },
    {
      key: 'total',
      header: 'Total',
      render: (value: any, row: any) => {
        const total = parseFloat(row.total?.toString?.() ?? '0') || 0;
        return (
          <span className="font-semibold">{currencyFormatter.format(total)}</span>
        );
      },
    },
    {
      key: 'orderDate',
      header: 'Order Date',
      render: (value: any, row: any) => {
        const orderDate = new Date(row.orderDate as unknown as string);
        return (
          <span className="text-slate-400">
            {isNaN(orderDate.getTime()) ? 'Unknown' : orderDate.toLocaleString()}
          </span>
        );
      },
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (value: any, row: any) => (
        <div className="flex items-center gap-3">
          {row.orderNumber && (
            <button
              onClick={() => window.open(`/track/${row.orderNumber}`, '_blank')}
              className="text-slate-300 hover:text-emerald-400 rounded-md p-2 border border-white/10 hover:bg-white/5"
              title="Track"
              aria-label="Track"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 11a3 3 0 100-6 3 3 0 000 6z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 22s7-7.58 7-12a7 7 0 10-14 0c0 4.42 7 12 7 12z" />
              </svg>
              <span className="sr-only">Track</span>
            </button>
          )}

          <button
            onClick={() => setSelectedOrderId(row.id as any)}
            className="text-slate-300 hover:text-emerald-400 rounded-md p-2 border border-white/10 hover:bg-white/5"
            title="View"
            aria-label="View"
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <span className="sr-only">View</span>
          </button>

          <button
            onClick={() => {
              setSelectedOrderId(row.id as any);
              setPrintRequested(true);
            }}
            className="text-slate-300 hover:text-emerald-400 rounded-md p-2 border border-white/10 hover:bg-white/5"
            title="Print"
            aria-label="Print"
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 9V2h12v7M6 18H4a2 2 0 01-2-2v-5a2 2 0 012-2h16a2 2 0 012 2v5a2 2 0 01-2 2h-2M6 14h12v8H6v-8z" />
            </svg>
            <span className="sr-only">Print</span>
          </button>

          {row.status === 'pending' && (
            <button
              onClick={() => handleUpdateStatus(row.id, 'confirmed')}
              disabled={false}
              className="bg-emerald-600 text-white px-3 py-1.5 rounded-md hover:bg-emerald-700 transition-colors disabled:opacity-50"
              title="Mark as Order Confirmed"
            >
              Confirm
            </button>
          )}

          {row.status === 'confirmed' && (
            <button
              onClick={() => handleUpdateStatus(row.id, 'shipped' as any)}
              disabled={false}
              className="bg-indigo-600 text-white px-3 py-1.5 rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50"
              title="Mark as Shipped"
            >
              Ship
            </button>
          )}

          {row.status === 'shipped' && (
            <button
              onClick={() => handleUpdateStatus(row.id, 'delivered')}
              disabled={false}
              className="bg-emerald-600 text-white px-3 py-1.5 rounded-md hover:bg-emerald-700 transition-colors disabled:opacity-50"
              title="Mark as Delivered"
            >
              Deliver
            </button>
          )}

          {!['cancelled', 'delivered', 'returned'].includes(row.status) && (
            <button
              onClick={() => { setCancelOrderId(row.id as any); setCancelReason(""); }}
              disabled={false}
              className="text-red-400 hover:text-red-300 rounded-md p-2 border border-red-500/20 hover:bg-red-500/10 transition-colors disabled:opacity-50"
              title="Cancel Order"
              aria-label="Cancel"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span className="sr-only">Cancel</span>
            </button>
          )}
        </div>
      ),
    },
  ];

  // Transform meta data for EntityTable pagination
  const paginationData = meta ? {
    currentPage: meta.page,
    totalPages: meta.totalPages,
    onPageChange: setPage,
    totalItems: meta.total,
    itemsPerPage: meta.limit,
  } : undefined;

  return (
    <div className="min-h-screen bg-slate-900">
      <Head>
        <title>Orders - Teno Store</title>
        <meta name="description" content="Process and manage customer orders" />
      </Head>

      <TopTaskBar />
      <SideTaskBar />

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {!user && (
          <div className="min-h-[50vh] bg-slate-900 flex items-center justify-center p-6">
            <div className="relative max-w-md w-full space-y-8">
              <div className="text-center">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-emerald-500/10 ring-1 ring-emerald-500/20 shadow-md mb-6">
                  <svg className="h-7 w-7 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <h2 className="mt-2 text-3xl font-extrabold tracking-tight text-slate-100">Authentication Required</h2>
                <p className="mt-2 text-slate-300">You need to be logged in to manage orders.</p>
              </div>
              <div className="bg-slate-800/60 rounded-lg p-4 border border-slate-600/50">
                <button
                  className="group bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors w-full"
                  onClick={() => router.push('/login')}
                >
                  Go to Login
                </button>
              </div>
            </div>
          </div>
        )}
        
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-extrabold tracking-tight text-slate-100">Orders</h1>
              <p className="mt-2 text-slate-300">View and manage your store&apos;s orders.</p>
            </div>
            {user && currentStoreId && (
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors"
              >
                + Add Order
              </button>
            )}
          </div>
        </div>

        {!currentStoreId && (
          <div className="bg-yellow-500/10 border border-yellow-500/20 text-yellow-300 px-4 py-3 rounded-md mb-6">
            Please select a store to view and manage its orders.
          </div>
        )}

        {user && currentStoreId && (
          <EntityTable
            columns={columns}
            data={data}
            isLoading={isLoading}
            loadingText="Loading orders..."
            noDataText={
              <div className="text-center">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-emerald-500/10 ring-1 ring-emerald-500/20 shadow-md mb-6">
                  <svg className="h-7 w-7 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-slate-100 mb-2">No orders yet</h3>
                <p className="text-slate-300">Orders will appear here when created.</p>
              </div>
            }
            pagination={paginationData}
            containerClassName="border border-white/10"
          />
        )}
      </div>

      {/* Order Details Modal (Console Dark Theme) */}
      {selectedOrderId && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/50" onClick={() => setSelectedOrderId(null)} />
          <div className="relative w-full max-w-4xl overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95">
            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-indigo-500/20 blur opacity-60" />
            <div className="relative flex flex-col max-h-[80vh]">
              <div className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="mx-auto flex h-10 w-10 items-center justify-center rounded-lg bg-slate-700/60">
                    <svg className="h-6 w-6 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-100">Order Details</h3>
                    {selectedOrder?.orderNumber && (
                      <div className="text-xs text-slate-400">Order {selectedOrder.orderNumber}</div>
                    )}
                  </div>
                </div>
                <button
                  ref={closeBtnRef}
                  onClick={() => setSelectedOrderId(null)}
                  className="text-slate-400 hover:text-slate-200 rounded-md p-1"
                  aria-label="Close"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
              </div>

              <div className="p-6 overflow-y-auto">
                {isLoadingOrder && (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-400 mx-auto mb-4"></div>
                      <p className="text-slate-300">Loading order...</p>
                    </div>
                  </div>
                )}

                {orderError && (
                  <div className="bg-red-500/10 border border-red-500/20 text-red-300 px-4 py-3 rounded-md">
                    Failed to load order. Please try again.
                  </div>
                )}

                {!isLoadingOrder && !orderError && !selectedOrder && (
                  <div className="bg-yellow-500/10 border border-yellow-500/20 text-yellow-300 px-4 py-3 rounded-md">
                    Order not found.
                  </div>
                )}

                {!isLoadingOrder && !orderError && selectedOrder && (
                  <div className="space-y-6">
                    {/* Header Card with status */}
                    <div className="bg-slate-800 rounded-lg shadow p-6 border border-white/10">
                      <div className="flex flex-col md:flex-row md:items-center justify-between">
                        <div>
                          <h2 className="text-2xl font-semibold text-slate-100">Order {selectedOrder.orderNumber ?? `#${selectedOrder.id.toString()}`}</h2>
                          {selectedOrder.orderDate && (
                            <p className="text-slate-300">Placed on {new Date(selectedOrder.orderDate as any).toLocaleDateString()}</p>
                          )}
                        </div>
                        <div className="mt-4 md:mt-0">
                          {(() => {
                            const step = getStatusStep(selectedOrder.status);
                            const status = ORDER_STATUSES[step] ?? ORDER_STATUSES[0];
                            return (
                              <span className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ring-1 ring-inset ${
                                status.color === 'text-emerald-300' ? 'ring-emerald-500/30 text-emerald-300 bg-emerald-500/10' :
                                status.color === 'text-cyan-300' ? 'ring-cyan-500/30 text-cyan-300 bg-cyan-500/10' :
                                status.color === 'text-indigo-300' ? 'ring-indigo-500/30 text-indigo-300 bg-indigo-500/10' :
                                status.color === 'text-yellow-300' ? 'ring-yellow-500/30 text-yellow-300 bg-yellow-500/10' :
                                status.color === 'text-red-300' ? 'ring-red-500/30 text-red-300 bg-red-500/10' :
                                status.color === 'text-orange-300' ? 'ring-orange-500/30 text-orange-300 bg-orange-500/10' :
                                'ring-slate-500/30 text-slate-300 bg-slate-500/10'
                              }`}>{status.label}</span>
                            );
                          })()}
                        </div>
                      </div>

                      {selectedOrder.expectedDeliveryDate && selectedOrder.status !== 'cancelled' && selectedOrder.status !== 'returned' && (
                        <div className="bg-blue-500/10 border border-blue-500/20 rounded-md p-4 mt-6">
                          <div className="flex">
                            <svg className="h-5 w-5 text-blue-300 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            <div>
                              <h3 className="text-sm font-medium text-blue-300">Expected Delivery</h3>
                              <p className="text-sm text-blue-200">{new Date(selectedOrder.expectedDeliveryDate as any).toLocaleDateString()}</p>
                            </div>
                          </div>
                        </div>
                      )}
                      {selectedOrder.status === 'cancelled' && selectedOrder.cancellationReason && (
                        <div className="bg-red-500/10 border border-red-500/20 rounded-md p-4 mt-6">
                          <div className="flex">
                            <svg className="h-5 w-5 text-red-300 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10A8 8 0 11.001 9.999 8 8 0 0118 10zM9 7a1 1 0 112 0v3a1 1 0 01-2 0V7zm1 6a1.5 1.5 0 100 3 1.5 1.5 0 000-3z" clipRule="evenodd" />
                            </svg>
                            <div>
                              <h3 className="text-sm font-medium text-red-300">Cancellation Reason</h3>
                              <p className="text-sm text-red-200 whitespace-pre-wrap">{selectedOrder.cancellationReason}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Progress */}
                    <div className="bg-slate-800 rounded-lg shadow p-6 border border-white/10">
                      <h3 className="text-lg font-medium text-slate-100 mb-6">Order Progress</h3>
                      <div className="relative">
                        {(() => {
                          const currentStep = getStatusStep(selectedOrder.status);
                          const displayStatuses = ORDER_STATUSES.filter(s => {
                            if (selectedOrder.status === 'cancelled' || selectedOrder.status === 'returned') {
                              return s.value === selectedOrder.status || ['draft', 'confirmed'].includes(s.value);
                            }
                            return !['cancelled', 'returned'].includes(s.value);
                          });
                          return displayStatuses.map((status, index) => {
                            const stepIndex = ORDER_STATUSES.findIndex(s => s.value === status.value);
                            const stepCompleted = isCompleted(currentStep, stepIndex);
                            const stepCurrent = isCurrent(currentStep, stepIndex);
                            return (
                              <div key={status.value} className="relative flex items-start mb-8 last:mb-0">
                                {index < displayStatuses.length - 1 && (
                                  <div className={`absolute left-6 top-12 w-0.5 h-8 ${stepCompleted ? 'bg-emerald-500' : 'bg-slate-600'}`} />
                                )}
                                <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 ${
                                  stepCompleted ? 'bg-emerald-500 border-emerald-500' : stepCurrent ? 'bg-indigo-500 border-indigo-500' : 'bg-slate-700 border-slate-600'
                                }`}>
                                  {stepCompleted ? (
                                    <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                  ) : stepCurrent ? (
                                    <div className="w-3 h-3 bg-white rounded-full" />
                                  ) : (
                                    <div className="w-3 h-3 bg-slate-500 rounded-full" />
                                  )}
                                </div>
                                <div className="ml-4 min-w-0 flex-1">
                                  <h4 className={`text-sm font-medium ${stepCompleted || stepCurrent ? 'text-slate-100' : 'text-slate-400'}`}>{status.label}</h4>
                                  <p className={`text-sm ${stepCompleted || stepCurrent ? 'text-slate-300' : 'text-slate-500'}`}>{status.description}</p>
                                </div>
                              </div>
                            );
                          });
                        })()}
                      </div>
                    </div>

                    {/* Items */}
                    <div className="bg-slate-800 rounded-lg shadow p-6 border border-white/10">
                      <h3 className="text-lg font-medium text-slate-100 mb-4">Order Items</h3>
                      <div className="space-y-4">
                        {(selectedOrder.items || selectedOrder.orderItems)?.map((item: any) => {
                          const unitPrice = parseFloat(item.unitPrice?.toString?.() ?? '0') || 0;
                          const lineTotal = parseFloat(item.lineTotal?.toString?.() ?? '0') || 0;
                          return (
                            <div key={item.id.toString()} className="flex items-center justify-between py-4 border-b border-white/10 last:border-b-0">
                              <div className="flex-1">
                                <h4 className="text-sm font-medium text-slate-100">{item.productName}</h4>
                                <p className="text-sm text-slate-300">Quantity: {item.quantity}</p>
                                <p className="text-sm text-slate-300">Unit Price: {currencyFormatter.format(unitPrice)}</p>
                              </div>
                              <div className="text-right">
                                <p className="text-sm font-medium text-slate-100">{currencyFormatter.format(lineTotal)}</p>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Summary */}
                    <div className="bg-slate-800 rounded-lg shadow p-6 border border-white/10">
                      <h3 className="text-lg font-medium text-slate-100 mb-4">Order Summary</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-300">Subtotal:</span>
                          <span className="text-slate-100">{currencyFormatter.format(parseFloat((selectedOrder.subtotal as any)?.toString() ?? '0'))}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-300">Tax:</span>
                          <span className="text-slate-100">{currencyFormatter.format(parseFloat((selectedOrder.taxAmount as any)?.toString() ?? '0'))}</span>
                        </div>
                        <div className="border-t border-white/10 pt-2">
                          <div className="flex justify-between text-base font-medium">
                            <span className="text-slate-100">Total:</span>
                            <span className="text-slate-100">{currencyFormatter.format(parseFloat((selectedOrder.total as any)?.toString() ?? '0'))}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="px-6 py-3 border-t border-white/10 flex items-center justify-end gap-3">
                <button
                  onClick={() => setSelectedOrderId(null)}
                  className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-md transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {printRequested && selectedOrder && (
        <OrderPrint
          order={selectedOrder}
          currencyFormatter={currencyFormatter}
          isOpen={true}
          onClose={() => setPrintRequested(false)}
        />
      )}
      
      {cancelOrderId && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/50" onClick={() => setCancelOrderId(null)} />
          <div className="relative w-full max-w-md overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-slate-800">
            <div className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-slate-100">Cancel Order</h3>
              <button onClick={() => setCancelOrderId(null)} className="text-slate-400 hover:text-slate-200 rounded-md p-1" aria-label="Close">
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
              </button>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-200 mb-1">Reason (optional)</label>
                <textarea
                  className="w-full rounded-md bg-slate-900 border border-white/10 text-slate-100 p-3 outline-none focus:ring-2 focus:ring-red-500/40 focus:border-red-500/40"
                  rows={4}
                  placeholder="Provide a reason for cancellation"
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                />
                <p className="text-xs text-slate-400 mt-1">Customers may see this reason in communications.</p>
              </div>
            </div>
            <div className="px-6 py-3 border-t border-white/10 flex items-center justify-end gap-3">
              <button onClick={() => setCancelOrderId(null)} className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-md transition-colors">
                No, keep order
              </button>
              <button
                onClick={() => { handleUpdateStatus(cancelOrderId, 'cancelled'); setCancelOrderId(null); }}
                disabled={false}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                Yes, cancel order
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Order Modal - Placeholder for future implementation */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/50" onClick={() => setShowAddModal(false)} />
          <div className="relative w-full max-w-2xl overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95">
            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-indigo-500/20 blur opacity-60" />
            <div className="relative flex flex-col max-h-[80vh]">
              <div className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
                <h3 className="text-lg font-semibold text-slate-100">Add New Order</h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-slate-400 hover:text-slate-200 rounded-md p-1"
                  aria-label="Close"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
              </div>
              <div className="p-6 overflow-y-auto">
                <div className="text-center py-12">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-emerald-500/10 ring-1 ring-emerald-500/20 shadow-md mb-6">
                    <svg className="h-7 w-7 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-slate-100 mb-2">Coming Soon</h3>
                  <p className="text-slate-300">Order creation functionality will be available in a future update.</p>
                </div>
              </div>
              <div className="px-6 py-3 border-t border-white/10 flex items-center justify-end gap-3">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-md transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
