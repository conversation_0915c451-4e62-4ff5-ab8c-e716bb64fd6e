"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.placeOrder = void 0;
exports.placeOrderTool = placeOrderTool;
const dist_1 = require("../../../../../ai-agent/dist");
const customer_service_1 = require("../../services/customer.service");
const order_service_1 = require("../../services/order.service");
const shared_1 = require("./shared");
function logError(context, error, additionalInfo) {
    const errorContext = {
        tool: 'placeOrder',
        context,
        error: error?.message || String(error),
        stack: error?.stack,
        timestamp: new Date().toISOString(),
        ...additionalInfo
    };
    console.error(`\n🚨 PLACE ORDER ERROR in ${context}:`);
    console.error(`   Error: ${errorContext.error}`);
    console.error(`   Context: ${context}`);
    if (additionalInfo) {
        console.error(`   Additional Info:`, additionalInfo);
    }
    if (error?.stack) {
        console.error(`   Stack Trace:`);
        console.error(error.stack);
    }
    console.error(`   Full Error Context:`, JSON.stringify(errorContext, null, 2));
    console.error(`\n`);
}
async function placeOrderTool(params, db, conversationUuid) {
    try {
        console.log(`🔧 placeOrderTool execution started with:`, {
            dbType: typeof db,
            dbExists: !!db,
            dbInitialized: db?.isInitialized,
            conversationUuid,
            timestamp: new Date().toISOString()
        });
        if (!db) {
            console.error(`❌ Database connection is undefined`);
            throw new Error('Database connection is undefined - cannot execute placeOrder tool');
        }
        if (!db || !db.isInitialized) {
            console.error(`❌ Database connection is invalid:`, {
                dbExists: !!db,
                dbInitialized: db?.isInitialized,
                dbType: typeof db
            });
            throw new Error('Database connection is not properly initialized');
        }
        const conversationRepo = db.getRepository('Conversation');
        if (!conversationRepo || typeof conversationRepo.findOne !== 'function') {
            console.error(`❌ Failed to get conversation repository:`, {
                hasRepo: !!conversationRepo,
                repoType: typeof conversationRepo,
                hasFindOne: conversationRepo ? typeof conversationRepo.findOne : 'N/A'
            });
            throw new Error('Failed to get conversation repository');
        }
        const customerRepo = db.getRepository('Customer');
        const orderRepo = db.getRepository('Order');
        const productRepo = db.getRepository('Product');
        try {
            console.log(`🔧 Testing database connection with simple query...`);
            const testResult = await conversationRepo.findOne({
                where: { uuid: conversationUuid, isDeleted: false },
                select: ['id']
            });
            console.log(`✅ Database connection test passed, found conversation:`, testResult ? 'yes' : 'no');
        }
        catch (error) {
            console.error(`❌ Database connection test failed:`, error);
            throw new Error(`Database connection test failed: ${error.message}`);
        }
        console.log(`✅ Database connection validation passed`);
        const { customerName, customerEmail = '', customerPhone, customerAddress, items, useTax = false, taxRate = 0, priority = 'normal' } = params || {};
        console.log(`\n🛒 PLACE ORDER STARTED:`);
        console.log(`   Customer: ${customerName} (${customerPhone})`);
        console.log(`   Items: ${items.length} item(s)`);
        console.log(`   Priority: ${priority}`);
        console.log(`   Conversation: ${conversationUuid}\n`);
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
        if (!customerName) {
            throw new Error('customerName is required');
        }
        if (!customerPhone || customerPhone.trim() === '') {
            throw new Error('customerPhone is required');
        }
        if (!customerAddress || customerAddress.trim() === '') {
            throw new Error('customerAddress is required');
        }
        if (!items) {
            throw new Error('items parameter is missing. Must provide an array of items with productId or productName and quantity.');
        }
        if (!Array.isArray(items) || items.length === 0) {
            throw new Error('items must be a non-empty array');
        }
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const hasProductId = typeof item.productId !== 'undefined';
            const hasProductName = typeof item.productName === 'string' && item.productName.trim().length > 0;
            const hasValidQuantity = typeof item.quantity === 'number' && item.quantity > 0;
            if (!hasValidQuantity) {
                throw new Error(`Item ${i + 1}: quantity must be a positive number`);
            }
            if (!hasProductId && !hasProductName) {
                throw new Error(`Item ${i + 1}: must provide either productId (numeric) or productName (non-empty string)`);
            }
            if (hasProductId) {
                const productIdValue = item.productId;
                const isNumericId = typeof productIdValue === 'number' || typeof productIdValue === 'bigint' ||
                    (typeof productIdValue === 'string' && /^\d+$/.test(productIdValue.trim()));
                const isValidName = typeof productIdValue === 'string' && productIdValue.trim().length > 0;
                if (!isNumericId && !isValidName) {
                    throw new Error(`Item ${i + 1}: productId must be numeric or productName must be non-empty string`);
                }
            }
        }
        console.log(`📋 Reading conversation context...`);
        const convo = await conversationRepo.findOne({
            where: { uuid: conversationUuid, isDeleted: false },
            select: ['id', 'userId', 'storeId', 'createdBy', 'context'],
        });
        if (!convo) {
            logError('conversation_lookup', new Error('Conversation not found'), { conversationUuid });
            throw new Error('Conversation not found');
        }
        console.log(`   Store ID: ${convo.storeId}`);
        console.log(`   User ID: ${convo.userId}`);
        console.log(`🔍 Checking for existing orders...`);
        const underwayCheck = await (0, order_service_1.checkOrderUnderwayByPhone)(customerPhone, db);
        if (underwayCheck) {
            const order = underwayCheck;
            const orderDetails = "Order items (details would need orderItems relation loaded)";
            const statusText = order.status.charAt(0).toUpperCase() + order.status.slice(1);
            const priorityText = order.priority ? ` (${order.priority} priority)` : '';
            console.log(`   Found existing order: ${order.orderNumber}`);
            return {
                message: `Order already underway for ${underwayCheck.customer?.name || customerName} (${customerPhone}): ${order.orderNumber}. Status: ${statusText}${priorityText}. Items: ${orderDetails}. Total: $${order.total}. Track your order at: ${frontendUrl}/track/${order.orderNumber}`,
                orderNumber: order.orderNumber,
                orderId: order.id,
                underway: true,
                total: order.total,
                customerName: underwayCheck.customer?.name || customerName,
                customerPhone: customerPhone,
                status: order.status,
                priority: order.priority,
                items: [],
                trackingUrl: `frontendurl/track/${order.orderNumber}`,
            };
        }
        console.log(`👤 Processing customer...`);
        let customerIdBigInt;
        if (customerEmail && customerEmail.trim() !== '') {
            console.log(`   Searching by email: ${customerEmail}`);
            const foundCustomers = await (0, customer_service_1.filterCustomers)({ email: customerEmail, storeId: convo.storeId }, db);
            if (Array.isArray(foundCustomers) && foundCustomers.length > 0) {
                const exact = foundCustomers.find((c) => (c.email || '').toLowerCase() === customerEmail.toLowerCase());
                customerIdBigInt = (exact || foundCustomers[0]).id;
                console.log(`   Found existing customer by email: ${customerIdBigInt}`);
            }
            else {
                console.log(`   Creating new customer with email`);
                const created = await (0, customer_service_1.createCustomer)({
                    email: customerEmail,
                    name: customerName,
                    phone: customerPhone || undefined,
                    address: customerAddress || undefined,
                    storeId: convo.storeId,
                    createdBy: convo.createdBy?.toString(),
                }, db);
                customerIdBigInt = created.id;
                console.log(`   Created new customer: ${customerIdBigInt}`);
            }
        }
        else {
            console.log(`   Searching by name: ${customerName}`);
            const foundCustomers = await (0, customer_service_1.filterCustomers)({ name: customerName, storeId: convo.storeId }, db);
            if (Array.isArray(foundCustomers) && foundCustomers.length > 0) {
                customerIdBigInt = foundCustomers[0].id;
                console.log(`   Found existing customer by name: ${customerIdBigInt}`);
            }
            else {
                console.log(`   Creating new customer without email`);
                const created = await (0, customer_service_1.createCustomer)({
                    name: customerName,
                    email: undefined,
                    phone: customerPhone || undefined,
                    address: customerAddress || undefined,
                    storeId: convo.storeId,
                    createdBy: convo.createdBy?.toString(),
                }, db);
                customerIdBigInt = created.id;
                console.log(`   Created new customer: ${customerIdBigInt}`);
            }
        }
        console.log(`[tools.placeOrder] Customer resolved:`, {
            customerId: customerIdBigInt.toString(),
            customerEmail: (customerEmail && customerEmail.trim() !== '') ? customerEmail : 'no email provided',
            searchMethod: (customerEmail && customerEmail.trim() !== '') ? 'by_email' : 'by_name'
        });
        try {
            console.log(`📝 Updating conversation with customer ID...`);
            await conversationRepo.update({ id: convo.id }, { customerId: customerIdBigInt });
        }
        catch (error) {
            logError('conversation_update', error, { conversationId: convo.id, customerId: customerIdBigInt });
            console.error('[tools.placeOrder] Failed to update conversation with customerId:', error);
        }
        try {
            console.log(`📋 Updating conversation context...`);
            const customerRecord = await customerRepo.findOne({
                where: { id: customerIdBigInt, isDeleted: false, storeId: convo.storeId },
                select: { id: true, name: true, email: true, phone: true },
            });
            const customerOrders = await orderRepo.find({
                where: { isDeleted: false, storeId: convo.storeId, customerId: customerIdBigInt },
                order: { createdAt: 'desc' },
                relations: { items: true },
                take: 5,
            });
            const currentCtx = convo.context || {};
            const nextCtx = { ...currentCtx, customer: customerRecord, customerOrders };
            await conversationRepo.update({ id: convo.id }, { context: nextCtx });
        }
        catch (error) {
            logError('context_update', error, { conversationId: convo.id, customerId: customerIdBigInt });
            console.error('[tools.placeOrder] Failed to update conversation context:', error);
        }
        console.log(`📦 Resolving products against catalog...`);
        const preparedItems = await (0, shared_1.resolveItemsAgainstCatalog)(items, convo.storeId, db);
        console.log(`   Prepared ${preparedItems.length} items`);
        const customerRecord = await customerRepo.findOne({
            where: { id: customerIdBigInt, isDeleted: false, storeId: convo.storeId },
            select: { name: true, email: true, phone: true },
        });
        console.log(`🛒 Creating order...`);
        const orderInput = {
            status: order_service_1.OrderStatusService.PENDING,
            priority: priority.toUpperCase() in order_service_1.OrderPriorityService ? order_service_1.OrderPriorityService[priority.toUpperCase()] : order_service_1.OrderPriorityService.NORMAL,
            useTax,
            taxRate,
            orderDate: new Date(),
            userId: convo.userId.toString(),
            storeId: convo.storeId,
            customerId: customerIdBigInt.toString(),
            customerName: customerRecord?.name || customerName,
            customerEmail: customerRecord?.email || customerEmail,
            customerPhone: customerPhone || customerRecord?.phone || '',
            customerAddress: customerAddress || customerRecord?.address || '',
            createdBy: convo.createdBy.toString(),
            items: preparedItems.map((it) => ({
                productId: it.productId.toString(),
                productName: it.productName,
                quantity: it.quantity,
                unitPrice: it.unitPrice,
                lineTotal: it.quantity * it.unitPrice,
                taxAmount: it.taxAmount,
            })),
        };
        console.log(`   Order input prepared:`, {
            customerId: orderInput.customerId,
            itemCount: orderInput.items.length,
            priority: orderInput.priority
        });
        const createdOrder = await (0, order_service_1.createOrder)(orderInput, db);
        const latestOrder = {
            id: createdOrder.id,
            orderNumber: createdOrder.orderNumber,
            total: createdOrder.total,
            customerId: customerIdBigInt,
            customerEmail,
            customerName,
            items: preparedItems.map((it) => ({ productId: it.productId, productName: it.productName, quantity: it.quantity, lineTotal: it.lineTotal })),
        };
        const current = convo.context || {};
        const nextContext = { ...current, latestOrder, orderBooked: true };
        try {
            await conversationRepo.update({ id: convo.id }, { context: nextContext });
        }
        catch (error) {
            logError('final_context_update', error, { conversationId: convo.id, orderId: createdOrder.id });
            console.error('[tools.placeOrder] Failed to update final conversation context:', error);
        }
        try {
            console.debug('[tools.placeOrder] created', { orderNumber: latestOrder.orderNumber, total: latestOrder.total });
        }
        catch { }
        console.log(`✅ Order created successfully: ${latestOrder.orderNumber}`);
        return {
            message: `Order ${latestOrder.orderNumber} has been created for ${customerName}${customerEmail && customerEmail.trim() !== '' ? ` (${customerEmail})` : ''} with ${latestOrder.items.length} item(s). Total: ${latestOrder.total}. Track your order at: frontendurl/track/${latestOrder.orderNumber}`,
            orderNumber: latestOrder.orderNumber,
            orderId: latestOrder.id,
            total: latestOrder.total,
            trackingUrl: `frontendurl/track/${latestOrder.orderNumber}`,
        };
    }
    catch (error) {
        logError('place_order_execution', error, {
            params,
            conversationUuid,
            timestamp: new Date().toISOString()
        });
        throw error;
    }
}
exports.placeOrder = (0, dist_1.createTool)(placeOrderTool, {
    name: 'placeOrder',
    description: 'Place a new order. Provide customerName, customerPhone, customerAddress, and items array. CustomerEmail is optional. Each item must have: quantity (positive number) and EITHER productName (string, recommended) OR productId (numeric ID, use only when exact ID is known). Optionally include useTax (boolean), taxRate (number), and priority (low/normal/high/urgent). Prefer productName over productId for better reliability.',
    parameterTypes: {
        customerName: { type: 'string' },
        customerPhone: { type: 'string' },
        customerAddress: { type: 'string' },
        customerEmail: { type: 'string', optional: true },
        items: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    productId: { type: 'number', optional: true },
                    productName: { type: 'string', optional: true },
                    quantity: { type: 'number' },
                    taxAmount: { type: 'number', optional: true },
                },
                required: ['quantity'],
            },
        },
        useTax: { type: 'boolean', optional: true },
        taxRate: { type: 'number', optional: true },
        priority: { type: 'string', optional: true },
    },
    requiredParams: ['customerName', 'customerPhone', 'customerAddress', 'items'],
});
//# sourceMappingURL=agent-tool-placeOrder.js.map