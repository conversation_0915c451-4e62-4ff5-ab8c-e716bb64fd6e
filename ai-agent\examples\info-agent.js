"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createInfoAgent = createInfoAgent;
const axios_1 = __importDefault(require("axios"));
const cheerio = __importStar(require("cheerio"));
const agent_1 = require("../src/agent");
const tools_1 = require("../src/tools");
/**
 * Tool for web scraping
 */
const webSearch = (0, tools_1.createTool)(async function webSearch(url) {
    try {
        const response = await axios_1.default.get(url, {
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });
        const $ = cheerio.load(response.data);
        // Remove script and style elements
        $('script, style').remove();
        // Get text content
        const text = $('body').text().replace(/\s+/g, ' ').trim();
        // Limit to first 2000 characters
        return text.substring(0, 2000);
    }
    catch (error) {
        return `Error fetching ${url}: ${error instanceof Error ? error.message : String(error)}`;
    }
}, {
    description: 'Search and scrape content from a website URL',
    parameterTypes: {
        url: String
    },
    requiredParams: ['url']
});
/**
 * Tool for getting current date/time
 */
const getCurrentTime = (0, tools_1.createTool)(function getCurrentTime() {
    return new Date().toISOString();
}, {
    description: 'Get the current date and time in ISO format'
});
/**
 * Create an information agent with web search capabilities
 */
function createInfoAgent() {
    return new agent_1.Agent('InfoAgent', `You are a helpful information assistant. You can search the web for current information and answer questions.
    
    Guidelines:
    - Always provide accurate and up-to-date information
    - Cite your sources when using web search
    - If you can't find information, say so clearly
    - Be concise but thorough in your responses`, [webSearch, getCurrentTime], 'Information research and web search assistant');
}
//# sourceMappingURL=info-agent.js.map