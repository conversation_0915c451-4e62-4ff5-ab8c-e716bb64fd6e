---
description: The feature implementation workflow for backend tasks
---

1. First You must first Analyse the full content of the following documentation (500 lines each): backend/README.md, backend/docs/API_LAYER.md, backend/docs/ENTITY.md, backend/docs/LOGGER_REFERENCE.md, backend/docs/PROJECT_STRUCTURE.md, backend/docs/DATABASE_MIGRATION.
2. Analyse other files provided by user.
3. Make sure to report issues and list the missing list of function or features and prompt the user.
4. Next, look at the user request and fullfill it.
5. Make sure to keep TODO list and update the TODO list often.
6. You must report all errors and potential issues you encounter
7. You are prohibited from starting the backend server for testing, only build is allowed

- We are doing production build, make sure to not use developpment specific tools or commands.
- Ask questions if you are not sure what to do
- Always report issues you find