import { Response } from 'express';
import { ImagesService } from './images.service';
export declare class ImagesController {
    private readonly imagesService;
    constructor(imagesService: ImagesService);
    uploadImage(file: any): Promise<{
        filename: string;
    }>;
    getImage(filename: string, res: Response): Promise<void>;
    deleteImage(filename: string): Promise<{
        message: string;
    }>;
}
