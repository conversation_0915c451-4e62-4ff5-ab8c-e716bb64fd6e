{"version": 3, "file": "google.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/strategies/google.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAAoD;AACpD,qEAAmE;AACnE,2CAA4C;AAC5C,2CAA+C;AAGxC,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,IAAA,2BAAgB,EAAC,kCAAQ,EAAE,QAAQ,CAAC;IACtE,YAAoB,aAA4B;QAC9C,KAAK,CAAC;YACJ,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC;YACvD,YAAY,EAAE,aAAa,CAAC,GAAG,CAAS,sBAAsB,CAAC;YAC/D,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,qBAAqB,CAAC,IAAI,4CAA4C;YAC7G,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;SAC5B,CAAC,CAAC;QANe,kBAAa,GAAb,aAAa,CAAe;IAOhD,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,WAAmB,EACnB,YAAoB,EACpB,OAAY,EACZ,IAAoB;QAEpB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAGzC,MAAM,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QACnE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,EAAE,IAAI,CAAC,CAAC;QACrE,CAAC;QAGD,IAAI,WAAW,GAAG,cAAc,CAAC;QACjC,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtC,WAAW,GAAG,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACvD,CAAC;iBAAM,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC1B,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC;YAC/B,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC3B,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YAChC,CAAC;QACH,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAErE,MAAM,IAAI,GAAG;YACX,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,KAAK;YACL,IAAI,EAAE,WAAW;YACjB,OAAO;SACR,CAAC;QAEF,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACnB,CAAC;CACF,CAAA;AAhDY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAEwB,sBAAa;GADrC,cAAc,CAgD1B"}