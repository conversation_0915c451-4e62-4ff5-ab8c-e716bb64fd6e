"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createBigIntSafeDataSourceOptions = createBigIntSafeDataSourceOptions;
exports.createBigIntSafeQueryBuilder = createBigIntSafeQueryBuilder;
exports.withBigIntHandling = withBigIntHandling;
exports.setupBigIntErrorHandling = setupBigIntErrorHandling;
exports.containsBigInt = containsBigInt;
exports.safeClone = safeClone;
const bigint_handler_1 = require("./bigint-handler");
function createBigIntSafeDataSourceOptions(baseOptions) {
    return {
        ...baseOptions,
    };
}
function createBigIntSafeQueryBuilder(queryBuilder, options) {
    const originalGetMany = queryBuilder.getMany;
    const originalGetOne = queryBuilder.getOne;
    const originalGetCount = queryBuilder.getCount;
    const originalGetRawAndEntities = queryBuilder.getRawAndEntities;
    if (options?.transformResults !== false) {
        queryBuilder.getMany = async function () {
            const results = await originalGetMany.call(this);
            return (0, bigint_handler_1.handleTypeORMResult)(results);
        };
        queryBuilder.getOne = async function () {
            const result = await originalGetOne.call(this);
            return result ? (0, bigint_handler_1.handleTypeORMResult)(result) : null;
        };
        queryBuilder.getRawAndEntities = async function () {
            const [rawResults, entities] = await originalGetRawAndEntities.call(this);
            return [
                (0, bigint_handler_1.handleTypeORMResult)(rawResults),
                (0, bigint_handler_1.handleTypeORMResult)(entities)
            ];
        };
    }
    if (options?.handleCounts !== false) {
        queryBuilder.getCount = async function () {
            const count = await originalGetCount.call(this);
            if (typeof count === 'bigint') {
                return Number(count);
            }
            return count;
        };
    }
    return queryBuilder;
}
function withBigIntHandling(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    if (originalMethod) {
        descriptor.value = async function (...args) {
            try {
                const result = await originalMethod.apply(this, args);
                return (0, bigint_handler_1.handleTypeORMResult)(result);
            }
            catch (error) {
                if (error instanceof Error && error.message.includes('BigInt')) {
                    console.warn(`BigInt serialization error in ${propertyKey}, attempting recovery...`);
                    if (error.stack) {
                        console.warn('Error stack:', error.stack);
                    }
                }
                throw error;
            }
        };
    }
    return descriptor;
}
function setupBigIntErrorHandling() {
    (0, bigint_handler_1.setupGlobalBigIntHandler)();
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;
    console.log = function (...args) {
        const transformedArgs = args.map(arg => (0, bigint_handler_1.handleTypeORMResult)(arg));
        originalLog.apply(console, transformedArgs);
    };
    console.warn = function (...args) {
        const transformedArgs = args.map(arg => (0, bigint_handler_1.handleTypeORMResult)(arg));
        originalWarn.apply(console, transformedArgs);
    };
    console.error = function (...args) {
        const transformedArgs = args.map(arg => (0, bigint_handler_1.handleTypeORMResult)(arg));
        originalError.apply(console, transformedArgs);
    };
    console.log('BigInt error handling setup complete');
}
function containsBigInt(obj) {
    if (obj === null || obj === undefined) {
        return false;
    }
    if (typeof obj === 'bigint') {
        return true;
    }
    if (Array.isArray(obj)) {
        return obj.some(containsBigInt);
    }
    if (typeof obj === 'object') {
        return Object.values(obj).some(containsBigInt);
    }
    return false;
}
function safeClone(obj) {
    if (obj === null || obj === undefined) {
        return obj;
    }
    if (typeof obj === 'bigint') {
        return obj.toString();
    }
    if (Array.isArray(obj)) {
        return obj.map(safeClone);
    }
    if (typeof obj === 'object') {
        const cloned = {};
        for (const [key, value] of Object.entries(obj)) {
            cloned[key] = safeClone(value);
        }
        return cloned;
    }
    return obj;
}
//# sourceMappingURL=typeorm-bigint-config.js.map