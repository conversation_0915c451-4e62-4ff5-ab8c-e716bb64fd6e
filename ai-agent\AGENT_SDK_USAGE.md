### Agent SDK: TypeScript/Node.js Usage Guide

This guide shows how to use the Agent SDK in TypeScript/Node.js for building LLM-powered agents with tool-calling capabilities.

### Prerequisites

- Node.js 18+
- npm or yarn
- Install dependencies:

```bash
cd ai-agent
npm install
```

- Environment variables (e.g., in a `.env` file at repo root):

```bash
API_KEY=your_api_key_here
BASE_URL=https://openrouter.ai/api/v1      # Optional for custom endpoints
MODEL=google/gemini-2.5-flash-lite         # Optional, model to use
```

### Build and Development

```bash
# Build TypeScript to JavaScript
npm run build

# Development with ts-node
npm run dev

# Clean build artifacts
npm run clean
```

### Basic Usage

#### 1) Simple Calculator Agent

```typescript
// examples/calculator.ts
import { Agent, Runner, createTool, setLlmProvider } from 'dido-agent-sdk';

// Set up the LLM provider (optional if using environment variables)
setLlmProvider('your-api-key-here');

// Create a calculator tool
const calculator = createTool(
  function calculate(expression: string): number {
    try {
      // Simple evaluation (in production, use a proper math parser)
      return Function(`"use strict"; return (${expression})`)();
    } catch (error) {
      throw new Error(`Invalid expression: ${expression}`);
    }
  },
  {
    description: 'Calculate mathematical expressions',
    parameterTypes: { expression: String },
    requiredParams: ['expression']
  }
);

// Create the agent
const calculatorAgent = new Agent(
  'CalculatorAgent',
  'You are a calculator assistant. Help users with mathematical calculations.',
  [calculator]
);

// Run the agent
async function main() {
  const result = await Runner.run(calculatorAgent, 'What is 15 * 8 + 23?');
  console.log('Result:', result);
}

main().catch(console.error);
```

#### 2) Using Decorators for Tools

```typescript
// examples/weather-service.ts
import { Agent, Runner, functionTool } from 'dido-agent-sdk';

class WeatherService {
  @functionTool({
    description: 'Get weather information for a city',
    parameterTypes: { city: String },
    requiredParams: ['city']
  })
  async getWeather(city: string): Promise<string> {
    // Mock weather data - in production, use a real weather API
    const weather = {
      temperature: Math.floor(Math.random() * 30) + 10,
      condition: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)]
    };
    
    return `Weather in ${city}: ${weather.temperature}°C, ${weather.condition}`;
  }
}

async function main() {
  const weatherService = new WeatherService();
  
  const agent = new Agent(
    'WeatherAgent',
    'You are a weather assistant. Provide weather information for cities.',
    [weatherService.getWeather.bind(weatherService)]
  );
  
  const result = await Runner.run(agent, 'What is the weather like in London?');
  console.log('Weather result:', result);
}

main().catch(console.error);
```

#### 3) Pre-built Agents

The SDK includes pre-built agents for common use cases:

```typescript
// examples/prebuilt-agents.ts
import { createInfoAgent, createSaleAgent, Runner } from 'dido-agent-sdk';

async function useInfoAgent() {
  const agent = createInfoAgent();
  const result = await Runner.run(agent, 'What is the current time?');
  console.log('Info agent result:', result);
}

async function useSaleAgent() {
  // Note: Requires backend API to be running
  const agent = createSaleAgent();
  const result = await Runner.run(agent, 'Search for products related to "electronics"');
  console.log('Sale agent result:', result);
}
```

### Advanced Usage

#### Creating Custom Agents with Multiple Tools

```typescript
// examples/advanced-agent.ts
import { Agent, Runner, createTool } from 'dido-agent-sdk';
import axios from 'axios';

// File system tool
const readFile = createTool(
  async function readFile(filePath: string): Promise<string> {
    const fs = await import('fs/promises');
    try {
      return await fs.readFile(filePath, 'utf-8');
    } catch (error) {
      throw new Error(`Cannot read file: ${filePath}`);
    }
  },
  {
    description: 'Read contents of a file',
    parameterTypes: { filePath: String },
    requiredParams: ['filePath']
  }
);

// HTTP request tool
const httpGet = createTool(
  async function httpGet(url: string): Promise<string> {
    try {
      const response = await axios.get(url, { timeout: 10000 });
      return JSON.stringify(response.data, null, 2);
    } catch (error) {
      throw new Error(`HTTP request failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
  {
    description: 'Make HTTP GET request to a URL',
    parameterTypes: { url: String },
    requiredParams: ['url']
  }
);

// Create agent with multiple tools
const assistantAgent = new Agent(
  'AssistantAgent',
  `You are a helpful assistant that can read files and make HTTP requests.
  
  Guidelines:
  - Be careful with file paths and URLs
  - Provide clear error messages when operations fail
  - Always explain what you're doing before using tools`,
  [readFile, httpGet]
);

async function main() {
  const result = await Runner.run(
    assistantAgent, 
    'Can you read the package.json file and tell me about this project?'
  );
  console.log('Assistant result:', result);
}

main().catch(console.error);
```

#### Interactive REPL

```typescript
// examples/interactive.ts
import readline from 'readline';
import { Agent, Runner, createInfoAgent } from 'dido-agent-sdk';

async function startInteractiveSession(agent: Agent) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log(`${agent.name} ready. Type 'exit' to quit.\n`);

  const askQuestion = (): Promise<string> => {
    return new Promise((resolve) => {
      rl.question('You: ', (answer) => {
        resolve(answer.trim());
      });
    });
  };

  while (true) {
    try {
      const userInput = await askQuestion();
      
      if (!userInput) continue;
      
      if (userInput.toLowerCase() === 'exit') {
        console.log('Goodbye!');
        break;
      }

      const reply = await Runner.run(agent, userInput);
      console.log(`${agent.name}: ${reply}\n`);
    } catch (error) {
      console.error('Error:', error instanceof Error ? error.message : String(error));
    }
  }

  rl.close();
}

// Start interactive session
const agent = createInfoAgent();
startInteractiveSession(agent).catch(console.error);
```

### API Reference

#### Core Classes

- **`Agent`**: Represents an LLM-powered agent with tools
- **`Runner`**: Executes agents with the OpenAI API
- **`LLMProvider`**: Manages API configuration

#### Tool Creation

- **`createTool(func, config)`**: Create a tool from a function
- **`@functionTool(config)`**: Decorator for marking methods as tools

#### Pre-built Agents

- **`createInfoAgent()`**: Information research and web search
- **`createSaleAgent()`**: Sales assistant for products and quotes

### File Structure

```
ai-agent/
├── src/
│   ├── agent.ts           # Agent class
│   ├── runner.ts          # Runner class
│   ├── llm-provider.ts    # LLM configuration
│   ├── tools.ts           # Tool creation utilities
│   ├── schema.ts          # Schema generation
│   ├── types.ts           # Type definitions
│   ├── agents/            # Pre-built agents
│   │   ├── info-agent.ts
│   │   ├── sale-agent.ts
│   │   └── index.ts
│   └── index.ts           # Main exports
├── examples/              # Usage examples
├── dist/                  # Compiled JavaScript
├── package.json
└── tsconfig.json
```

### Notes

- Keep tools small and focused with clear type annotations
- Use proper error handling in tool implementations
- The SDK automatically handles JSON serialization for tool results
- Environment variables take precedence over programmatic configuration
- All operations are async and return Promises

### Migration from Python

If migrating from the Python version:

1. Replace `@function_tool` with `createTool()` or `@functionTool()`
2. Use `async/await` for asynchronous operations
3. Replace Python type hints with TypeScript interfaces
4. Use `Runner.run()` instead of `Runner.run()` (now async)
5. Configure with `setLlmProvider()` or environment variables