import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne } from 'typeorm';
import { CustomerStatus } from '../shared/enums';

@Entity('customers')
export class Customer {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  email: string;

  @Column({ type: 'varchar', nullable: true })
  phone: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ type: 'varchar', nullable: true })
  city: string;

  @Column({ type: 'varchar', nullable: true })
  state: string;

  @Column({ type: 'varchar', nullable: true })
  zipCode: string;

  @Column({ type: 'varchar', nullable: true })
  country: string;

  @Column({ type: 'enum', enum: CustomerStatus, default: CustomerStatus.ACTIVE })
  status: CustomerStatus;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint' })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  @Column({ type: 'bigint' })
  storeId: string;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.createdCustomers)
  createdByUser: any;

  @ManyToOne('User', (user: any) => user.updatedCustomers)
  updatedByUser: any;

  @ManyToOne('Store', (store: any) => store.customers)
  store: any;

  @OneToMany('Order', (order: any) => order.customer)
  orders: any[];
}
