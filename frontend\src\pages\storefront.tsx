import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { storeApi } from '../utils/api';
import { SupportedLanguage, LANGUAGE_OPTIONS, getBrowserPreferenceDefaults, getTranslation } from '../utils/preferences';

export default function StorefrontDirectoryPage() {
  const router = useRouter();
  const [selectedStore, setSelectedStore] = useState<string>('');
  const [selectedLanguage, setSelectedLanguage] = useState<SupportedLanguage>('en-US');

  // State for API calls
  const [stores, setStores] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<any>(null);

  // Load language preference from localStorage or use browser defaults
  useEffect(() => {
    const savedPreferences = localStorage.getItem('storefront-preferences');
    if (savedPreferences) {
      try {
        const { language } = JSON.parse(savedPreferences);
        setSelectedLanguage(language);
      } catch (error) {
        console.warn('Failed to parse saved preferences:', error);
        const defaults = getBrowserPreferenceDefaults();
        setSelectedLanguage(defaults.language);
      }
    } else {
      const defaults = getBrowserPreferenceDefaults();
      setSelectedLanguage(defaults.language);
    }
  }, []);

  // Fetch stores on component mount
  useEffect(() => {
    fetchStores();
  }, []);

  const fetchStores = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await storeApi.getAll({ page: 1, limit: 100 });
      setStores((data as any)?.data ?? []);
    } catch (err) {
      setError(err);
      console.error('Failed to fetch stores:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Save language preference to localStorage when it changes
  const saveLanguagePreference = (language: SupportedLanguage) => {
    const preferences = {
      language,
      timestamp: Date.now(),
      version: '1.0'
    };
    localStorage.setItem('storefront-preferences', JSON.stringify(preferences));
    setSelectedLanguage(language);
  };

  const goToStore = (storeUuid: string) => {
    router.push(`/store/${storeUuid}`);
  };

  const handleStoreSelection = () => {
    if (selectedStore) {
      goToStore(selectedStore);
    }
  };

  return (
    <div className="min-h-screen bg-white text-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to Teno Store
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover amazing stores and products from around the world
          </p>
        </div>

        {/* Language Selector */}
        <div className="flex justify-center mb-8">
          <div className="bg-gray-100 rounded-lg p-2">
            {LANGUAGE_OPTIONS.map((lang) => (
              <button
                key={lang}
                onClick={() => saveLanguagePreference(lang)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  selectedLanguage === lang
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {lang.toUpperCase()}
              </button>
            ))}
          </div>
        </div>

        {/* Store Selection */}
        <div className="max-w-md mx-auto mb-12">
          <div className="bg-gray-50 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Select a Store
            </h2>
            <div className="space-y-4">
              <select
                value={selectedStore}
                onChange={(e) => setSelectedStore(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">
                  Choose a store...
                </option>
                {stores.map((store) => (
                  <option key={store.uuid} value={store.uuid}>
                    {store.name}
                  </option>
                ))}
              </select>
              <button
                onClick={handleStoreSelection}
                disabled={!selectedStore}
                className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Visit Store
              </button>
            </div>
          </div>
        </div>

        {/* Store List */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {isLoading ? (
            // Loading skeleton
            Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-6 animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-4"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            ))
          ) : error ? (
            // Error state
            <div className="col-span-full text-center py-12">
              <p className="text-red-600 mb-4">
                Error loading stores: {error.message}
              </p>
              <button
                onClick={fetchStores}
                className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700"
              >
                Retry
              </button>
            </div>
          ) : stores.length > 0 ? (
            // Store cards
            stores.map((store) => (
              <div
                key={store.uuid}
                className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => goToStore(store.uuid)}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">{store.name}</h3>
                  <span className="text-sm text-gray-500">{store.currency}</span>
                </div>
                {store.description && (
                  <p className="text-gray-600 mb-4 line-clamp-2">{store.description}</p>
                )}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>{store.preferredLanguage}</span>
                  <span>{new Date(store.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            ))
          ) : (
            // Empty state
            <div className="col-span-full text-center py-12">
              <p className="text-gray-600">
                No stores available at the moment.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}