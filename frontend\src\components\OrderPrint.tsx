import { useEffect, useMemo } from 'react';
import { printHtml, renderOrderToHtml } from '../utils/print';
import { usePreferences } from '../context/PreferencesContext';

type OrderPrintProps = {
	order: any;
	currencyFormatter: Intl.NumberFormat;
	isOpen: boolean;
	onClose: () => void;
};

export default function OrderPrint({ order, currencyFormatter, isOpen, onClose }: OrderPrintProps) {
	const { language } = usePreferences();
	const printTitle = useMemo(() => `Order ${order?.orderNumber ?? `#${order?.id?.toString?.() ?? ''}`}`,[order]);

	useEffect(() => {
		if (!isOpen || !order) return;
		const html = renderOrderToHtml(order, currencyFormatter, language);
		printHtml(html, {
			title: printTitle,
			onAfterPrint: onClose
		});
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [isOpen, order, language]);

	// Renders nothing, acts as a controller to trigger printing
	return null;
}


