{"c": ["pages/products", "webpack"], "r": ["pages/dashboard", "pages/customers", "pages/conversations", "/_error"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cdashboard.tsx&page=%2Fdashboard!", "./src/pages/dashboard.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Ccustomers%5Cindex.tsx&page=%2Fcustomers!", "./src/components/CustomerModals.tsx", "./src/pages/customers/index.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cconversations%5Cindex.tsx&page=%2Fconversations!", "./src/components/MessageFormatter.tsx", "./src/components/TimelineItem.tsx", "./src/pages/conversations/index.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"]}