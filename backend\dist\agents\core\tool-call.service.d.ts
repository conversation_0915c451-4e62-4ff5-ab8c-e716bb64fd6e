import { DataSource } from 'typeorm';
export interface CreateToolCallInput {
    toolName: string;
    toolInput: any;
    conversationId: bigint;
    createdBy: bigint;
    toolOutput?: any;
    executionTime?: number;
    success?: boolean;
    errorMessage?: string;
    cost?: number;
    inputTokens?: number;
    outputTokens?: number;
}
export interface UpdateToolCallInput {
    id: bigint;
    toolOutput?: any;
    executionTime?: number;
    success?: boolean;
    errorMessage?: string;
    cost?: number;
    inputTokens?: number;
    outputTokens?: number;
    updatedBy: bigint;
}
export interface ToolCallFilter {
    conversationId?: bigint;
    toolName?: string;
    success?: boolean;
    createdAfter?: Date;
    createdBefore?: Date;
}
export declare function createToolCall(db: DataSource, input: CreateToolCallInput): Promise<any>;
export declare function updateToolCall(db: DataSource, input: UpdateToolCallInput): Promise<any>;
export declare function getToolCallsByConversation(db: DataSource, conversationId: bigint, limit?: number, offset?: number): Promise<any[]>;
export declare function getToolCalls(db: DataSource, filter: ToolCallFilter, limit?: number, offset?: number): Promise<any[]>;
export declare function getToolCallById(db: DataSource, id: bigint): Promise<any | null>;
export declare function deleteToolCall(db: DataSource, id: bigint, deletedBy: bigint): Promise<boolean>;
export declare function getToolCallStats(db: DataSource, conversationId: bigint): Promise<{
    total: number;
    successful: number;
    failed: number;
    averageExecutionTime: number | null;
    toolUsage: Record<string, number>;
}>;
