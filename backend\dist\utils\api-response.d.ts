export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    details?: any;
    meta?: {
        page?: number;
        limit?: number;
        total?: number;
        totalPages?: number;
        hasNext?: boolean;
        hasPrev?: boolean;
    };
}
export interface PaginationMeta {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}
export declare function successResponse<T>(data: T, meta?: Partial<PaginationMeta>): ApiResponse<T>;
export declare function errorResponse(message: string, statusCode?: number, details?: any): ApiResponse;
export declare function createPaginationMeta(page: number, limit: number, total: number): PaginationMeta;
