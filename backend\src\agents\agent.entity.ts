import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne } from 'typeorm';

@Entity('agents')
export class Agent {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar' })
  type: string;

  @Column({ type: 'json' })
  configuration: any;

  @Column({ type: 'varchar', nullable: true })
  status: string;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint' })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  @Column({ type: 'bigint' })
  storeId: string;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.createdAgents)
  createdByUser: any;

  @ManyToOne('User', (user: any) => user.updatedAgents)
  updatedByUser: any;

  @ManyToOne('Store', (store: any) => store.agents)
  store: any;

  @OneToMany('Conversation', (conversation: any) => conversation.agent)
  conversations: any[];
}
