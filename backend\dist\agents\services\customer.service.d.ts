import { Customer } from '../../customers/customer.entity';
export interface CustomerFilter {
    storeId?: string | bigint;
    email?: string;
    name?: string;
    phone?: string;
}
export interface CreateCustomerInput {
    email?: string;
    name: string;
    phone?: string;
    address?: string;
    storeId: string | bigint;
    createdBy?: string;
}
export declare function filterCustomers(filter: CustomerFilter, db: any): Promise<Customer[]>;
export declare function createCustomer(input: CreateCustomerInput, db: any): Promise<Customer>;
export declare function updateCustomer(customerId: string, updateData: Partial<Customer>, db: any): Promise<Customer>;
