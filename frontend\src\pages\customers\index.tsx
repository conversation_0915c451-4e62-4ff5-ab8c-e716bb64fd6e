import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { customerApi } from '../../utils/api';
import { useAuth } from '../../context/AuthContext';
import { useStore } from '../../context/StoreContext';
import TopTaskBar from '../../components/TopTaskBar';
import SideTaskBar from '../../components/SideTaskBar';
import EntityTable from '../../components/EntityTable';
import { CustomerCreateModal, CustomerUpdateModal } from '../../components/CustomerModals';

// Custom hook for F1 keyboard shortcut
function useAddEntityShortcut(isEnabled: boolean, onOpen: () => void) {
  useEffect(() => {
    if (!isEnabled) return;

    const handler = (event: KeyboardEvent) => {
      if (event.key !== 'F1') return;

      const target = event.target as HTMLElement | null;
      const tag = target?.tagName?.toLowerCase();
      const isFormField = tag === 'input' || tag === 'textarea' || tag === 'select' || (target?.isContentEditable ?? false);
      if (isFormField) return;

      event.preventDefault();
      onOpen();
    };

    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [isEnabled, onOpen]);
}

export default function CustomersPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { currentStoreId } = useStore();

  const [page, setPage] = useState(1);
  const limit = 20;

  // State for API calls
  const [customersData, setCustomersData] = useState<any[]>([]);
  const [customersMeta, setCustomersMeta] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<any>(null);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);

  // F1 keyboard shortcut
  const isShortcutEnabled = Boolean(currentStoreId) && !showCreateModal;
  useAddEntityShortcut(isShortcutEnabled, () => setShowCreateModal(true));

  useEffect(() => {
    setPage(1);
  }, []);

  // Reset to first page when store changes
  useEffect(() => {
    setPage(1);
  }, [currentStoreId]);

  // Fetch customers when store changes
  useEffect(() => {
    if (user && currentStoreId) {
      fetchCustomers();
    }
  }, [user, currentStoreId, page]);

  const fetchCustomers = async () => {
    if (!user || !currentStoreId) return;
    
    setIsLoading(true);
    setError(null);
    try {
      const data = await customerApi.getByStoreId(currentStoreId, { page, limit });
      setCustomersData((data as any)?.data ?? []);
      setCustomersMeta((data as any)?.meta ?? null);
    } catch (err) {
      setError(err);
      console.error('Failed to fetch customers:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateCustomer = async (customerData: any) => {
    try {
      await customerApi.create(customerData);
      await fetchCustomers(); // Refresh the list
    } catch (err) {
      throw err; // Re-throw to let the modal handle the error
    }
  };

  const handleUpdateCustomer = async (customerData: any) => {
    if (!selectedCustomer?.id) return;
    
    try {
      await customerApi.update(selectedCustomer.id, customerData);
      await fetchCustomers(); // Refresh the list
    } catch (err) {
      throw err; // Re-throw to let the modal handle the error
    }
  };

  const handleDeleteCustomer = async (customerId: string) => {
    if (!confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      return;
    }

    try {
      await customerApi.delete(customerId);
      await fetchCustomers(); // Refresh the list
    } catch (err) {
      console.error('Failed to delete customer:', err);
      alert('Failed to delete customer. Please try again.');
    }
  };

  const openUpdateModal = (customer: any) => {
    setSelectedCustomer(customer);
    setShowUpdateModal(true);
  };

  // Define table columns
  const columns = useMemo(() => [
    {
      key: 'name',
      header: 'Customer Name',
      render: (value: string, row: any) => (
        <div>
          <div className="font-medium text-slate-100">{value}</div>
          {row.email && (
            <div className="text-sm text-slate-400">{row.email}</div>
          )}
        </div>
      ),
    },
    {
      key: 'phone',
      header: 'Phone',
      render: (value: string) => value || '-',
      cellClassName: 'text-slate-300',
    },
    {
      key: 'address',
      header: 'Address',
      render: (value: string) => value || '-',
      cellClassName: 'text-slate-300',
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (value: string) => (
        <div className="text-sm">
          <div className="text-slate-100">{new Date(value).toLocaleDateString()}</div>
          <div className="text-xs text-slate-400">{new Date(value).toLocaleTimeString()}</div>
        </div>
      ),
    },
    {
      key: 'id',
      header: 'Customer ID',
      render: (value: string) => (
        <span className="text-xs text-slate-400 font-mono">{value}</span>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (value: any, row: any) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => openUpdateModal(row)}
            className="text-slate-300 hover:text-emerald-400 rounded-md p-2 border border-white/10 hover:bg-white/5"
            title="Edit"
            aria-label="Edit"
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span className="sr-only">Edit</span>
          </button>
          <button
            onClick={() => handleDeleteCustomer(row.id)}
            className="text-slate-300 hover:text-red-400 rounded-md p-2 border border-white/10 hover:bg-white/5"
            title="Delete"
            aria-label="Delete"
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            <span className="sr-only">Delete</span>
          </button>
        </div>
      ),
    },
  ], []);

  if (!user) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-slate-100 mb-4">Authentication Required</h2>
          <p className="text-slate-300 mb-4">Please log in to view customers.</p>
          <button
            onClick={() => router.push('/login')}
            className="bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  if (!currentStoreId) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-slate-100 mb-4">Store Selection Required</h2>
          <p className="text-slate-300 mb-4">Please select a store to view customers.</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Customers - Teno Store</title>
        <meta name="description" content="Manage your store customers" />
      </Head>

      <div className="min-h-screen bg-slate-900">
        <TopTaskBar />
        
        <div className="flex">
          <SideTaskBar />
          
          <main className="flex-1 p-6">
            <div className="max-w-7xl mx-auto">
              <div className="mb-8 flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-extrabold tracking-tight text-slate-100">Customers</h1>
                  <p className="text-slate-300 mt-2">Manage your store customers</p>
                </div>
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors flex items-center gap-2"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Customer
                </button>
              </div>

              {error ? (
                <div className="bg-red-500/10 border border-red-500/20 text-red-300 px-4 py-3 rounded-md mb-6">
                  <p className="mb-2">Error loading customers: {error.message}</p>
                  <button
                    onClick={fetchCustomers}
                    className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors text-sm"
                  >
                    Retry
                  </button>
                </div>
              ) : (
                <EntityTable
                  columns={columns}
                  data={customersData}
                  isLoading={isLoading}
                  loadingText="Loading customers..."
                  noDataText={
                    <div className="text-center">
                      <p className="text-slate-400 mb-2">No customers found for this store.</p>
                      <p className="text-sm text-slate-500">Customers will appear here once they place orders or you can create them manually.</p>
                    </div>
                  }
                  pagination={
                    customersMeta
                      ? {
                          currentPage: page,
                          totalPages: customersMeta.totalPages,
                          onPageChange: setPage,
                          totalItems: customersMeta.total,
                          itemsPerPage: limit,
                        }
                      : undefined
                  }
                />
              )}
            </div>
          </main>
        </div>
      </div>

      {/* Create Customer Modal */}
      <CustomerCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateCustomer}
        storeId={currentStoreId}
      />

      {/* Update Customer Modal */}
      <CustomerUpdateModal
        isOpen={showUpdateModal}
        onClose={() => {
          setShowUpdateModal(false);
          setSelectedCustomer(null);
        }}
        onSubmit={handleUpdateCustomer}
        customer={selectedCustomer}
      />
    </>
  );
}
