import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useAuth } from './AuthContext';

interface StoreContextValue {
  currentStoreId: string | null;
  setCurrentStoreId: (storeId: string | null) => void;
  clearStore: () => void;
  autoSelectFirstStore: (stores: any[]) => void;
}

const StoreContext = createContext<StoreContextValue | undefined>(undefined);

export function StoreProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [currentStoreId, setCurrentStoreIdState] = useState<string | null>(null);

  const storageKey = useMemo(() => {
    const userId = user?.id ? String(user.id) : 'anon';
    return `teno:currentStoreId:${userId}`;
  }, [user?.id]);

  // Hydrate from localStorage on mount or when user changes
  useEffect(() => {
    try {
      const raw = localStorage.getItem(storageKey);
      if (raw) {
        setCurrentStoreIdState(raw);
      } else {
        setCurrentStoreIdState(null);
      }
    } catch {}
  }, [storageKey]);

  // Clear store when user changes (login/logout) because key scope changes
  useEffect(() => {
    const onAuthChange = () => {
      setCurrentStoreIdState(null);
    };
    if (typeof window !== 'undefined') {
      window.addEventListener('teno:auth:change', onAuthChange as EventListener);
    }
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('teno:auth:change', onAuthChange as EventListener);
      }
    };
  }, []);

  const setCurrentStoreId = useCallback((storeId: string | null) => {
    setCurrentStoreIdState(storeId);
    try {
      if (storeId) localStorage.setItem(storageKey, storeId);
      else localStorage.removeItem(storageKey);
    } catch {}
  }, [storageKey]);

  const clearStore = useCallback(() => {
    setCurrentStoreId(null);
  }, [setCurrentStoreId]);

  const autoSelectFirstStore = useCallback((stores: any[]) => {
    if (stores.length > 0 && !currentStoreId) {
      const firstStore = stores[0];
      setCurrentStoreId(firstStore.id);
    }
  }, [currentStoreId, setCurrentStoreId]);

  const value = useMemo<StoreContextValue>(() => ({
    currentStoreId,
    setCurrentStoreId,
    clearStore,
    autoSelectFirstStore,
  }), [currentStoreId, setCurrentStoreId, clearStore, autoSelectFirstStore]);

  return (
    <StoreContext.Provider value={value}>
      {children}
    </StoreContext.Provider>
  );
}

export function useStore(): StoreContextValue {
  const ctx = useContext(StoreContext);
  if (!ctx) {
    throw new Error('useStore must be used within a StoreProvider');
  }
  return ctx;
}

 