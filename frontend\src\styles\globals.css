@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  --bg-primary: #0B1020;
  --brand-emerald: #10B981;
  --brand-cyan: #22D3EE;
  --brand-indigo: #6366F1;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer components {
  /* App background container for dark agentic theme */
  .app-bg {
    @apply relative min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 overflow-hidden;
    background-color: var(--bg-primary);
  }

  /* Radial glow background overlay */
  .glow-overlay {
    @apply pointer-events-none absolute inset-0 opacity-40;
    background: radial-gradient(1200px 600px at -10% -20%, #2E5BFF 10%, transparent 60%),
                radial-gradient(800px 400px at 120% 110%, var(--brand-emerald) 5%, transparent 50%);
  }

  /* Subtle grid overlay */
  .grid-overlay {
    @apply pointer-events-none absolute inset-0;
    opacity: 0.08;
    background-image: radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.6) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  /* Glass card shell */
  .glass-card {
    @apply relative rounded-xl border border-white/10 bg-white/5 backdrop-blur-xl shadow-2xl p-6 sm:p-8;
  }

  /* Optional gradient accent layer for cards (use as an empty absolutely positioned div inside .glass-card) */
  .card-accent {
    @apply pointer-events-none absolute -inset-px rounded-xl;
    background-image: linear-gradient(90deg, color-mix(in oklab, var(--brand-emerald) 25%, transparent), color-mix(in oklab, var(--brand-cyan) 25%, transparent), color-mix(in oklab, var(--brand-indigo) 25%, transparent));
  }

  /* Brand gradient text utility */
  .brand-gradient-text {
    background-image: linear-gradient(90deg, var(--brand-emerald), var(--brand-cyan), var(--brand-indigo));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  /* Primary CTA button for dark theme */
  .primary-button {
    @apply relative w-full flex items-center justify-center gap-3 py-3 px-4 rounded-lg text-sm font-medium text-slate-100 bg-slate-900/60 ring-1 ring-white/10 hover:bg-slate-900/80 hover:ring-emerald-400/50 focus:outline-none focus:ring-2 focus:ring-emerald-400/60 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;
  }

  /* Hover glow layer to nest inside buttons if desired */
  .button-glow {
    @apply absolute inset-0 -z-10 rounded-lg opacity-0 blur-xl transition-opacity duration-300;
    background-image: linear-gradient(90deg, color-mix(in oklab, var(--brand-emerald) 20%, transparent), color-mix(in oklab, var(--brand-cyan) 20%, transparent), color-mix(in oklab, var(--brand-indigo) 20%, transparent));
  }

  /* Subtle muted text for descriptions */
  .subtle-text {
    @apply text-sm text-slate-300/80;
  }

  /* Small rounded icon badge container */
  .icon-badge {
    @apply mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-white/5 ring-1 ring-white/10 shadow-md backdrop-blur;
  }
}

