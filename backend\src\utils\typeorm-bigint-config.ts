/**
 * TypeORM configuration utilities for handling BigInt serialization issues
 */

import { DataSourceOptions } from 'typeorm';
import { setupGlobalBigIntHandler, handleTypeORMResult } from './bigint-handler';

/**
 * Extends TypeORM DataSourceOptions with BigInt handling configuration
 */
export type BigIntSafeDataSourceOptions = DataSourceOptions & {
  // Add any additional options specific to BigInt handling
};

/**
 * Creates TypeORM configuration options with BigInt serialization handling
 * This can be used to configure the AppDataSource with proper BigInt handling
 */
export function createBigIntSafeDataSourceOptions(
  baseOptions: DataSourceOptions
): BigIntSafeDataSourceOptions {
  return {
    ...baseOptions,
    // Add any specific BigInt handling configurations here
    // For example, you could add custom query result transformers
  };
}

/**
 * TypeORM query result transformer that automatically handles BigInt values
 * This can be used as a global transformer in TypeORM queries
 */
export function createBigIntSafeQueryBuilder<T>(
  queryBuilder: any,
  options?: {
    transformResults?: boolean;
    handleCounts?: boolean;
  }
) {
  const originalGetMany = queryBuilder.getMany;
  const originalGetOne = queryBuilder.getOne;
  const originalGetCount = queryBuilder.getCount;
  const originalGetRawAndEntities = queryBuilder.getRawAndEntities;

  if (options?.transformResults !== false) {
    // Override getMany to transform results
    queryBuilder.getMany = async function() {
      const results = await originalGetMany.call(this);
      return handleTypeORMResult(results);
    };

    // Override getOne to transform results
    queryBuilder.getOne = async function() {
      const result = await originalGetOne.call(this);
      return result ? handleTypeORMResult(result) : null;
    };

    // Override getRawAndEntities to transform results
    queryBuilder.getRawAndEntities = async function() {
      const [rawResults, entities] = await originalGetRawAndEntities.call(this);
      return [
        handleTypeORMResult(rawResults),
        handleTypeORMResult(entities)
      ];
    };
  }

  if (options?.handleCounts !== false) {
    // Override getCount to handle BigInt results
    queryBuilder.getCount = async function() {
      const count = await originalGetCount.call(this);
      if (typeof count === 'bigint') {
        return Number(count);
      }
      return count;
    };
  }

  return queryBuilder;
}

/**
 * Decorator for TypeORM repositories to automatically handle BigInt serialization
 * This can be applied to repository classes or methods
 */
export function withBigIntHandling<T extends any[], R>(
  target: any,
  propertyKey: string,
  descriptor: TypedPropertyDescriptor<(...args: T) => Promise<R>>
) {
  const originalMethod = descriptor.value;
  
  if (originalMethod) {
    descriptor.value = async function(...args: T): Promise<R> {
      try {
        const result = await originalMethod.apply(this, args);
        return handleTypeORMResult(result);
      } catch (error) {
        if (error instanceof Error && error.message.includes('BigInt')) {
          console.warn(`BigInt serialization error in ${propertyKey}, attempting recovery...`);
          // Try to recover by transforming the error context
          if (error.stack) {
            console.warn('Error stack:', error.stack);
          }
        }
        throw error;
      }
    };
  }
  
  return descriptor;
}

/**
 * Global error handler for BigInt serialization issues
 * This can be registered as a global error handler in your application
 */
export function setupBigIntErrorHandling() {
  // Setup the global BigInt handler
  setupGlobalBigIntHandler();
  
  // Also override console.log to handle BigInt values in logging
  const originalLog = console.log;
  const originalWarn = console.warn;
  const originalError = console.error;
  
  console.log = function(...args: any[]) {
    const transformedArgs = args.map(arg => handleTypeORMResult(arg));
    originalLog.apply(console, transformedArgs);
  };
  
  console.warn = function(...args: any[]) {
    const transformedArgs = args.map(arg => handleTypeORMResult(arg));
    originalWarn.apply(console, transformedArgs);
  };
  
  console.error = function(...args: any[]) {
    const transformedArgs = args.map(arg => handleTypeORMResult(arg));
    originalError.apply(console, transformedArgs);
  };

  console.log('BigInt error handling setup complete');
}

/**
 * Utility to check if an object contains BigInt values
 */
export function containsBigInt(obj: any): boolean {
  if (obj === null || obj === undefined) {
    return false;
  }

  if (typeof obj === 'bigint') {
    return true;
  }

  if (Array.isArray(obj)) {
    return obj.some(containsBigInt);
  }

  if (typeof obj === 'object') {
    return Object.values(obj).some(containsBigInt);
  }

  return false;
}

/**
 * Utility to safely clone objects while handling BigInt values
 */
export function safeClone<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'bigint') {
    return obj.toString() as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(safeClone) as unknown as T;
  }

  if (typeof obj === 'object') {
    const cloned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      cloned[key] = safeClone(value);
    }
    return cloned;
  }

  return obj;
}
