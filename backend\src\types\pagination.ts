import { z } from 'zod';

// Pagination input schema
export const paginationInputSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
});

// Pagination metadata schema as per API_LAYER.md requirements
export const paginationMetaSchema = z.object({
  total: z.number().describe('Total number of items across all pages'),
  page: z.number().describe('Current page number'),
  limit: z.number().describe('Number of items per page'),
  totalPages: z.number().describe('Total number of pages available'),
  hasNext: z.boolean().describe('Boolean indicating if there are more pages'),
  hasPrev: z.boolean().describe('Boolean indicating if there are previous pages'),
});

// Generic paginated response schema
export const createPaginatedResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: z.array(dataSchema),
    meta: paginationMetaSchema,
  });

// Types
export type PaginationInput = z.infer<typeof paginationInputSchema>;
export type PaginationMeta = z.infer<typeof paginationMetaSchema>;
export type PaginatedResponse<T> = {
  data: T[];
  meta: PaginationMeta;
};

// Utility function to create pagination metadata
export function createPaginationMeta(
  total: number,
  page: number,
  limit: number
): PaginationMeta {
  const totalPages = Math.ceil(total / limit);
  
  return {
    total,
    page,
    limit,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
}

