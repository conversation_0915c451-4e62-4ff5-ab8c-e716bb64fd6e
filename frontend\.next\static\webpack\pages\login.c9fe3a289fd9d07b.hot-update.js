"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/login",{

/***/ "./src/pages/login.tsx":
/*!*****************************!*\
  !*** ./src/pages/login.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Login; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/api */ \"./src/utils/api.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Login() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { loginWithGoogle, user, isLoading: isAuthLoading, refresh } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [devEmail, setDevEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"<EMAIL>\");\n    const [devName, setDevName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Dev User\");\n    const [storesData, setStoresData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isStoresLoading, setIsStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userIdBigInt = typeof (user === null || user === void 0 ? void 0 : user.id) !== \"undefined\" ? BigInt(user.id) : undefined;\n    // Fetch stores data\n    const fetchStores = async ()=>{\n        if (!user || !userIdBigInt) {\n            console.log(\"[Login] fetchStores: No user or userIdBigInt, skipping\");\n            return;\n        }\n        console.log(\"[Login] fetchStores: Starting fetch for user:\", user.id);\n        setIsStoresLoading(true);\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_6__.storeApi.getByUserId(userIdBigInt.toString(), {\n                page: 1,\n                limit: 1\n            });\n            console.log(\"[Login] fetchStores: API response:\", result);\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setStoresData(result);\n                console.log(\"[Login] fetchStores: Set stores data with meta:\", result.meta);\n            } else {\n                const fallbackData = {\n                    data: Array.isArray(result) ? result : [],\n                    meta: {\n                        total: 0\n                    }\n                };\n                setStoresData(fallbackData);\n                console.log(\"[Login] fetchStores: Set fallback data:\", fallbackData);\n            }\n        } catch (err) {\n            console.error(\"[Login] fetchStores: Error fetching stores:\", err);\n            const errorData = {\n                data: [],\n                meta: {\n                    total: 0\n                }\n            };\n            setStoresData(errorData);\n            console.log(\"[Login] fetchStores: Set error data:\", errorData);\n        } finally{\n            setIsStoresLoading(false);\n            console.log(\"[Login] fetchStores: Set loading to false\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check for error in URL params\n        if (router.query.error) {\n            switch(router.query.error){\n                case \"auth_error\":\n                    setError(\"Authentication failed. Please try again.\");\n                    break;\n                case \"auth_failed\":\n                    setError(\"Login failed. Please check your credentials.\");\n                    break;\n                case \"token_error\":\n                    setError(\"Token generation failed. Please try again.\");\n                    break;\n                default:\n                    setError(\"An error occurred during login.\");\n            }\n        }\n    }, [\n        router.query.error\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // If explicitly landed here after logout, do not auto-redirect anywhere\n        const loggedOut = typeof router.query.loggedOut === \"string\" ? router.query.loggedOut : undefined;\n        if (loggedOut === \"1\") return;\n        if (!isAuthLoading && user) {\n            var _storesData_meta, _storesData_data;\n            // Wait for store lookup to finish before deciding where to go\n            if (isStoresLoading) {\n                console.log(\"[Login] Stores still loading, waiting...\");\n                return;\n            }\n            // Only proceed with redirect logic if we have actually fetched stores data\n            if (storesData === null) {\n                console.log(\"[Login] Stores data not yet fetched, waiting...\");\n                return;\n            }\n            var _storesData_meta_total, _ref;\n            const totalStores = (_ref = (_storesData_meta_total = storesData === null || storesData === void 0 ? void 0 : (_storesData_meta = storesData.meta) === null || _storesData_meta === void 0 ? void 0 : _storesData_meta.total) !== null && _storesData_meta_total !== void 0 ? _storesData_meta_total : storesData === null || storesData === void 0 ? void 0 : (_storesData_data = storesData.data) === null || _storesData_data === void 0 ? void 0 : _storesData_data.length) !== null && _ref !== void 0 ? _ref : 0;\n            console.log(\"[Login] Redirect decision:\", {\n                totalStores,\n                storesData,\n                isStoresLoading,\n                user: user.id\n            });\n            // Only redirect to setup if user has no active stores\n            if (totalStores === 0) {\n                console.log(\"[Login] No stores found, redirecting to setup\");\n                router.replace(\"/setup/store\");\n                return;\n            }\n            // Otherwise honor next or go dashboard\n            const nextParam = typeof router.query.next === \"string\" ? router.query.next : undefined;\n            const target = nextParam && nextParam.startsWith(\"/\") ? nextParam : \"/dashboard\";\n            console.log(\"[Login] Stores found, redirecting to:\", target);\n            router.replace(target, target);\n        }\n    }, [\n        user,\n        isAuthLoading,\n        isStoresLoading,\n        storesData,\n        router.query.loggedOut,\n        router\n    ]);\n    // Fetch stores when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchStores();\n        }\n    }, [\n        user\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Login - Teno Store Console\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Authenticate to the Teno Store console\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"app-bg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glow-overlay\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid-overlay\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-md w-full space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"icon-badge\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"h-7 w-7 text-emerald-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"currentColor\",\n                                                    d: \"M12 2a10 10 0 1 0 10 10A10.011 10.011 0 0 0 12 2Zm0 18a8 8 0 1 1 8-8a8.009 8.009 0 0 1-8 8Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"currentColor\",\n                                                    d: \"M7 12a5 5 0 0 1 10 0h2a7 7 0 0 0-14 0Zm5-3a3 3 0 0 1 3 3h2a5 5 0 0 0-10 0h2a3 3 0 0 1 3-3Z\",\n                                                    className: \"opacity-80\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"mt-6 text-3xl font-extrabold tracking-tight brand-gradient-text\",\n                                        children: \"Sign in to Teno Store\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 subtle-text\",\n                                        children: \"Authenticate to access your Teno workspace\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-4 rounded-md border border-red-500/20 bg-red-500/10 px-4 py-3 text-red-200\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setIsLoading(true);\n                                                    setError(null);\n                                                    loginWithGoogle();\n                                                },\n                                                disabled: isLoading,\n                                                className: \"group primary-button\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#4285F4\",\n                                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#34A853\",\n                                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#FBBC05\",\n                                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#EA4335\",\n                                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isLoading ? \"Connecting...\" : \"Continue with Google\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"button-glow group-hover:opacity-100\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, this),\n                                            (process.env.NEXT_PUBLIC_DEBUG_AUTH === \"1\" || \"development\" !== \"production\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                value: devEmail,\n                                                                onChange: (e)=>setDevEmail(e.target.value),\n                                                                placeholder: \"<EMAIL>\",\n                                                                className: \"w-full rounded-md bg-slate-800/50 border border-slate-700 px-3 py-2 text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/60\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: devName,\n                                                                onChange: (e)=>setDevName(e.target.value),\n                                                                placeholder: \"Optional name\",\n                                                                className: \"w-full rounded-md bg-slate-800/50 border border-slate-700 px-3 py-2 text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/60\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            try {\n                                                                setIsLoading(true);\n                                                                setError(null);\n                                                                const user = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_5__.debugLogin)(devEmail, devName);\n                                                                await (refresh === null || refresh === void 0 ? void 0 : refresh());\n                                                                const nextParam = typeof router.query.next === \"string\" ? router.query.next : undefined;\n                                                                const target = nextParam && nextParam.startsWith(\"/\") ? nextParam : \"/dashboard\";\n                                                                router.replace(target, target);\n                                                            } catch (e) {\n                                                                setError(\"Debug login failed\");\n                                                            } finally{\n                                                                setIsLoading(false);\n                                                            }\n                                                        },\n                                                        disabled: isLoading,\n                                                        className: \"group secondary-button w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: isLoading ? \"Connecting...\" : \"Debug login (no password)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"subtle-text hover:text-slate-100 cursor-pointer\",\n                                                    onClick: ()=>window.location.href = \"/\",\n                                                    children: \"Back to Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\login.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Login, \"ESsAGciYoB0yv9JX8eyO1WaPH5w=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/login.tsx\n"));

/***/ })

});