import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
// import { trpc } from '../../utils/trpc'; // tRPC removed, using REST API instead
import { useAuth } from '../../context/AuthContext';
import TopTaskBar from '../../components/TopTaskBar';
import SideTaskBar from '../../components/SideTaskBar';

export default function ConversationsListPage() {
  // 1. Hooks and state
  const router = useRouter();
  const { user } = useAuth();

  const [page, setPage] = useState(1);
  const [createdUuid, setCreatedUuid] = useState<string | null>(null);
  const [detailsConversationId, setDetailsConversationId] = useState<string | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isShareOpen, setIsShareOpen] = useState(false);
  const limit = 10;

  const userIdAsBigInt = useMemo(() => {
    if (!user?.id) return null;
    try {
      return BigInt(user.id);
    } catch {
      return null;
    }
  }, [user?.id]);

  const conversationsQuery = trpc.conversation.getByUserId.useQuery(
    { userId: userIdAsBigInt!, page, limit },
    { enabled: !!userIdAsBigInt }
  );

  // Fetch one store to associate when creating a new conversation
  const storesQuery = trpc.store.getByUserId.useQuery(
    { userId: userIdAsBigInt!, page: 1, limit: 1 },
    { enabled: !!userIdAsBigInt }
  );

  // Create a new conversation and then show share panel
  const createConversationMutation = trpc.conversation.create.useMutation({
    onSuccess: (conversation) => {
      setCreatedUuid(conversation.uuid);
      setIsShareOpen(true);
      conversationsQuery.refetch();
    },
    onError: (error) => {
      console.error('Failed to create conversation:', error);
    },
  });

  // Compute selected conversation id as bigint for details query
  const detailsConversationIdBigInt = useMemo(() => {
    if (!detailsConversationId) return null;
    try { return BigInt(detailsConversationId); } catch { return null; }
  }, [detailsConversationId]);

  const conversationDetailsQuery = trpc.conversation.getByIdWithMessages.useQuery(
    { id: detailsConversationIdBigInt!, page: 1, limit: 20 },
    { enabled: !!detailsConversationIdBigInt }
  );

  // 2. Effects and handlers
  useEffect(() => {
    setPage(1);
  }, [userIdAsBigInt]);

  const handleCreateConversation = async () => {
    if (!userIdAsBigInt) return;
    const defaultStore = storesQuery.data?.data?.[0];
    if (!defaultStore) {
      console.error('No store found for user');
      return;
    }
    createConversationMutation.mutate({
      title: 'New Live Conversation',
      userId: userIdAsBigInt,
      storeId: defaultStore.id,
      createdBy: userIdAsBigInt,
    });
  };

  const closeAllModals = () => {
    setIsDetailsOpen(false);
    setIsShareOpen(false);
  };

  // 3. Early returns for loading/error states
  if (!user) {
    return (
      <>
        <Head>
          <title>Authentication Required - Teno Store</title>
          <meta name="description" content="Please log in to view your conversations" />
        </Head>
        
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
          <div className="text-center max-w-md w-full">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="mb-4">
                <svg className="h-12 w-12 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">Authentication Required</h2>
              <p className="text-gray-600 mb-6">You need to be logged in to view conversations.</p>
              <button
                className="w-full bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
                onClick={() => router.push('/login')}
              >
                Go to Login
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  const isLoading = conversationsQuery.isLoading;
  const error = conversationsQuery.error;
  const data = conversationsQuery.data?.data ?? [];
  const meta = conversationsQuery.data?.meta;

  // 4. Main render
  return (
    <>
      <Head>
        <title>Conversations - Teno Store</title>
        <meta name="description" content="Browse and manage your agent conversations" />
      </Head>
      
      <div className="min-h-screen bg-slate-900">
        <TopTaskBar />
        <SideTaskBar />
        
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-extrabold tracking-tight text-slate-100">Agent Conversations</h1>
                <p className="mt-2 text-slate-300">Review and resume past or active conversations.</p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handleCreateConversation}
                  disabled={createConversationMutation.isLoading || !storesQuery.data?.data?.length}
                  className="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                {createConversationMutation.isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    New Conversation
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Share Modal */}
          {isShareOpen && createdUuid && (
            <div className="fixed inset-0 z-50 flex items-center justify-center p-4" role="dialog" aria-labelledby="share-modal-title" aria-modal="true">
              <div className="absolute inset-0 bg-black/50 transition-opacity" onClick={closeAllModals} aria-hidden="true" />
              <div className="relative bg-gradient-to-br from-slate-800/95 to-slate-900/95 border border-white/10 rounded-2xl shadow-2xl w-full max-w-lg overflow-hidden">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-indigo-500/20 rounded-2xl blur opacity-60"></div>
                <div className="relative px-6 py-4 border-b border-white/10">
                  <div className="flex items-center justify-between">
                    <h3 id="share-modal-title" className="text-lg font-semibold text-slate-100">Conversation Created</h3>
                    <button 
                      className="text-slate-400 hover:text-slate-200 rounded-md p-1"
                      onClick={closeAllModals}
                      aria-label="Close modal"
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="relative p-6">
                  <div className="bg-emerald-500/10 border border-emerald-500/20 rounded-md p-4 mb-4">
                    <div className="flex">
                      <svg className="h-5 w-5 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-emerald-300">Success!</h4>
                        <p className="text-sm text-emerald-200/80">Your conversation has been created successfully.</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-200 mb-2">Share this link with your customer:</label>
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          readOnly
                          value={typeof window !== 'undefined' ? `${window.location.origin}/live/${createdUuid}` : `/live/${createdUuid}`}
                          className="flex-1 px-3 py-2 border border-slate-600/50 rounded-md bg-slate-800/60 text-sm font-mono text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
                          onClick={(e) => e.currentTarget.select()}
                        />
                        <button
                          className="bg-emerald-600 text-white px-3 py-2 rounded-md hover:bg-emerald-700 transition-colors text-sm"
                          onClick={async () => {
                            const link = `${window.location.origin}/live/${createdUuid}`;
                            try { 
                              await navigator.clipboard.writeText(link);
                              // Could add a toast notification here
                            } catch (error) {
                              console.error('Failed to copy link:', error);
                            }
                          }}
                        >
                          Copy
                        </button>
                      </div>
                    </div>
                    
                    <div className="flex gap-3">
                      <button
                        className="flex-1 bg-cyan-600 text-white px-4 py-2 rounded-md hover:bg-cyan-700 transition-colors"
                        onClick={() => window.open(`/live/${createdUuid}`, '_blank')}
                      >
                        Open Live Page
                      </button>
                      <button
                          className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-md transition-colors"
                          onClick={closeAllModals}
                        >
                          Done
                        </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="bg-slate-800 rounded-lg shadow-lg p-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-400 mx-auto mb-4"></div>
                <p className="text-slate-300">Loading conversations...</p>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 text-red-300 px-4 py-3 rounded-md">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400 mr-3 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium">Error Loading Conversations</h3>
                  <p className="text-sm mt-1">Failed to load conversations. Please try again later.</p>
                </div>
              </div>
            </div>
          )}

          {/* Conversations List */}
          {!isLoading && !error && (
            <div className="bg-slate-800 rounded-lg shadow-lg overflow-hidden">
              {data.length === 0 && (
                <div className="p-12 text-center">
                  <svg className="h-12 w-12 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <h3 className="text-lg font-medium text-slate-100 mb-2">No conversations yet</h3>
                  <p className="text-slate-300 mb-6">Start a conversation with an agent to see it here.</p>
                  <button 
                    className="bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors"
                    onClick={() => router.push('/dashboard')}
                  >
                    Go to Dashboard
                  </button>
                </div>
              )}

              <div className="divide-y divide-white/10">
                {data.map((conv) => {
                  const idString = conv.id.toString();
                  const lastUpdated = new Date(conv.updatedAt as unknown as string);
                  const messageCount = conv._count?.messages ?? 0;
                  return (
                    <article key={idString} className="block hover:bg-white/5 transition-colors focus-within:bg-white/5">
                      <div className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            {/* Status Badges */}
                            <div className="flex items-center gap-3 mb-3">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-500/10 text-cyan-300 ring-1 ring-cyan-500/20">Conversation</span>
                            {messageCount > 0 && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-500/10 text-emerald-300 ring-1 ring-emerald-500/20">
                                {messageCount} {messageCount === 1 ? 'message' : 'messages'}
                              </span>
                            )}
                            </div>
                            
                            {/* Conversation Info */}
                            <h3 className="text-lg font-semibold text-slate-100 mb-2 truncate">
                              {conv.title ?? 'Untitled conversation'}
                            </h3>
                            <p className="text-sm text-slate-300 mb-4">
                              {conv.store?.name ? (
                                <>
                                  <span className="font-medium">Store:</span> {conv.store.name}
                                </>
                              ) : (
                                <span className="text-slate-400">No store assigned</span>
                              )}
                            </p>
                            
                            {/* Actions */}
                            <div className="flex gap-2">
                              <button
                                onClick={() => { 
                                  setDetailsConversationId(idString); 
                                  setIsDetailsOpen(true); 
                                }}
                                className="inline-flex items-center px-3 py-1.5 border border-slate-600 text-sm font-medium rounded-md text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 transition-colors"
                              >
                                View Details
                              </button>
                            </div>
                          </div>
                          
                          {/* Timestamp */}
                          <div className="text-sm text-slate-400 text-right ml-6 flex-shrink-0">
                            <time className="block" dateTime={conv.updatedAt as unknown as string}>
                              {isNaN(lastUpdated.getTime()) ? 'Unknown date' : lastUpdated.toLocaleDateString()}
                            </time>
                            <div className="text-xs text-slate-500 mt-1">
                              {isNaN(lastUpdated.getTime()) ? '' : lastUpdated.toLocaleTimeString([], { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                              })}
                            </div>
                          </div>
                        </div>
                      </div>
                    </article>
                  );
                })}
              </div>
            </div>
          )}

          {/* Pagination */}
          {!isLoading && !error && meta && data.length > 0 && (
            <nav className="mt-8 bg-slate-800 rounded-lg shadow-lg px-6 py-4" aria-label="Pagination">
              <div className="flex items-center justify-between">
                <button
                  className="border border-slate-600 text-slate-300 bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!meta.hasPrev}
                  onClick={() => setPage((p) => Math.max(1, p - 1))}
                  aria-label="Go to previous page"
                >
                  <span className="flex items-center">
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    Previous
                  </span>
                </button>
                
                <div className="text-sm text-slate-300 text-center">
                  <div className="font-medium">Page {meta.page} of {meta.totalPages}</div>
                  <div className="text-xs text-slate-400 mt-1">{meta.total} total conversations</div>
                </div>
                
                <button
                  className="border border-slate-600 text-slate-300 bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!meta.hasNext}
                  onClick={() => setPage((p) => p + 1)}
                  aria-label="Go to next page"
                >
                  <span className="flex items-center">
                    Next
                    <svg className="h-4 w-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </span>
                </button>
              </div>
            </nav>
          )}
        </div>
      </div>

      {/* Details Modal */}
      {isDetailsOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4" role="dialog" aria-labelledby="details-modal-title" aria-modal="true">
          <div className="absolute inset-0 bg-black/50 transition-opacity" onClick={closeAllModals} aria-hidden="true" />
          <div className="relative w-full max-w-4xl overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95">
            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-indigo-500/20 blur opacity-60" />
            <div className="relative flex flex-col max-h-[80vh]">
              {/* Modal Header */}
              <div className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
                <h3 id="details-modal-title" className="text-lg font-semibold text-slate-100">Conversation Details</h3>
                <button 
                  className="text-slate-400 hover:text-slate-200 rounded-md p-1"
                  onClick={closeAllModals}
                  aria-label="Close modal"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              {/* Modal Content */}
              <div className="p-6 overflow-y-auto">
                {conversationDetailsQuery.isLoading && (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-400 mx-auto mb-3"></div>
                      <p className="text-slate-300">Loading conversation details...</p>
                    </div>
                  </div>
                )}
                
                {conversationDetailsQuery.error && (
                  <div className="bg-red-500/10 border border-red-500/20 text-red-300 px-4 py-3 rounded-md">
                    <div className="flex">
                      <svg className="h-5 w-5 text-red-400 mr-3 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                      <div>
                        <h4 className="text-sm font-medium">Error Loading Conversation</h4>
                        <p className="text-sm mt-1">Failed to load conversation details. Please try again.</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {conversationDetailsQuery.data && (
                  <div className="space-y-6">
                    {/* Conversation Header */}
                    <div className="bg-slate-800/60 border border-slate-600/50 rounded-lg p-4">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                        <div className="flex-1">
                          <h4 className="text-xl font-semibold text-slate-100 mb-2">
                            {conversationDetailsQuery.data.title ?? 'Untitled conversation'}
                          </h4>
                          <div className="space-y-1 text-sm text-slate-300">
                            {conversationDetailsQuery.data.uuid && (
                              <div><span className="font-medium">UUID:</span> {conversationDetailsQuery.data.uuid}</div>
                            )}
                            <div>
                              <span className="font-medium">Store:</span> {conversationDetailsQuery.data.store?.name ?? (
                                <span className="text-slate-400">No store assigned</span>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        {/* Live Link Section */}
                        {conversationDetailsQuery.data?.uuid && (
                          <div className="bg-slate-900/60 rounded-md border border-slate-700 p-3 flex-1 w-full sm:max-w-md md:max-w-xl">
                            <label className="block text-xs font-medium text-slate-300 mb-2">Live Conversation Link:</label>
                            <div className="space-y-2">
                              <input
                                type="text"
                                readOnly
                                value={typeof window !== 'undefined' ? `${window.location.origin}/live/${conversationDetailsQuery.data.uuid}` : `/live/${conversationDetailsQuery.data.uuid}`}
                                className="w-full px-2 py-1 border border-slate-600/50 rounded text-xs font-mono bg-slate-800/60 text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
                                onClick={(e) => e.currentTarget.select()}
                              />
                              <div className="flex gap-2">
                                <button
                                  className="flex-1 text-xs px-2 py-1 border border-slate-600 text-slate-300 rounded bg-slate-800/50 hover:bg-slate-700/50 transition-colors"
                                  onClick={async () => {
                                    const link = `${window.location.origin}/live/${conversationDetailsQuery.data!.uuid}`;
                                    try { 
                                      await navigator.clipboard.writeText(link); 
                                    } catch (error) {
                                      console.error('Failed to copy link:', error);
                                    }
                                  }}
                                >
                                  Copy
                                </button>
                                <button
                                  onClick={() => conversationDetailsQuery.data && window.open(`/live/${conversationDetailsQuery.data.uuid}`, '_blank')}
                                  className="flex-1 text-xs px-2 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors"
                                >
                                  Open
                                </button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Messages Section */}
                    <div className="bg-slate-800/60 border border-slate-600/50 rounded-lg overflow-hidden">
                      <div className="px-4 py-3 border-b border-white/10 bg-slate-800/80">
                        <h5 className="font-medium text-sm text-slate-100">Messages</h5>
                      </div>
                      <div className="max-h-64 overflow-y-auto">
                        {(conversationDetailsQuery.data.messages ?? []).length === 0 ? (
                          <div className="p-6 text-center">
                            <svg className="h-8 w-8 text-slate-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                            <p className="text-sm text-slate-400">No messages yet.</p>
                          </div>
                        ) : (
                          <div className="divide-y divide-white/10">
                            {(conversationDetailsQuery.data.messages ?? []).map((m) => {
                              const isAgent = !m.user && !m.customer;
                              const isCustomer = !!m.customer;
                              const sender = m.customer?.email || (isAgent ? 'Agent' : m.user?.email) || 'Unknown';
                              const at = new Date(m.createdAt as unknown as string);
                              
                              return (
                                <div key={m.id.toString()} className="p-4">
                                  <div className="flex items-start justify-between mb-2">
                                    <div className="flex items-center gap-2">
                                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                        isAgent 
                                          ? 'bg-cyan-500/10 text-cyan-300 ring-1 ring-cyan-500/20' 
                                          : isCustomer 
                                          ? 'bg-emerald-500/10 text-emerald-300 ring-1 ring-emerald-500/20' 
                                          : 'bg-slate-700 text-slate-300'
                                      }`}>
                                        {isAgent ? 'Agent' : isCustomer ? 'Customer' : 'User'}
                                      </span>
                                      <span className="text-sm font-medium text-slate-100 truncate">{sender}</span>
                                    </div>
                                    <time className="text-xs text-slate-400 flex-shrink-0" dateTime={m.createdAt as unknown as string}>
                                      {isNaN(at.getTime()) ? 'Unknown time' : at.toLocaleString()}
                                    </time>
                                  </div>
                                  <div className="text-sm text-slate-200 whitespace-pre-wrap leading-relaxed pl-2 border-l-2 border-white/10">
                                    {m.content}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Modal Footer */}
              <div className="px-6 py-3 border-t border-white/10 flex items-center justify-end gap-3">
                <button 
                  className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-md transition-colors"
                  onClick={closeAllModals}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}


