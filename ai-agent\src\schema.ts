import { JsonSchema, ToolFunction } from './types';

/**
 * Convert TypeScript types to JSON schema for OpenAI function calling
 */
export function typeToJsonSchema(type: any): JsonSchema {
  // Handle primitive types
  if (type === String || type === 'string') {
    return { type: 'string' };
  }
  if (type === Number || type === 'number') {
    return { type: 'number' };
  }
  if (type === Boolean || type === 'boolean') {
    return { type: 'boolean' };
  }
  if (Array.isArray(type)) {
    return {
      type: 'array',
      items: typeToJsonSchema(type[0] || String)
    };
  }
  if (type === Object || typeof type === 'object') {
    return { type: 'object' };
  }
  
  // Default to string for unknown types
  return { type: 'string' };
}

/**
 * Build parameter schema from function metadata
 */
export function buildParametersSchema(
  func: ToolFunction,
  parameterTypes?: Record<string, any>,
  requiredParams?: string[]
): JsonSchema {
  const properties: Record<string, JsonSchema> = {};
  const required: string[] = requiredParams || [];
  
  // If parameter types are provided, use them
  if (parameterTypes) {
    for (const [name, type] of Object.entries(parameterTypes)) {
      // Check if the type is already a JSON schema object
      if (typeof type === 'object' && type !== null && 'type' in type) {
        properties[name] = type as JsonSchema;
      } else {
        // Convert TypeScript types to JSON schema
        properties[name] = typeToJsonSchema(type);
      }
    }
  }
  
  return {
    type: 'object',
    properties,
    required
  };
}

/**
 * Decorator-like function to add tool metadata to functions
 */
export function addToolMetadata(
  func: ToolFunction,
  description?: string,
  parameterTypes?: Record<string, any>,
  requiredParams?: string[],
  explicitName?: string
): ToolFunction {
  const toolDescription = description || func.name || 'Tool function';
  // Ensure a non-empty, valid function name for OpenAI tools
  const deriveToolName = (): string => {
    if (explicitName && explicitName.trim().length > 0) return explicitName.trim();
    const base = (func as any).name as string | undefined;
    if (base && base.trim().length > 0) return base.trim();
    // Derive from description
    const fromDesc = (toolDescription || '').toLowerCase().replace(/[^a-z0-9]+/g, '_').replace(/^_+|_+$/g, '');
    if (fromDesc && fromDesc.length > 0) return fromDesc;
    // Fallback unique name
    return `tool_${Math.random().toString(36).slice(2, 10)}`;
  };
  const toolName = deriveToolName();
  const parametersSchema = buildParametersSchema(func, parameterTypes, requiredParams);
  
  (func as any)._toolSpec = {
    type: 'function',
    function: {
      name: toolName,
      description: toolDescription,
      parameters: parametersSchema
    }
  };
  
  return func;
}
