import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { conversationApi, agentApi } from '../../utils/api';
import { useAuth } from '../../context/AuthContext';
import { useStore } from '../../context/StoreContext';
import TopTaskBar from '../../components/TopTaskBar';
import SideTaskBar from '../../components/SideTaskBar';
import TimelineItem from '../../components/TimelineItem';

interface ConversationWithDetails {
  id: bigint;
  uuid: string;
  title: string | null;
  context?: any;
  notificationStatus: string | null;
  totalCost: number | null;
  totalExecutionTime: number | null;
  totalInputTokens: number | null;
  totalOutputTokens: number | null;
  createdAt: Date;
  updatedAt: Date;
  isDeleted: boolean;
  userId: bigint;
  storeId: bigint;
  customerId: bigint | null;
  agentId?: string | null;
  user: {
    id: bigint;
    email: string;
    name: string | null;
  };
  store: {
    id: bigint;
    name: string;
    description: string | null;
  };
  customer: {
    id: bigint;
    email: string | null;
    name: string | null;
    phone: string | null;
    address: string | null;
  } | null;
}

interface Agent {
  id: string;
  name: string;
}

export default function ConversationDebugPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { currentStoreId } = useStore();

  // State for conversation list
  const [page, setPage] = useState(1);
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const limit = 20;
  
  // State for timeline pagination
  const [timelinePage, setTimelinePage] = useState(1);
  const timelineLimit = 50;

  // State for conversation info modal
  const [showConversationModal, setShowConversationModal] = useState(false);
  const [newConversation, setNewConversation] = useState<ConversationWithDetails | null>(null);
  const [selectedAgentId, setSelectedAgentId] = useState<string>('');
  
  // State for copy feedback
  const [copyFeedback, setCopyFeedback] = useState<{show: boolean, message: string}>({show: false, message: ''});

  // State for API data and loading
  const [conversations, setConversations] = useState<any[]>([]);
  const [conversationDetails, setConversationDetails] = useState<any>(null);
  const [unifiedTimeline, setUnifiedTimeline] = useState<any>(null);
  const [conversationsMeta, setConversationsMeta] = useState<any>(null);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [isLoadingTimeline, setIsLoadingTimeline] = useState(false);
  const [isLoadingAgents, setIsLoadingAgents] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Convert user ID to BigInt
  const userIdAsBigInt = useMemo(() => {
    if (!user?.id) return null;
    try {
      return BigInt(user.id);
    } catch {
      return null;
    }
  }, [user?.id]);

  // Fetch agents
  const fetchAgents = async () => {
    setIsLoadingAgents(true);
    try {
      const result = await agentApi.getAll({ page: 1, limit: 100 });
      if (result && typeof result === 'object' && 'data' in result) {
        setAgents((result.data as Agent[]) || []);
      } else {
        setAgents(Array.isArray(result) ? (result as Agent[]) : []);
      }
    } catch (err) {
      console.error('Error fetching agents:', err);
    } finally {
      setIsLoadingAgents(false);
    }
  };

  // Fetch conversations list
  const fetchConversations = async () => {
    if (!userIdAsBigInt) return;
    
    // If no store is selected, don't fetch conversations
    if (!currentStoreId) {
      setConversations([]);
      setConversationsMeta(null);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    try {
      // Use the store-specific endpoint to filter conversations by current store
      const result = await conversationApi.getByStoreId(currentStoreId, { page, limit });
      if (result && typeof result === 'object' && 'data' in result) {
        setConversations(Array.isArray(result.data) ? result.data : []);
        setConversationsMeta((result as any).meta || null);
      } else {
        setConversations(Array.isArray(result) ? result : []);
        setConversationsMeta(null);
      }
    } catch (err) {
      setError('Failed to fetch conversations');
      console.error('Error fetching conversations:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch detailed conversation data when selected
  const selectedConversationIdBigInt = useMemo(() => {
    if (!selectedConversationId) return null;
    try {
      const result = BigInt(selectedConversationId);
      console.log('[FRONTEND DEBUG] Converting selectedConversationId to BigInt:', {
        stringId: selectedConversationId,
        bigIntId: result,
        type: typeof result
      });
      // Reset timeline page when conversation changes
      setTimelinePage(1);
      return result;
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error converting to BigInt:', error);
      return null;
    }
  }, [selectedConversationId]);

  const fetchConversationDetails = async () => {
    if (!selectedConversationIdBigInt) return;
    
    setIsLoadingDetails(true);
    setError(null);
    try {
      const result = await conversationApi.getById(selectedConversationIdBigInt.toString());
      if (result && typeof result === 'object' && 'data' in result) {
        setConversationDetails(result.data || result);
      } else {
        setConversationDetails(result);
      }
    } catch (err) {
      setError('Failed to fetch conversation details');
      console.error('Error fetching conversation details:', err);
    } finally {
      setIsLoadingDetails(false);
    }
  };

  const fetchUnifiedTimeline = async () => {
    if (!selectedConversationIdBigInt) return;
    
    setIsLoadingTimeline(true);
    setError(null);
    try {
      const result = await conversationApi.getUnifiedTimeline(selectedConversationIdBigInt.toString(), {
        page: timelinePage,
        limit: timelineLimit
      });
      if (result && typeof result === 'object' && 'data' in result) {
        setUnifiedTimeline(result.data || result);
      } else {
        setUnifiedTimeline(result);
      }
    } catch (err) {
      setError('Failed to fetch timeline');
      console.error('Error fetching timeline:', err);
    } finally {
      setIsLoadingTimeline(false);
    }
  };

  // Fetch data when dependencies change
  useEffect(() => {
    fetchConversations();
    fetchAgents();
  }, [page, limit, userIdAsBigInt, currentStoreId]); // Add currentStoreId as dependency

  useEffect(() => {
    if (selectedConversationIdBigInt) {
      fetchConversationDetails();
    }
  }, [selectedConversationIdBigInt]);

  useEffect(() => {
    if (selectedConversationIdBigInt) {
      fetchUnifiedTimeline();
    }
  }, [selectedConversationIdBigInt, timelinePage, timelineLimit]);

  // Create conversation mutation
  const handleCreateConversation = async (conversationData: any) => {
    if (!user?.id || !userIdAsBigInt || !currentStoreId) return;
    
    try {
      const result = await conversationApi.create({
        title: 'New Conversation',
        userId: userIdAsBigInt.toString(),
        storeId: currentStoreId, // Use current store ID instead of hardcoded '1'
        createdBy: userIdAsBigInt.toString(),
      });
      
      console.log('[FRONTEND DEBUG] Conversation created:', result);
      // Refresh the conversations list
      fetchConversations();
      // Set the new conversation for the modal and show it
      if (result && typeof result === 'object' && 'data' in result) {
        setNewConversation((result.data as any) || result);
      } else {
        setNewConversation(result as any);
      }
    } catch (error) {
      console.error('[FRONTEND DEBUG] Failed to create conversation:', error);
      // Hide modal on error
      setShowConversationModal(false);
      setNewConversation(null);
    }
  };

  // Function to handle starting conversation creation
  const handleStartConversation = () => {
    if (!user?.id || !userIdAsBigInt || !currentStoreId) return;
    
    // Show modal immediately with loading state
    setShowConversationModal(true);
    setNewConversation(null);
    
    // Start the conversation creation
    handleCreateConversation({
      title: 'New Conversation',
      userId: userIdAsBigInt.toString(),
      storeId: currentStoreId, // Use current store ID instead of hardcoded '1'
      createdBy: userIdAsBigInt.toString(),
    });
  };

  // Function to copy live link to clipboard
  const copyLiveLink = async (conversationUuid: string) => {
    try {
      const url = `${window.location.origin}/live/${conversationUuid}`;
      await navigator.clipboard.writeText(url);
      
      // Show success feedback
      setCopyFeedback({show: true, message: 'Link copied to clipboard!'});
      setTimeout(() => {
        setCopyFeedback({show: false, message: ''});
      }, 2000);
    } catch (err) {
      console.error('Failed to copy live link:', err);
      // Show error feedback
      setCopyFeedback({show: true, message: 'Failed to copy link'});
      setTimeout(() => {
        setCopyFeedback({show: false, message: ''});
      }, 2000);
    }
  };

  // Function to get agent name by ID
  const getAgentName = (agentId: string | null) => {
    if (!agentId) return 'No agent assigned';
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.name : `Agent ${agentId}`;
  };

  // Filter conversations based on search and filters
  const filteredConversations = useMemo(() => {
    if (!conversations) return [];

    return conversations.filter((conv) => {
      const matchesSearch =
        !searchTerm ||
        conv.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conv.uuid.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conv.customer?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conv.customer?.email?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = filterStatus === 'all' || conv.notificationStatus === filterStatus;

      return matchesSearch && matchesStatus;
    });
  }, [conversations, searchTerm, filterStatus]);

  // Get unique statuses for filter dropdown
  const uniqueStatuses = useMemo(() => {
    if (!conversations) return [];
    const statuses = Array.from(new Set(conversations.map(conv => conv.notificationStatus).filter(Boolean)));
    return statuses as string[];
  }, [conversations]);

  const selectedConversation = useMemo(() => {
    if (!selectedConversationId || !conversationDetails) return null;

    return {
      ...conversationDetails
    } as ConversationWithDetails;
  }, [selectedConversationId, conversationDetails]);

  // Early returns for loading/error states
  if (!user) {
    return (
      <>
        <Head>
          <title>Authentication Required - Teno Store</title>
          <meta name="description" content="Please log in to view conversation debugging" />
        </Head>

        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
          <div className="text-center max-w-md w-full">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="mb-4">
                <svg className="h-12 w-12 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">Authentication Required</h2>
              <p className="text-gray-600 mb-6">You need to be logged in to view conversation debugging.</p>
              <button
                className="w-full bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
                onClick={() => router.push('/login')}
              >
                Go to Login
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Check if store is selected
  if (!currentStoreId) {
    return (
      <>
        <Head>
          <title>Store Required - Teno Store</title>
          <meta name="description" content="Please select a store to view conversations" />
        </Head>

        <div className="min-h-screen bg-slate-900">
          <TopTaskBar />
          <SideTaskBar />

          <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div className="text-center py-12">
              <svg className="h-16 w-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              <h2 className="text-2xl font-semibold text-slate-100 mb-2">Store Required</h2>
              <p className="text-slate-400 mb-6">Please select a store to view and manage conversations.</p>
              <button
                className="inline-flex items-center px-4 py-2 bg-emerald-600 text-white text-sm font-medium rounded-md hover:bg-emerald-700 transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-slate-900"
                onClick={() => router.push('/dashboard')}
              >
                Go to Dashboard
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Conversations - Teno Store</title>
        <meta name="description" content="Debug conversation tool calls, messages, and customer information" />
      </Head>

      <div className="min-h-screen bg-slate-900">
        <TopTaskBar />
        <SideTaskBar />

        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          
          {/* Copy Feedback Notification */}
          {copyFeedback.show && (
            <div className={`fixed top-20 right-6 z-50 px-4 py-2 rounded-md shadow-lg transition-all duration-300 ${
              copyFeedback.message.includes('Failed') 
                ? 'bg-red-600 text-white' 
                : 'bg-green-600 text-white'
            }`}>
              <div className="flex items-center space-x-2">
                {copyFeedback.message.includes('Failed') ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                )}
                <span className="font-medium">{copyFeedback.message}</span>
              </div>
            </div>
          )}
          
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-extrabold tracking-tight text-slate-100">Conversations</h1>
                <p className="mt-2 text-slate-300">Debug tool calls, messages, and customer information for conversations.</p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={handleStartConversation}
                  disabled={isLoading || !userIdAsBigInt || !currentStoreId}
                  className="inline-flex items-center px-4 py-2 bg-emerald-600 text-white text-sm font-medium rounded-md hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-slate-900"
                >
                  <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Start Conversation
                </button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Conversation List Panel */}
            <div className="lg:col-span-1">
              <div className="bg-slate-800 rounded-lg shadow-lg">
                <div className="p-4 border-b border-white/10">
                  <h2 className="text-lg font-semibold text-slate-100 mb-4">Conversations</h2>

                  {/* Search and Filters */}
                  <div className="space-y-3 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-1">Search</label>
                      <input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        placeholder="Search conversations..."
                        className="w-full px-3 py-2 border border-slate-600/50 rounded-md bg-slate-800/60 text-sm text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-1">Status</label>
                      <select
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                        className="w-full px-3 py-2 border border-slate-600/50 rounded-md bg-slate-800/60 text-sm text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
                      >
                        <option value="all">All Status</option>
                        {uniqueStatuses.map((status) => (
                          <option key={status} value={status}>
                            {status}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>

                {/* Conversations List */}
                <div className="max-h-96 overflow-y-auto">
                  {isLoading && (
                    <div className="p-4 text-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-400 mx-auto mb-2"></div>
                      <p className="text-sm text-slate-400">Loading conversations...</p>
                    </div>
                  )}

                  {error && (
                    <div className="p-4">
                      <div className="bg-red-500/10 border border-red-500/20 text-red-300 px-3 py-2 rounded-md text-sm">
                        Error loading conversations
                      </div>
                    </div>
                  )}

                  {!isLoading && !error && (
                    <div className="divide-y divide-white/10">
                      {filteredConversations.length === 0 ? (
                        <div className="p-4 text-center">
                          <p className="text-sm text-slate-400">No conversations found for this store</p>
                        </div>
                      ) : (
                        filteredConversations.map((conv) => {
                          const idString = conv.id.toString();
                          const isSelected = selectedConversationId === idString;
                          const lastUpdated = new Date(conv.updatedAt as unknown as string);

                          return (
                            <div
                              key={idString}
                              className={`p-4 hover:bg-white/5 transition-colors ${
                                isSelected ? 'bg-emerald-500/10 border-l-4 border-emerald-500' : ''
                              }`}
                            >
                              <button
                                onClick={() => setSelectedConversationId(idString)}
                                className="w-full text-left focus:outline-none focus:bg-white/5 rounded"
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1 min-w-0">
                                    <h3 className="text-sm font-medium text-slate-100 truncate">
                                      {conv.title ?? 'Untitled conversation'}
                                    </h3>
                                    <p className="text-xs text-slate-400 mt-1">
                                      {conv.customer?.name || conv.customer?.email || 'No customer'}
                                    </p>
                                    <p className="text-xs text-slate-500 mt-1">
                                      {isNaN(lastUpdated.getTime()) ? 'Unknown' : lastUpdated.toLocaleDateString()}
                                    </p>
                                  </div>
                                  <div className="text-xs text-slate-500">
                                    {conv._count?.messages ?? 0}
                                  </div>
                                </div>
                              </button>
                              <div className="flex gap-2 mt-3">
                                <button
                                  onClick={() => copyLiveLink(conv.uuid)}
                                  className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                                  title="Copy live link"
                                >
                                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2M15 21h6M3 10h18" />
                                  </svg>
                                  Copy Link
                                </button>
                                <button
                                  onClick={() => window.open(`/live/${conv.uuid}`, '_blank')}
                                  className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-emerald-600 text-white text-xs font-medium rounded hover:bg-emerald-700 transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                                  title="Go to live conversation"
                                >
                                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-7 4h12m-7-8h1a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V8a2 2 0 012-2h1m4 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v1" />
                                  </svg>
                                  Go Live
                                </button>
                              </div>
                            </div>
                          );
                        })
                      )}
                    </div>
                  )}
                </div>

                {/* Pagination */}
                {conversationsMeta && filteredConversations.length > 0 && (
                  <div className="p-4 border-t border-white/10">
                    <div className="flex items-center justify-between text-sm">
                      <button
                        className="text-slate-400 hover:text-slate-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={!conversationsMeta.hasPrev}
                        onClick={() => setPage((p) => Math.max(1, p - 1))}
                      >
                        Previous
                      </button>
                      <span className="text-slate-300">
                        Page {conversationsMeta.page} of {conversationsMeta.totalPages}
                      </span>
                      <button
                        className="text-slate-400 hover:text-slate-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={!conversationsMeta.hasNext}
                        onClick={() => setPage((p) => p + 1)}
                      >
                        Next
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Debug Panel */}
            <div className="lg:col-span-2">
              {!selectedConversation ? (
                <div className="bg-slate-800 rounded-lg shadow-lg p-8 text-center">
                  <svg className="h-12 w-12 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-slate-100 mb-2">Select a Conversation</h3>
                  <p className="text-slate-400">Choose a conversation from the list to view debugging information.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Conversation Header */}
                  <div className="bg-slate-800 rounded-lg shadow-lg p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h2 className="text-xl font-semibold text-slate-100">
                          {selectedConversation.title ?? 'Untitled Conversation'}
                        </h2>
                        <p className="text-sm text-slate-400 mt-1">
                          UUID: {selectedConversation.uuid}
                        </p>
                      </div>
                      <div className="text-right text-sm text-slate-400">
                        <div>Created: {new Date(selectedConversation.createdAt).toLocaleString()}</div>
                        <div>Updated: {new Date(selectedConversation.updatedAt).toLocaleString()}</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-slate-300">Status:</span>
                        <span className="ml-2 text-slate-100">{selectedConversation.notificationStatus || 'None'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">Agent:</span>
                        <span className="ml-2 text-slate-100">{getAgentName(selectedConversation.agentId || null)}</span>
                      </div>
                    </div>

                    {selectedConversation.context && (
                      <div className="mt-4">
                        <span className="font-medium text-slate-300 block mb-2">Context:</span>
                        <pre className="bg-slate-900 border border-slate-600 rounded-md p-3 text-xs text-slate-100 overflow-x-auto">
                          {JSON.stringify(selectedConversation.context, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>

                  {/* Customer Information */}
                  <div className="bg-slate-800 rounded-lg shadow-lg">
                    <div className="px-6 py-4 border-b border-white/10">
                      <h3 className="font-medium text-slate-100">Customer Information</h3>
                    </div>
                    <div className="p-6">
                      {selectedConversation.customer ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-slate-300 mb-1">Name</label>
                            <div className="text-slate-100 bg-slate-900/60 border border-slate-600/50 rounded-md px-3 py-2">
                              {selectedConversation.customer.name || 'Not provided'}
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-slate-300 mb-1">Email</label>
                            <div className="text-slate-100 bg-slate-900/60 border border-slate-600/50 rounded-md px-3 py-2">
                              {selectedConversation.customer.email || 'Not provided'}
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-slate-300 mb-1">Phone</label>
                            <div className="text-slate-100 bg-slate-900/60 border border-slate-600/50 rounded-md px-3 py-2">
                              {selectedConversation.customer.phone || 'Not provided'}
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-slate-300 mb-1">Address</label>
                            <div className="text-slate-100 bg-slate-900/60 border border-slate-600/50 rounded-md px-3 py-2">
                              {selectedConversation.customer.address || 'Not provided'}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <p className="text-slate-400">No customer information available</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Unified Timeline */}
                  <div className="bg-slate-800 rounded-lg shadow-lg">
                    <div className="px-6 py-4 border-b border-white/10">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-slate-100">
                          Conversation Timeline 
                          {unifiedTimeline && (
                            <span className="ml-2 text-sm text-slate-400">
                              ({unifiedTimeline.total} events)
                            </span>
                          )}
                        </h3>
                        <button
                          onClick={() => fetchUnifiedTimeline()}
                          disabled={isLoadingTimeline}
                          className="px-3 py-1 text-sm bg-slate-700 text-slate-300 rounded-md hover:bg-slate-600 disabled:opacity-50 transition-colors"
                          title="Refresh timeline"
                        >
                          {isLoadingTimeline ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-emerald-400 mx-auto"></div>
                          ) : (
                            '🔄 Refresh'
                          )}
                        </button>
                      </div>
                    </div>
                    <div className="p-6">
                      {isLoadingTimeline ? (
                        <div className="text-center py-4">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-400 mx-auto mb-2"></div>
                          <p className="text-sm text-slate-400">Loading timeline...</p>
                        </div>
                      ) : error ? (
                        <div className="p-4 text-center text-red-300">
                          Error loading timeline: {error}
                        </div>
                      ) : !unifiedTimeline ? (
                        <div className="text-center py-8">
                          <p className="text-slate-400">Select a conversation to view its timeline.</p>
                        </div>
                      ) : unifiedTimeline.timeline.length === 0 ? (
                        <div className="text-center py-8">
                          <p className="text-slate-400">No events found in this conversation.</p>
                        </div>
                      ) : (
                        <>
                          <div className="space-y-4 max-h-96 overflow-y-auto">
                            {unifiedTimeline.timeline.map((item: any) => (
                              <TimelineItem key={item.id.toString()} item={item} />
                            ))}
                          </div>
                          
                          {/* Timeline Pagination */}
                          {unifiedTimeline.total > timelineLimit && (
                            <div className="mt-6 pt-4 border-t border-slate-600/30">
                              <div className="flex items-center justify-between">
                                <div className="text-sm text-slate-400">
                                  Showing {((timelinePage - 1) * timelineLimit) + 1} to {Math.min(timelinePage * timelineLimit, unifiedTimeline.total)} of {unifiedTimeline.total} events
                                </div>
                                <div className="flex items-center gap-2">
                                  <button
                                    onClick={() => setTimelinePage((p) => Math.max(1, p - 1))}
                                    disabled={timelinePage === 1}
                                    className="px-3 py-1 text-sm bg-slate-700 text-slate-300 rounded-md hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                  >
                                    Previous
                                  </button>
                                  <span className="text-sm text-slate-400">
                                    Page {timelinePage} of {Math.ceil(unifiedTimeline.total / timelineLimit)}
                                  </span>
                                  <button
                                    onClick={() => setTimelinePage((p) => p + 1)}
                                    disabled={timelinePage >= Math.ceil(unifiedTimeline.total / timelineLimit)}
                                    className="px-3 py-1 text-sm bg-slate-700 text-slate-300 rounded-md hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                  >
                                    Next
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Conversation Info Modal */}
        {showConversationModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-slate-800 rounded-lg shadow-lg p-6 max-w-2xl w-full mx-4">
              {isLoading ? (
                // Loading State
                <div className="text-center py-8">
                  <div className="animate-spin h-12 w-12 text-emerald-400 mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-slate-100 mb-2">Creating Conversation...</h3>
                  <p className="text-slate-300">Please wait while we set up your new conversation.</p>
                </div>
              ) : !newConversation ? (
                // Agent Selection State
                <>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-slate-100">Select Agent</h3>
                    <button
                      onClick={() => setShowConversationModal(false)}
                      className="text-slate-400 hover:text-slate-200 transition-colors"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Choose an agent for this conversation:
                    </label>
                    <select
                      value={selectedAgentId}
                      onChange={(e) => setSelectedAgentId(e.target.value)}
                      className="w-full px-3 py-2 border border-slate-600/50 rounded-md bg-slate-800/60 text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
                    >
                      <option value="">Select an agent...</option>
                      {agents.map((agent) => (
                        <option key={agent.id} value={agent.id}>
                          {agent.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="flex gap-3">
                    <button
                      onClick={() => setShowConversationModal(false)}
                      className="flex-1 px-4 py-2 bg-slate-600 text-slate-200 text-sm font-medium rounded-md hover:bg-slate-500 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => {
                        if (selectedAgentId) {
                          // Note: Backend doesn't currently support agent assignment at conversation level
                          // Agent will be assigned when first message is sent
                          handleCreateConversation({
                            title: 'New Conversation',
                            userId: userIdAsBigInt?.toString(),
                            storeId: currentStoreId,
                            createdBy: userIdAsBigInt?.toString(),
                          });
                        }
                      }}
                      disabled={!selectedAgentId}
                      className="flex-1 px-4 py-2 bg-emerald-600 text-white text-sm font-medium rounded-md hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                    >
                      Create Conversation
                    </button>
                  </div>
                </>
              ) : (
                // Success State
                <>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-slate-100">New Conversation Created!</h3>
                    <button
                      onClick={() => setShowConversationModal(false)}
                      className="text-slate-400 hover:text-slate-200 transition-colors"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  
                  {/* Live Link Section */}
                  <div className="bg-slate-700 rounded-lg p-4 mb-6">
                    <div className="mb-4">
                      <h4 className="text-lg font-medium text-slate-200 mb-2">Live Conversation Link</h4>
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-slate-800 rounded-md p-3 border border-slate-600 hover:border-slate-500 transition-colors">
                          <span 
                            className="text-slate-100 font-mono text-xs break-all select-all cursor-pointer hover:text-emerald-300 transition-colors"
                            onClick={() => copyLiveLink(newConversation.uuid)}
                            title="Click to copy link"
                          >
                            {`${window.location.origin}/live/${newConversation.uuid}`}
                          </span>
                        </div>
                        <button
                          onClick={() => copyLiveLink(newConversation.uuid)}
                          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-700 flex items-center space-x-2"
                          title="Copy link to clipboard"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2M15 21h6M3 10h18" />
                          </svg>
                          <span className="text-sm font-medium">Copy</span>
                        </button>
                        <button
                          onClick={() => {
                            if (newConversation.uuid) {
                              window.open(`/live/${newConversation.uuid}`, '_blank');
                            }
                          }}
                          className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-slate-700 flex items-center space-x-2"
                          title="Open live conversation in new tab"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-7 4h12m-7-8h1a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V8a2 2 0 012-2h1m4 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v1" />
                          </svg>
                          <span className="text-sm font-medium">Go Live</span>
                        </button>
                      </div>
                    </div>
                    
                    {/* Other Conversation Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-slate-300">Title:</span>
                        <span className="ml-2 text-slate-100">{newConversation.title || 'Untitled'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">UUID:</span>
                        <span className="ml-2 text-slate-100 font-mono text-xs">{newConversation.uuid}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">Agent:</span>
                        <span className="ml-2 text-slate-100">{getAgentName(newConversation.agentId || null)}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">Created:</span>
                        <span className="ml-2 text-slate-100">{new Date(newConversation.createdAt).toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">Status:</span>
                        <span className="ml-2 text-slate-100">{newConversation.notificationStatus || 'None'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">User:</span>
                        <span className="ml-2 text-slate-100">{newConversation.user?.name || newConversation.user?.email || 'Unknown'}</span>
                      </div>
                      {newConversation.totalCost !== null && (
                        <div>
                          <span className="font-medium text-emerald-300">💰 Total Cost:</span>
                          <span className="ml-2 text-emerald-100">
                            {typeof newConversation.totalCost === 'number' 
                              ? `$${newConversation.totalCost.toFixed(6)}` 
                              : `$${newConversation.totalCost}`
                            }
                          </span>
                        </div>
                      )}
                      {newConversation.totalExecutionTime !== null && (
                        <div>
                          <span className="font-medium text-blue-300">⏱️ Total Time:</span>
                          <span className="ml-2 text-blue-100">{newConversation.totalExecutionTime}ms</span>
                        </div>
                      )}
                    </div>
                  </div>



                  <div className="text-center">
                    <button
                      onClick={() => setShowConversationModal(false)}
                      className="text-slate-400 hover:text-slate-200 transition-colors text-sm"
                    >
                      Close
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
}
