<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Teno Store - Your complete e-commerce solution with AI-powered order management. Build beautiful storefronts with intelligent automation.">
    <meta name="keywords" content="e-commerce, AI, order management, storefront, inventory, online store, shopify alternative">
    <meta name="author" content="Teno Store">
    <meta property="og:title" content="Teno Store - AI-Powered E-commerce Solution">
    <meta property="og:description" content="Transform your business with AI-powered e-commerce. Complete storefront solution with intelligent order management.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://tenostore.com">
    
    <title>Teno Store - AI-Powered E-commerce Solution</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background-color: #ffffff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        /* Header */
        header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e5e7eb;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 800;
            color: #3b82f6;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #6b7280;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #3b82f6;
        }

        .cta-button {
            background: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .cta-button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        /* Hero Section */
        .hero {
            padding: 8rem 0 4rem;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.25rem;
            color: #6b7280;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 3rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #3b82f6;
            padding: 1rem 2rem;
            border: 2px solid #3b82f6;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .btn-secondary:hover {
            background: #3b82f6;
            color: white;
            transform: translateY(-2px);
        }

        /* Features Section */
        .features {
            padding: 4rem 0;
            background: white;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: #6b7280;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            padding: 2rem;
            border-radius: 1rem;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 3rem;
            height: 3rem;
            background: #3b82f6;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #1f2937;
        }

        .feature-card p {
            color: #6b7280;
            line-height: 1.6;
        }

        /* Pricing Section */
        .pricing {
            padding: 4rem 0;
            background: #f8fafc;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .pricing-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
            position: relative;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .pricing-card.featured {
            border-color: #3b82f6;
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: "Most Popular";
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #1f2937;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 800;
            color: #3b82f6;
            margin-bottom: 0.5rem;
        }

        .plan-price-period {
            color: #6b7280;
            font-size: 1rem;
            margin-bottom: 1.5rem;
        }

        .plan-description {
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .plan-features li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            color: #4b5563;
        }

        .plan-features li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        .plan-button {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            display: block;
        }

        .plan-button.primary {
            background: #3b82f6;
            color: white;
        }

        .plan-button.primary:hover {
            background: #2563eb;
        }

        .plan-button.secondary {
            background: transparent;
            color: #3b82f6;
            border: 2px solid #3b82f6;
        }

        .plan-button.secondary:hover {
            background: #3b82f6;
            color: white;
        }

        /* CTA Section */
        .cta-section {
            padding: 4rem 0;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            text-align: center;
        }

        .cta-section h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .cta-section p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .cta-section .btn-primary {
            background: white;
            color: #3b82f6;
        }

        .cta-section .btn-primary:hover {
            background: #f1f5f9;
            transform: translateY(-2px);
        }

        /* Footer */
        footer {
            background: #1f2937;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: white;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: #9ca3af;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-section ul li a:hover {
            color: #3b82f6;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 1rem;
            text-align: center;
            color: #9ca3af;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .hero-buttons a {
                width: 100%;
                max-width: 300px;
                text-align: center;
            }

            .section-title {
                font-size: 2rem;
            }

            .pricing-card.featured {
                transform: none;
            }

            .cta-section h2 {
                font-size: 2rem;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header id="header">
        <nav class="container">
            <a href="#" class="logo">🛍️ Teno Store</a>
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#pricing">Pricing</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <a href="#pricing" class="cta-button">Get Started</a>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1 class="fade-in-up">AI-Powered E-commerce<br>for Modern Businesses</h1>
            <p class="fade-in-up">Transform your business with Teno Store - the complete e-commerce solution that combines beautiful storefronts with intelligent AI agents for automated order management.</p>
            <div class="hero-buttons fade-in-up">
                <a href="#pricing" class="btn-primary">Start Free Trial</a>
                <a href="#features" class="btn-secondary">Learn More</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">Everything You Need to Succeed</h2>
            <p class="section-subtitle">From beautiful storefronts to AI-powered automation, Teno Store provides all the tools you need to build and scale your e-commerce business.</p>
            
            <div class="features-grid">
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">🛍️</div>
                    <h3>Beautiful Storefronts</h3>
                    <p>Create stunning, mobile-responsive stores with multi-language support and SEO optimization. Your customers will love the shopping experience.</p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">🤖</div>
                    <h3>AI Order Management</h3>
                    <p>Intelligent agents handle customer conversations, process orders, and update customer information automatically using natural language processing.</p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">📦</div>
                    <h3>Smart Inventory</h3>
                    <p>Comprehensive product management with automatic stock tracking, category organization, and bulk operations for efficient inventory control.</p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">👥</div>
                    <h3>Customer Management</h3>
                    <p>Centralized customer database with order history, communication tools, and automated relationship management for better customer service.</p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">📊</div>
                    <h3>Sales Analytics</h3>
                    <p>Real-time reporting and performance analytics to track your business metrics, revenue trends, and key performance indicators.</p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">🌍</div>
                    <h3>Global Ready</h3>
                    <p>Support for 15+ languages and 40+ currencies with region-specific formatting and localization for worldwide reach.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <h2 class="section-title">Choose Your Plan</h2>
            <p class="section-subtitle">Start free and scale as you grow. All plans include our core features with different limits and capabilities.</p>
            
            <div class="pricing-grid">
                <!-- Free Plan -->
                <div class="pricing-card fade-in-up">
                    <div class="plan-name">Free</div>
                    <div class="plan-price">$0</div>
                    <div class="plan-price-period">Forever free</div>
                    <div class="plan-description">Perfect for getting started with your first store and testing our platform.</div>
                    <ul class="plan-features">
                        <li>1 Store</li>
                        <li>Up to 50 Products</li>
                        <li>Basic AI Agent (100 conversations/month)</li>
                        <li>Mobile-responsive Storefront</li>
                        <li>Order Management</li>
                        <li>Customer Database</li>
                        <li>Email Support</li>
                        <li>SSL Certificate</li>
                    </ul>
                    <a href="#" class="plan-button secondary">Get Started Free</a>
                </div>

                <!-- Pro Plan -->
                <div class="pricing-card featured fade-in-up">
                    <div class="plan-name">Pro</div>
                    <div class="plan-price">$29</div>
                    <div class="plan-price-period">per month</div>
                    <div class="plan-description">Ideal for growing businesses that need advanced features and higher limits.</div>
                    <ul class="plan-features">
                        <li>5 Stores</li>
                        <li>Unlimited Products</li>
                        <li>Advanced AI Agents (Unlimited conversations)</li>
                        <li>Multi-language Support (15+ languages)</li>
                        <li>Advanced Analytics</li>
                        <li>Custom Branding</li>
                        <li>API Access</li>
                        <li>Priority Support</li>
                        <li>Webhook Integration</li>
                        <li>Advanced Reporting</li>
                    </ul>
                    <a href="#" class="plan-button primary">Start Pro Trial</a>
                </div>

                <!-- Custom Plan -->
                <div class="pricing-card fade-in-up">
                    <div class="plan-name">Custom</div>
                    <div class="plan-price">Custom</div>
                    <div class="plan-price-period">Let's talk</div>
                    <div class="plan-description">Enterprise solution with unlimited everything and dedicated support for large businesses.</div>
                    <ul class="plan-features">
                        <li>Unlimited Stores</li>
                        <li>Unlimited Products</li>
                        <li>Enterprise AI Agents</li>
                        <li>Custom Integrations</li>
                        <li>Dedicated Account Manager</li>
                        <li>White-label Solution</li>
                        <li>Custom Development</li>
                        <li>24/7 Phone Support</li>
                        <li>SLA Guarantee</li>
                        <li>On-premise Deployment</li>
                    </ul>
                    <a href="#contact" class="plan-button secondary">Contact Sales</a>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <h2>Ready to Transform Your Business?</h2>
            <p>Join thousands of businesses already using Teno Store to automate their e-commerce operations with AI.</p>
            <a href="#pricing" class="btn-primary">Start Your Free Trial</a>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>🛍️ Teno Store</h3>
                    <p style="color: #9ca3af; margin-top: 1rem;">Your complete e-commerce solution with AI-powered order management. Transform your business with intelligent automation.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Product</h3>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#">API Documentation</a></li>
                        <li><a href="#">Integrations</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Press</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Support</a></li>
                        <li><a href="#">System Status</a></li>
                        <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Teno Store. All rights reserved. | <a href="#" style="color: #3b82f6;">Privacy Policy</a> | <a href="#" style="color: #3b82f6;">Terms of Service</a></p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header background on scroll
        window.addEventListener('scroll', function() {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all fade-in-up elements
        document.querySelectorAll('.fade-in-up').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
            observer.observe(el);
        });

        // Simple form handling for plan buttons
        document.querySelectorAll('.plan-button, .btn-primary, .btn-secondary').forEach(button => {
            button.addEventListener('click', function(e) {
                if (this.textContent.includes('Free') || this.textContent.includes('Trial')) {
                    e.preventDefault();
                    alert('Free trial signup would redirect to registration page!');
                } else if (this.textContent.includes('Contact') || this.textContent.includes('Sales')) {
                    e.preventDefault();
                    alert('Contact form would open here! Email: <EMAIL>');
                }
            });
        });

        // Add loading animation for page load
        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
            document.body.style.transition = 'opacity 0.3s ease-in';
        });

        // Initialize page
        document.body.style.opacity = '0';
    </script>
</body>
</html>
