export interface AuthUser {
  id: string;
  email: string;
  name?: string;
}

export interface MeResponse {
  user: AuthUser | null;
}

export const getBackendUrl = (): string => {
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
};

export const clearClientAuthArtifacts = () => {
  console.log('[clearClientAuthArtifacts] Clearing all auth artifacts');
  try {
    // Clear local cache of user
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('teno:auth:user');
      localStorage.removeItem('teno:auth:token');
      console.log('[clearClientAuthArtifacts] Cleared localStorage tokens');
    }
  } catch {}
  try {
    // Proactively drop any readable client token if it exists
    if (typeof document !== 'undefined') {
      // Expire both potential names just in case
      document.cookie = 'access_token_client=; Path=/; Max-Age=0; SameSite=Lax';
      document.cookie = 'access_token=; Path=/; Max-Age=0; SameSite=Lax';
      console.log('[clearClientAuthArtifacts] Cleared cookie tokens');
    }
  } catch {}
};

export const handleUnauthorized = () => {
  console.log('[handleUnauthorized] Called - clearing auth artifacts and redirecting to login');
  // Ensure client artifacts are cleared immediately
  clearClientAuthArtifacts();
  try {
    // Notify any listeners (e.g., UI) that auth state became unauthorized
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('teno:auth:unauthorized'));
    }
  } catch {}
  // Best-effort redirect to login preserving next path
  try {
    if (typeof window !== 'undefined') {
      const next = encodeURIComponent(window.location.pathname + window.location.search);
      const target = `/login?loggedOut=1&next=${next}`;
      // Avoid infinite loops if we are already on login
      if (!window.location.pathname.startsWith('/login')) {
        console.log('[handleUnauthorized] Redirecting to:', target);
        window.location.replace(target);
      }
    }
  } catch {}
};

export const fetchWithCredentials = async (input: string, init?: RequestInit) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(init && init.headers ? (init.headers as Record<string, string>) : {}),
  };

  // Try to get token from multiple sources in order of preference
  try {
    if (typeof document !== 'undefined' && !headers['Authorization']) {
      let token: string | undefined;
      
      // First try cookie (primary method)
      const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);
      if (cookieMatch) {
        token = decodeURIComponent(cookieMatch[1]);
        console.log('[fetchWithCredentials] Token found in cookie:', token.substring(0, 20) + '...');
      } else {
        console.log('[fetchWithCredentials] No token found in cookie');
      }
      
      // Fallback to localStorage if cookie not found
      if (!token) {
        try {
          token = localStorage.getItem('teno:auth:token') || undefined;
          if (token) {
            console.log('[fetchWithCredentials] Token found in localStorage:', token.substring(0, 20) + '...');
          } else {
            console.log('[fetchWithCredentials] No token found in localStorage');
          }
        } catch (e) {
          console.warn('Could not access localStorage:', e);
        }
      }
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
        console.log('[fetchWithCredentials] Token added to Authorization header');
      } else {
        console.log('[fetchWithCredentials] No token available for Authorization header');
      }
    }
  } catch (e) {
    console.warn('Error getting auth token:', e);
  }

  const response = await fetch(input, {
    ...init,
    credentials: 'include',
    headers,
  });

  // If backend says unauthorized/forbidden, clear local auth and nudge UI
  if (response.status === 401 || response.status === 403) {
    handleUnauthorized();
  }

  return response;
};

export const getCurrentUser = async (): Promise<AuthUser | null> => {
  // Check if we have a token before making the API call
  let hasToken = false;
  
  try {
    if (typeof document !== 'undefined') {
      const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);
      if (cookieMatch) {
        hasToken = true;
      }
    }
    
    if (!hasToken && typeof localStorage !== 'undefined') {
      try {
        const token = localStorage.getItem('teno:auth:token');
        if (token) {
          hasToken = true;
        }
      } catch (e) {
        console.warn('Could not access localStorage:', e);
      }
    }
  } catch (e) {
    console.warn('Error checking for token:', e);
  }
  
  if (!hasToken) {
    console.log('[getCurrentUser] No token found, skipping API call');
    return null;
  }
  
  const url = `${getBackendUrl()}/auth/me`;
  console.log('[getCurrentUser] Making API call to:', url);
  const response = await fetchWithCredentials(url);
  
  if (!response.ok) {
    console.log('[getCurrentUser] API call failed with status:', response.status);
    return null;
  }
  
  const data: MeResponse = await response.json();
  console.log('[getCurrentUser] API call successful, user:', data.user);
  return data.user ?? null;
};

export const performLogout = async (): Promise<void> => {
  const url = `${getBackendUrl()}/auth/logout`;
  try {
    await fetchWithCredentials(url, { method: 'POST' });
  } finally {
    // Always clear client artifacts regardless of server response
    clearClientAuthArtifacts();
  }
};

export const redirectToGoogleAuth = (nextPath?: string): void => {
  let url = `${getBackendUrl()}/auth/google`;
  try {
    // Prefer explicit nextPath, otherwise pick it up from current URL (?next=...)
    let nextParam = nextPath;
    if (!nextParam && typeof window !== 'undefined') {
      const sp = new URLSearchParams(window.location.search);
      const qp = sp.get('next') || undefined;
      nextParam = qp || undefined;
    }
    if (nextParam) {
      // Only allow app-internal paths starting with '/'
      const safeNext = decodeURIComponent(nextParam);
      if (safeNext.startsWith('/')) {
        url += `?next=${encodeURIComponent(safeNext)}`;
      }
    }
  } catch {}
  if (typeof window !== 'undefined') {
    window.location.href = url;
  }
};


export const debugLogin = async (email: string, name?: string): Promise<AuthUser | null> => {
  const url = `${getBackendUrl()}/auth/dev-login`;
  console.log('[debugLogin] Attempting debug login for:', email);
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, name }),
  });
  
  if (!response.ok) {
    console.error('[debugLogin] Login failed with status:', response.status);
    return null;
  }
  
  const data = await response.json();
  console.log('[debugLogin] Login successful, received data:', data);
  
  // Extract and store the token
  if (data.access_token) {
    console.log('[debugLogin] Storing access token');
    
    // Store in cookie
    const isLocalhost = typeof window !== 'undefined' && (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
    const cookieOptions = isLocalhost 
      ? `path=/; max-age=86400; samesite=lax`
      : `path=/; max-age=86400; secure; samesite=strict`;
    
    document.cookie = `access_token_client=${encodeURIComponent(data.access_token)}; ${cookieOptions}`;
    
    // Store in localStorage as backup
    try {
      localStorage.setItem('teno:auth:token', data.access_token);
    } catch (e) {
      console.warn('[debugLogin] Could not store token in localStorage:', e);
    }
    
    console.log('[debugLogin] Token stored successfully');
  } else {
    console.warn('[debugLogin] No access_token in response');
  }
  
  return data.user ?? null;
};



