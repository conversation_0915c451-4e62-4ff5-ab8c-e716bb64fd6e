"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerStatus = exports.ProductStatus = exports.StoreStatus = exports.UserStatus = exports.UserRole = exports.Currency = void 0;
var Currency;
(function (Currency) {
    Currency["USD"] = "USD";
    Currency["EUR"] = "EUR";
    Currency["GBP"] = "GBP";
    Currency["CAD"] = "CAD";
    Currency["AUD"] = "AUD";
    Currency["JPY"] = "JPY";
    Currency["CNY"] = "CNY";
    Currency["INR"] = "INR";
    Currency["BRL"] = "BRL";
    Currency["MXN"] = "MXN";
})(Currency || (exports.Currency = Currency = {}));
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["MANAGER"] = "manager";
    UserRole["STAFF"] = "staff";
    UserRole["CUSTOMER"] = "customer";
})(UserRole || (exports.UserRole = UserRole = {}));
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "active";
    UserStatus["INACTIVE"] = "inactive";
    UserStatus["SUSPENDED"] = "suspended";
    UserStatus["PENDING"] = "pending";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
var StoreStatus;
(function (StoreStatus) {
    StoreStatus["ACTIVE"] = "active";
    StoreStatus["INACTIVE"] = "inactive";
    StoreStatus["SUSPENDED"] = "suspended";
    StoreStatus["PENDING"] = "pending";
})(StoreStatus || (exports.StoreStatus = StoreStatus = {}));
var ProductStatus;
(function (ProductStatus) {
    ProductStatus["ACTIVE"] = "active";
    ProductStatus["INACTIVE"] = "inactive";
    ProductStatus["OUT_OF_STOCK"] = "out_of_stock";
    ProductStatus["DISCONTINUED"] = "discontinued";
})(ProductStatus || (exports.ProductStatus = ProductStatus = {}));
var CustomerStatus;
(function (CustomerStatus) {
    CustomerStatus["ACTIVE"] = "active";
    CustomerStatus["INACTIVE"] = "inactive";
    CustomerStatus["SUSPENDED"] = "suspended";
})(CustomerStatus || (exports.CustomerStatus = CustomerStatus = {}));
//# sourceMappingURL=enums.js.map