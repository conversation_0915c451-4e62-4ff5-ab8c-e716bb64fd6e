import {
  Controller,
  Get,
  Post,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  Res,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes } from '@nestjs/swagger';
import { ImagesService } from './images.service';

@ApiTags('images')
@Controller('images')
export class ImagesController {
  constructor(private readonly imagesService: ImagesService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('image'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload an image' })
  @ApiResponse({ status: 201, description: 'Image uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async uploadImage(@UploadedFile() file: any) {
    const filename = await this.imagesService.processImage(file);
    return { filename };
  }

  @Get(':filename')
  @ApiOperation({ summary: 'Get an image by filename' })
  @ApiResponse({ status: 200, description: 'Image retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Image not found' })
  async getImage(@Param('filename') filename: string, @Res() res: Response) {
    try {
      const imageBuffer = await this.imagesService.getImage(filename);
      res.set({
        'Content-Type': 'image/jpeg',
        'Content-Length': imageBuffer.length.toString(),
      });
      res.send(imageBuffer);
    } catch (error) {
      res.status(404).json({ message: 'Image not found' });
    }
  }

  @Delete(':filename')
  @ApiOperation({ summary: 'Delete an image' })
  @ApiResponse({ status: 200, description: 'Image deleted successfully' })
  async deleteImage(@Param('filename') filename: string) {
    await this.imagesService.deleteImage(filename);
    return { message: 'Image deleted successfully' };
  }
}
