import { useMemo, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { usePreferences } from '../../context/PreferencesContext';

type LangKey = 'en' | 'fr' | 'ar';
const messages: Record<LangKey, Record<string, string>> = {
  en: {
    title: 'Track Your Order - Teno Store',
    pageHeading: 'Track Your Order',
    pageSub: 'Enter your order number to check your order status and delivery information',
    orderNumber: 'Order Number',
    placeholder: 'e.g., ORD-20250115-001',
    helpTitle: 'Need Help?',
    helpText: "If you can't find your order number, check your email confirmation or contact our support team.",
    backToStore: '← Back to Store',
    buttonSearch: 'Searching...',
    buttonTrack: 'Track Order',
    metaDesc: 'Enter your order number to track your order status and delivery information',
  },
  fr: {
    title: 'Suivre votre commande - Teno Store',
    pageHeading: 'Suivre votre commande',
    pageSub: 'Entrez votre numéro de commande pour consulter son statut et les informations de livraison',
    orderNumber: 'Numéro de commande',
    placeholder: 'ex.: ORD-20250115-001',
    helpTitle: 'Besoin d’aide ?',
    helpText: "Si vous ne trouvez pas votre numéro de commande, vérifiez l’e-mail de confirmation ou contactez notre support.",
    backToStore: '← Retour à la boutique',
    buttonSearch: 'Recherche...',
    buttonTrack: 'Suivre la commande',
    metaDesc: 'Entrez votre numéro de commande pour suivre le statut et la livraison',
  },
  ar: {
    title: 'تتبع طلبك - متجر تينو',
    pageHeading: 'تتبع طلبك',
    pageSub: 'أدخل رقم الطلب للتحقق من حالة الطلب ومعلومات التسليم',
    orderNumber: 'رقم الطلب',
    placeholder: 'مثال: ORD-20250115-001',
    helpTitle: 'تحتاج مساعدة؟',
    helpText: 'إذا لم تتمكن من العثور على رقم الطلب، تحقق من بريد التأكيد أو تواصل مع الدعم.',
    backToStore: '← العودة إلى المتجر',
    buttonSearch: 'جارٍ البحث...',
    buttonTrack: 'تتبع الطلب',
    metaDesc: 'أدخل رقم الطلب لتتبع حالة الطلب ومعلومات التسليم',
  },
};

export default function TrackOrderHomePage() {
  const router = useRouter();
  const { language } = usePreferences();
  const [orderNumber, setOrderNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const langKey = useMemo<LangKey>(() => {
    const lng = (language || 'en').toLowerCase();
    if (lng.startsWith('fr')) return 'fr';
    if (lng.startsWith('ar')) return 'ar';
    return 'en';
  }, [language]);
  const t = messages[langKey];
  const dir = langKey === 'ar' ? 'rtl' : 'ltr';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!orderNumber.trim()) {
      return;
    }

    setIsLoading(true);
    
    // Navigate to the order tracking page
    router.push(`/track/${orderNumber.trim()}`);
  };

  return (
    <div dir={dir} className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
      <Head>
        <title>{t.title}</title>
        <meta name="description" content={t.metaDesc} />
      </Head>

      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-indigo-100 mb-6">
            <svg className="h-10 w-10 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h2 className="text-3xl font-extrabold tracking-tight text-gray-900">{t.pageHeading}</h2>
          <p className="mt-2 text-gray-600">{t.pageSub}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-lg">
          <form onSubmit={handleSubmit} className="space-y-6 p-8">
            <div>
              <label htmlFor="orderNumber" className="block text-sm font-medium text-gray-700 mb-2">
                {t.orderNumber}
              </label>
              <input
                id="orderNumber"
                name="orderNumber"
                type="text"
                required
                value={orderNumber}
                onChange={(e) => setOrderNumber(e.target.value)}
                className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 placeholder-gray-500"
                placeholder={t.placeholder}
              />
              <p className="mt-1 text-xs text-gray-500">{t.pageSub}</p>
            </div>
            
            <button
              type="submit"
              disabled={isLoading || !orderNumber.trim()}
              className="w-full bg-indigo-600 text-white py-3 px-4 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            >
              {isLoading ? t.buttonSearch : t.buttonTrack}
            </button>
          </form>
        </div>

        {/* Sample order number for demo */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <svg className="h-5 w-5 text-blue-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-blue-800">{t.helpTitle}</h3>
              <p className="text-sm text-blue-700">{t.helpText}</p>
            </div>
          </div>
        </div>

        {/* Back to Store */}
        <div className="text-center">
          <button
            onClick={() => router.push('/')}
            className="text-indigo-600 hover:text-indigo-500 font-medium transition-colors"
          >
            {t.backToStore}
          </button>
        </div>
      </div>
    </div>
  );
}
