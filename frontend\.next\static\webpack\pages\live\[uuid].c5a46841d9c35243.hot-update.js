"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/live/[uuid]",{

/***/ "./src/pages/live/[uuid].tsx":
/*!***********************************!*\
  !*** ./src/pages/live/[uuid].tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PublicLiveConversationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/TimelineItem */ \"./src/components/TimelineItem.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Icon Components\nconst CustomerIcon = (param)=>{\n    let { name } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-orange-400/30\",\n        children: name.charAt(0).toUpperCase()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n};\n_c = CustomerIcon;\nfunction PublicLiveConversationPage() {\n    var _timeline_timeline, _conversation_store;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { uuid } = router.query;\n    const [page] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isCustomerNameSet, setIsCustomerNameSet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isWaitingForAgent, setIsWaitingForAgent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const waitingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const limit = 50;\n    const conversationUuid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        // Wait for router to be ready and uuid to be available\n        if (router.isReady && uuid) {\n            return uuid;\n        }\n        return null;\n    }, [\n        router.isReady,\n        uuid\n    ]);\n    // Fetch conversation data\n    const fetchConversation = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getByUuid(conversationUuid);\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setConversation(result.data || result);\n            } else {\n                setConversation(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching conversation:\", err);\n            setError(\"Failed to load conversation\");\n        }\n    };\n    // Fetch timeline data\n    const fetchTimeline = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getUnifiedTimelineByUuid(conversationUuid, {\n                page,\n                limit\n            });\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setTimeline(result.data || result);\n            } else {\n                setTimeline(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching timeline:\", err);\n            setError(\"Failed to load timeline\");\n        }\n    };\n    // Fetch data when dependencies change\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (conversationUuid && router.isReady) {\n            fetchConversation();\n            fetchTimeline();\n        }\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    // Set up polling for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!conversationUuid || !router.isReady) return;\n        const interval = setInterval(()=>{\n            fetchConversation();\n            fetchTimeline();\n        }, 2000); // Reduced from 5000ms to 2000ms for better responsiveness\n        return ()=>clearInterval(interval);\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    const appendMessage = async (messageData)=>{\n        if (!conversationUuid) return;\n        try {\n            var _messagesEndRef_current;\n            console.log(\"\\uD83D\\uDD27 Frontend: Sending message:\", messageData);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.appendMessageByUuid(conversationUuid, messageData);\n            console.log(\"✅ Frontend: Message sent successfully:\", response);\n            setNewMessage(\"\");\n            // Set waiting state for agent response (only for user messages)\n            const isUserMessage = !messageData.agentId || messageData.agentId === \"customer-message\";\n            if (isUserMessage) {\n                setIsWaitingForAgent(true);\n                // Clear any existing timeout\n                if (waitingTimeoutRef.current) {\n                    clearTimeout(waitingTimeoutRef.current);\n                }\n                // Set a timeout to clear waiting state if no response comes (30 seconds)\n                waitingTimeoutRef.current = setTimeout(()=>{\n                    setIsWaitingForAgent(false);\n                }, 30000);\n            }\n            // Refresh data\n            fetchConversation();\n            fetchTimeline();\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        } catch (err) {\n            console.error(\"❌ Frontend: Failed to send message:\", err);\n            setError(\"Failed to send message\");\n            setIsWaitingForAgent(false);\n        }\n    };\n    // Use a ref to track previous message count to prevent unnecessary effects\n    const prevMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    const prevAgentMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        var _timeline_timeline;\n        const currentMessageCount = (timeline === null || timeline === void 0 ? void 0 : (_timeline_timeline = timeline.timeline) === null || _timeline_timeline === void 0 ? void 0 : _timeline_timeline.length) || 0;\n        if (currentMessageCount > prevMessageCountRef.current) {\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n            prevMessageCountRef.current = currentMessageCount;\n        }\n        // Check for new agent messages to clear waiting state\n        if (timeline === null || timeline === void 0 ? void 0 : timeline.timeline) {\n            const agentMessages = timeline.timeline.filter((item)=>{\n                var _item_data_metadata, _item_data, _item_data_metadata1, _item_data1;\n                return item.type === \"message\" && ((_item_data = item.data) === null || _item_data === void 0 ? void 0 : (_item_data_metadata = _item_data.metadata) === null || _item_data_metadata === void 0 ? void 0 : _item_data_metadata.agentId) && ((_item_data1 = item.data) === null || _item_data1 === void 0 ? void 0 : (_item_data_metadata1 = _item_data1.metadata) === null || _item_data_metadata1 === void 0 ? void 0 : _item_data_metadata1.agentId) !== \"customer-message\";\n            });\n            const currentAgentMessageCount = agentMessages.length;\n            if (currentAgentMessageCount > prevAgentMessageCountRef.current && isWaitingForAgent) {\n                setIsWaitingForAgent(false);\n                // Clear the timeout since we got a response\n                if (waitingTimeoutRef.current) {\n                    clearTimeout(waitingTimeoutRef.current);\n                    waitingTimeoutRef.current = null;\n                }\n                prevAgentMessageCountRef.current = currentAgentMessageCount;\n            } else if (currentAgentMessageCount !== prevAgentMessageCountRef.current) {\n                prevAgentMessageCountRef.current = currentAgentMessageCount;\n            }\n        }\n    }, [\n        timeline === null || timeline === void 0 ? void 0 : (_timeline_timeline = timeline.timeline) === null || _timeline_timeline === void 0 ? void 0 : _timeline_timeline.length,\n        timeline === null || timeline === void 0 ? void 0 : timeline.timeline,\n        isWaitingForAgent\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage(e);\n        }\n    };\n    const handleSendMessage = (e)=>{\n        e.preventDefault();\n        if (!newMessage.trim() || !conversation) return;\n        // Additional validation\n        if (!conversation.uuid) {\n            console.error(\"Conversation UUID is missing\");\n            return;\n        }\n        // Require customer name\n        if (!customerName.trim()) {\n            setError(\"Please enter your name to send a message\");\n            return;\n        }\n        try {\n            // Create customer message\n            const messageData = {\n                content: \"[\".concat(customerName.trim(), \"]: \").concat(newMessage.trim()),\n                createdBy: \"1\",\n                agentId: \"customer-message\"\n            };\n            appendMessage(messageData);\n        } catch (error) {\n            console.error(\"Failed to prepare message:\", error);\n        }\n    };\n    // Customer name setup modal\n    if (!isCustomerNameSet && conversation) {\n        var _conversation_store1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-slate-950 relative overflow-hidden flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"Join Live Chat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Join the live customer conversation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0\",\n                            style: {\n                                backgroundImage: \"\\n              radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\\n            \"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 max-w-md mx-auto px-6 py-8 bg-slate-900/50 border border-cyan-500/20 rounded-xl backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400 mb-2\",\n                                    children: \"Join Live Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-cyan-300/80\",\n                                    children: [\n                                        (conversation === null || conversation === void 0 ? void 0 : (_conversation_store1 = conversation.store) === null || _conversation_store1 === void 0 ? void 0 : _conversation_store1.name) || \"Store\",\n                                        \" - Live Conversation\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: (e)=>{\n                                e.preventDefault();\n                                if (customerName.trim()) {\n                                    setIsCustomerNameSet(true);\n                                }\n                            },\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"customerName\",\n                                            className: \"block text-sm font-medium text-cyan-300 mb-2\",\n                                            children: \"Your Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"customerName\",\n                                            value: customerName,\n                                            onChange: (e)=>setCustomerName(e.target.value),\n                                            placeholder: \"Enter your name\",\n                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 text-cyan-100 placeholder-cyan-400/50\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !customerName.trim(),\n                                    className: \"w-full px-6 py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95\",\n                                    children: \"Join Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            fontFamily: 'system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif'\n        },\n        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"min-h-screen bg-slate-950 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        className: \"jsx-c1d451e38a0b1618\",\n                        children: (conversation === null || conversation === void 0 ? void 0 : conversation.title) || \"Live Chat\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Live customer conversation\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 opacity-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n            radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\\n            radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\\n            radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\\n          \"\n                        },\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n            linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%),\\n            linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%)\\n          \",\n                            backgroundSize: \"50px 50px\"\n                        },\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 opacity-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"relative z-10 max-w-4xl mx-auto px-4 py-4 md:py-6 h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"mb-4 md:mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontFamily: \"Orbitron, monospace\"\n                                },\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400\",\n                                children: (conversation === null || conversation === void 0 ? void 0 : (_conversation_store = conversation.store) === null || _conversation_store === void 0 ? void 0 : _conversation_store.name) || \"LIVE CHAT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontFamily: \"Exo 2, sans-serif\"\n                                },\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-sm md:text-base text-cyan-300/80 mt-1\",\n                                children: [\n                                    \"Live Chat - \",\n                                    customerName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"bg-red-500/10 border border-red-500/30 text-red-300 px-4 py-3 rounded-lg backdrop-blur-sm mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-5 h-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\",\n                                        className: \"jsx-c1d451e38a0b1618\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"relative w-16 h-16 mx-auto mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 border-4 border-cyan-400/20 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 border-4 border-transparent border-t-cyan-400 rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontFamily: \"Exo 2, sans-serif\"\n                                    },\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-cyan-300/80\",\n                                    children: \"Initializing Neural Interface...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this),\n                    conversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 flex flex-col bg-slate-900/30 border border-cyan-500/20 rounded-xl backdrop-blur-sm overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 overflow-y-auto p-3 md:p-4 space-y-3 md:space-y-4 scrollbar-thin scrollbar-thumb-cyan-500/30 scrollbar-track-transparent\",\n                                children: [\n                                    ((timeline === null || timeline === void 0 ? void 0 : timeline.timeline) || []).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            item: item\n                                        }, \"\".concat(item.type, \"-\").concat(item.id), false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)),\n                                    (conversation === null || conversation === void 0 ? void 0 : conversation.notificationStatus) && conversation.notificationStatus !== \"None\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-3 md:gap-4 justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerIcon, {\n                                                    name: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"max-w-[85%] md:max-w-[70%]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontFamily: \"Exo 2, sans-serif\"\n                                                                },\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-xs opacity-80\",\n                                                                children: conversation.notificationStatus === \"AgentIsThinking\" ? \"AI Agent is thinking...\" : \"AI Agent is generating response...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"inline-flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/80 rounded-full animate-bounce\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/60 rounded-full animate-bounce [animation-delay:120ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/40 rounded-full animate-bounce [animation-delay:240ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this),\n                                    isWaitingForAgent && (!(conversation === null || conversation === void 0 ? void 0 : conversation.notificationStatus) || conversation.notificationStatus === \"None\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-3 md:gap-4 justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerIcon, {\n                                                    name: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"max-w-[85%] md:max-w-[70%]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontFamily: \"Exo 2, sans-serif\"\n                                                                },\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-xs opacity-80\",\n                                                                children: \"AI Agent is preparing response...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"inline-flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/80 rounded-full animate-bounce\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/60 rounded-full animate-bounce [animation-delay:120ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/40 rounded-full animate-bounce [animation-delay:240ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-c1d451e38a0b1618\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSendMessage,\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"border-t border-cyan-500/20 p-3 md:p-4 bg-slate-900/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-2 md:gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Enter your message...\",\n                                                    style: {\n                                                        fontFamily: \"Exo 2, sans-serif\"\n                                                    },\n                                                    rows: 1,\n                                                    disabled: isLoading,\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-full px-3 md:px-4 py-2 md:py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 resize-none text-cyan-100 placeholder-cyan-400/50 text-sm md:text-base transition-all duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-4 h-4 border-2 border-cyan-400/30 border-t-cyan-400 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: !newMessage.trim() || isLoading || !customerName.trim(),\n                                            style: {\n                                                fontFamily: \"Exo 2, sans-serif\"\n                                            },\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-4 md:px-6 py-2 md:py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-cyan-500/25 text-sm md:text-base\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-4 h-4 md:w-5 md:h-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\",\n                                                    className: \"jsx-c1d451e38a0b1618\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, this) : null\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"c1d451e38a0b1618\",\n                children: \".scrollbar-thin{scrollbar-width:thin}.scrollbar-thumb-cyan-500\\\\\\\\/30::-webkit-scrollbar-thumb {background-color:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}.scrollbar-track-transparent::-webkit-scrollbar-track{background-color:transparent}::-webkit-scrollbar{width:6px}::-webkit-scrollbar-track{background:transparent}::-webkit-scrollbar-thumb{background:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}::-webkit-scrollbar-thumb:hover{background:rgba(6,182,212,.5)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicLiveConversationPage, \"J/sSbsgZA8B655SmgwoU2VYYk98=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c1 = PublicLiveConversationPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"CustomerIcon\");\n$RefreshReg$(_c1, \"PublicLiveConversationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/live/[uuid].tsx\n"));

/***/ })

});