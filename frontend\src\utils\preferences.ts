export type SupportedLanguage = string;
export type SupportedCurrency = string;
export type CountryCode = string;

export const LANGUAGE_OPTIONS: SupportedLanguage[] = [
	'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko', 'ar', 'hi', 'bn', 'pa', 'ur', 'fa', 'tr', 'nl', 'sv', 'no', 'da', 'fi', 'pl', 'cs', 'sk', 'hu', 'ro', 'bg', 'hr', 'sl'
];

export const CURRENCY_OPTIONS: SupportedCurrency[] = [
	'USD','EUR','DZD','GBP','CAD','AUD','NZD','JPY','CNY','HKD','SGD','INR','BRL','MXN','ZAR','SEK','NOK','DKK','CHF','PLN','CZK','HUF','ILS','TRY','AED','SAR','QAR','KWD','BHD','OMR','EGP','NGN','KES','ARS','CLP','COP','PEN','UYU','KRW','THB','MYR','PHP','IDR'
];

export const CURRENCY_SYMBOLS: Record<SupportedCurrency, string> = {
	'USD': '$',
	'EUR': '€',
	'DZD': 'DZD',
	'GBP': '£',
	'CAD': 'C$',
	'AUD': 'A$',
	'NZD': 'NZ$',
	'JPY': '¥',
	'CNY': '¥',
	'HKD': 'HK$',
	'SGD': 'S$',
	'INR': '₹',
	'BRL': 'R$',
	'MXN': '$',
	'ZAR': 'R',
	'SEK': 'kr',
	'NOK': 'kr',
	'DKK': 'kr',
	'CHF': 'CHF',
	'PLN': 'PLN',
	'CZK': 'CZK',
	'HUF': 'HUF',
	'ILS': '₪',
	'TRY': '₺',
	'AED': 'AED',
	'SAR': 'SAR',
	'QAR': 'QAR',
	'KWD': 'KWD',
	'BHD': 'BHD',
	'OMR': 'OMR',
	'EGP': 'EGP',
	'NGN': 'NGN',
	'KES': 'KES',
	'ARS': 'ARS',
	'CLP': 'CLP',
	'COP': 'COP',
	'PEN': 'PEN',
	'UYU': 'UYU',
	'KRW': '₩',
	'THB': '฿',
	'MYR': 'RM',
	'PHP': '₱',
	'IDR': 'IDR',
};

export const DEFAULT_CURRENCY_BY_COUNTRY: Record<string, SupportedCurrency> = {
	US: 'USD',
	GB: 'GBP',
	CA: 'CAD',
	AU: 'AUD',
	NZ: 'NZD',
	JP: 'JPY',
	CN: 'CNY',
	HK: 'HKD',
	SG: 'SGD',
	IN: 'INR',
	BR: 'BRL',
	MX: 'MXN',
	ZA: 'ZAR',
	SE: 'SEK',
	NO: 'NOK',
	DK: 'DKK',
	CH: 'CHF',
	PL: 'PLN',
	CZ: 'CZK',
	HU: 'HUF',
	IL: 'ILS',
	TR: 'TRY',
	AE: 'AED',
	SA: 'SAR',
	DZ: 'DZD',
	DE: 'EUR',
	FR: 'EUR',
	ES: 'EUR',
	IT: 'EUR',
	PT: 'EUR',
};

export function getBrowserPreferenceDefaults(): { language: SupportedLanguage; country: CountryCode; currency: SupportedCurrency } {
	let language: SupportedLanguage = 'en';
	let country: CountryCode = 'US';
	try {
		if (typeof navigator !== 'undefined') {
			const browserLang = navigator.language || 'en';
			// Convert browser language to supported format (e.g., 'en-US' -> 'en')
			language = browserLang.split('-')[0] as SupportedLanguage;
			if (browserLang.split('-').length > 1) country = browserLang.split('-')[1].toUpperCase();
		}
	} catch {}
	const currency = DEFAULT_CURRENCY_BY_COUNTRY[country] || 'USD';
	return { language, country, currency };
}

export function formatCurrency(amount: number, currency: SupportedCurrency = 'USD'): string {
	const symbol = CURRENCY_SYMBOLS[currency] || currency;

	return `${symbol}${amount.toLocaleString('en-US', {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	})}`;
}

// Simple translation system for supported languages
export const TRANSLATIONS = {
	'en': {
		'storeDirectory': 'Store Directory',
		'browseStores': 'Browse available stores and their products',
		'visitStore': 'Visit Store',
		'shoppingCart': 'Shopping Cart',
		'addToCart': 'Add to Cart',
		'proceedToCheckout': 'Proceed to Checkout',
		'loading': 'Loading...',
		'noProducts': 'No products available in this store.',
		'howItWorks': 'How it works',
		'browseAndShop': 'Browse stores, view their products, and add items to your cart. No account required - start shopping immediately!'
	},
	'fr': {
		'storeDirectory': 'Répertoire des Magasins',
		'browseStores': 'Parcourez les magasins disponibles et leurs produits',
		'visitStore': 'Visiter le Magasin',
		'shoppingCart': 'Panier d\'Achats',
		'addToCart': 'Ajouter au Panier',
		'proceedToCheckout': 'Procéder au Paiement',
		'loading': 'Chargement...',
		'noProducts': 'Aucun produit disponible dans ce magasin.',
		'howItWorks': 'Comment ça marche',
		'browseAndShop': 'Parcourez les magasins, consultez leurs produits et ajoutez des articles à votre panier. Aucun compte requis - commencez à faire vos achats immédiatement !'
	},
	'ar': {
		'storeDirectory': 'دليل المتاجر',
		'browseStores': 'تصفح المتاجر المتاحة ومنتجاتها',
		'visitStore': 'زيارة المتجر',
		'shoppingCart': 'سلة التسوق',
		'addToCart': 'إضافة إلى السلة',
		'proceedToCheckout': 'المتابعة للدفع',
		'loading': 'جاري التحميل...',
		'noProducts': 'لا توجد منتجات متاحة في هذا المتجر.',
		'howItWorks': 'كيف يعمل',
		'browseAndShop': 'تصفح المتاجر، عرض منتجاتها، وإضافة العناصر إلى سلة التسوق الخاصة بك. لا حاجة لحساب - ابدأ التسوق فوراً!'
	}
};

export function getTranslation(key: keyof typeof TRANSLATIONS['en'], language: SupportedLanguage = 'en'): string {
	        return TRANSLATIONS[language as keyof typeof TRANSLATIONS]?.[key] || TRANSLATIONS['en'][key] || key;
}

// Get store currency - currently defaults to USD, but can be easily updated
// to use store-specific currency when that field is added to the Store model
export function getStoreCurrency(store?: any): SupportedCurrency {
	// TODO: When store.currency field is added, use: return store?.currency || 'USD';
	return 'USD';
}


