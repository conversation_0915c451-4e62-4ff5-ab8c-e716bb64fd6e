import { ToolFunction, ToolSpec } from './types';
import { addToolMetadata } from './schema';

/**
 * Decorator interface for tool function configuration
 */
export interface ToolConfig {
  name?: string;
  description?: string;
  parameterTypes?: Record<string, any>;
  requiredParams?: string[];
}

/**
 * Decorator function to mark a function as an LLM-callable tool.
 * 
 * This function adds metadata to the function that allows it to be
 * exposed to the LLM as a tool with proper schema information.
 * 
 * @param config - Configuration for the tool including description and parameter types
 * @returns Decorator function
 */
export function functionTool(config: ToolConfig = {}) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    if (typeof originalMethod !== 'function') {
      throw new Error('functionTool can only be applied to methods');
    }
    
    // Add tool metadata
    const toolFunction = addToolMetadata(
      originalMethod,
      config.description,
      config.parameterTypes,
      config.requiredParams,
      config.name
    );
    
    descriptor.value = toolFunction;
    return descriptor;
  };
}

/**
 * Function to manually mark a function as a tool (alternative to decorator)
 * 
 * @param func - The function to be marked as a tool
 * @param config - Configuration for the tool
 * @returns The original function with added tool metadata
 */
export function createTool(func: ToolFunction, config: ToolConfig = {}): ToolFunction {
  const description = config.description || func.name || 'Tool function';
  
  return addToolMetadata(
    func,
    description,
    config.parameterTypes,
    config.requiredParams,
    config.name
  );
}

/**
 * Extract tool specifications from a function with tool metadata
 */
export function getToolSpec(func: ToolFunction): ToolSpec | null {
  return (func as any)._toolSpec || null;
}

/**
 * Check if a function has tool metadata
 */
export function isToolFunction(func: ToolFunction): boolean {
  return !!(func as any)._toolSpec;
}
