import { fetchWithCredentials } from './auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

// Helper function to handle API responses
async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }
  return response.json();
}

// Generic API client class
export class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  // Generic GET request
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(`${this.baseUrl}${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }

    console.log('API GET request to:', url.toString());

    const response = await fetchWithCredentials(url.toString(), {
      method: 'GET',
    });

    return handleResponse<T>(response);
  }

  // Generic POST request
  async post<T>(endpoint: string, data?: any): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    console.log('🔧 API POST request to:', url);
    console.log('🔧 API POST data:', data);
    console.log('🔧 API base URL:', this.baseUrl);
    
    try {
      const response = await fetchWithCredentials(url, {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      });

      console.log('🔧 API POST response status:', response.status);
      console.log('🔧 API POST response headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API POST error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }
      
      return handleResponse<T>(response);
    } catch (error) {
      console.error('❌ API POST fetch error:', error);
      throw error;
    }
  }

  // Generic PUT request
  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetchWithCredentials(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });

    return handleResponse<T>(response);
  }

  // Generic DELETE request
  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetchWithCredentials(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
    });

    return handleResponse<T>(response);
  }
}

// Create a default API client instance
export const apiClient = new ApiClient();

// Store API methods
export const storeApi = {
  getAll: (params?: { page?: number; limit?: number }) =>
    apiClient.get('/stores', params),
  
  getById: (id: string | number) =>
    apiClient.get(`/stores/${id}`),
  
  getByUserId: (userId: string | number, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/stores/user/${userId}`, params),
  
  getByUuid: (uuid: string) =>
    apiClient.get(`/stores/uuid/${uuid}`),
  
  create: (data: { name: string; description?: string; currency: string; preferredLanguage?: string; userId: string | number }) =>
    apiClient.post('/stores', data),
  
  update: (id: string | number, data: { name: string; description?: string; currency: string; preferredLanguage?: string }) =>
    apiClient.put(`/stores/${id}`, data),
  
  delete: (id: string | number) =>
    apiClient.delete(`/stores/${id}`),
};

// Product API methods
export const productApi = {
  getAll: (params?: { page?: number; limit?: number; storeId?: string | number }) =>
    apiClient.get('/products', params),
  
  getById: (id: string | number) =>
    apiClient.get(`/products/${id}`),
  
  getByStoreId: (storeId: string | number, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/products/store/${storeId}`, params),
  
  getByStoreUuid: (storeUuid: string, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/products/store/uuid/${storeUuid}`, params),
  
  create: (data: { name: string; description?: string; price: number; sku?: string; storeId: string | number; imageUrl?: string; userId: string | number }) =>
    apiClient.post('/products', data),
  
  update: (id: string | number, data: { name: string; description?: string; price: number; sku?: string; imageUrl?: string }) =>
    apiClient.put(`/products/${id}`, data),
  
  delete: (id: string | number) =>
    apiClient.delete(`/products/${id}`),
};

// Customer API methods
export const customerApi = {
  getAll: (params?: { page?: number; limit?: number; storeId?: string | number }) =>
    apiClient.get('/customers', params),
  
  getById: (id: string | number) =>
    apiClient.get(`/customers/${id}`),
  
  getByStoreId: (storeId: string | number, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/customers/store/${storeId}`, params),
  
  create: (data: { name: string; email?: string; phone?: string; address?: string; storeId: string | number; userId: string | number }) =>
    apiClient.post('/customers', data),
  
  update: (id: string | number, data: { name: string; email?: string; phone?: string; address?: string }) =>
    apiClient.put(`/customers/${id}`, data),
  
  delete: (id: string | number) =>
    apiClient.delete(`/customers/${id}`),
};

// User API methods
export const userApi = {
  getAll: (params?: { page?: number; limit?: number }) =>
    apiClient.get('/users', params),
  
  getById: (id: string | number) =>
    apiClient.get(`/users/${id}`),
  
  create: (data: { email: string; name?: string; countryCode?: string; preferredLanguage?: string; preferredCurrency?: string }) =>
    apiClient.post('/users', data),
  
  update: (id: string | number, data: { name?: string; preferredLanguage?: string; preferredCurrency?: string; updatedBy: string | number }) =>
    apiClient.put(`/users/${id}`, data),
  
  delete: (id: string | number) =>
    apiClient.delete(`/users/${id}`),
};

// Order API methods
export const orderApi = {
  getAll: (params?: { page?: number; limit?: number; storeId?: string | number }) =>
    apiClient.get('/orders', params),
  
  getById: (id: string | number) =>
    apiClient.get(`/orders/${id}`),
  
  getByOrderNumber: (orderNumber: string) =>
    apiClient.get(`/orders/order-number/${orderNumber}`),
  
  getByStoreId: (storeId: string | number, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/orders/store/${storeId}`, params),
  
  filter: (data: { page?: number; limit?: number; storeId: string | number; status?: string; customerId?: string | number; startDate?: string; endDate?: string }) =>
    apiClient.post('/orders/filter', data),
  
  create: (data: { orderNumber: string; customerId: string | number; storeId: string | number; status?: string; orderItems: any[]; totalAmount: number }) =>
    apiClient.post('/orders', data),
  
  update: (id: string | number, data: { status: string; totalAmount?: number; userId: string | number }) =>
    apiClient.put(`/orders/${id}`, data),
  
  delete: (id: string | number) =>
    apiClient.delete(`/orders/${id}`),
};

// Conversation API methods (using existing endpoints)
export const conversationApi = {
  getAll: (params?: { page?: number; limit?: number }) =>
    apiClient.get('/conversations', params),
  
  getById: (id: string | number) =>
    apiClient.get(`/conversations/${id}`),
  
  getByUuid: (uuid: string) =>
    apiClient.get(`/conversations/uuid/${uuid}`),
  
  getByStoreId: (storeId: string | number, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/conversations/store/${storeId}`, params),
  
  getByUserId: (userId: string | number, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/conversations/user/${userId}`, params),
  
  create: (data: { title: string; userId: string | number; storeId: string | number; createdBy: string | number }) =>
    apiClient.post('/conversations', data),
  
  getTimeline: (id: string | number, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/conversations/${id}/timeline`, params),
  
  getUnifiedTimeline: (id: string | number, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/conversations/${id}/unified-timeline`, params),
  
  getUnifiedTimelineByUuid: (uuid: string, params?: { page?: number; limit?: number }) =>
    apiClient.get(`/conversations/uuid/${uuid}/unified-timeline`, params),
  
  appendMessage: (id: string | number, data: { content: string; senderType: string; senderId?: string | number }) =>
    apiClient.post(`/conversations/${id}/messages`, data),
  
  appendMessageByUuid: (uuid: string, data: { content: string; createdBy: string; agentId?: string; userId?: string; customerId?: string; imageUrl?: string; videoUrl?: string; attachmentUrl?: string; attachmentType?: string; cost?: number; executionTime?: number; inputTokens?: number; outputTokens?: number }) =>
    apiClient.post(`/conversations/uuid/${uuid}/messages`, data),
};

// Image API methods
export const imageApi = {
  upload: (file: File): Promise<{ filename: string; url: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    
    return fetchWithCredentials('/images', {
      method: 'POST',
      body: formData,
    }).then(response => {
      if (!response.ok) {
        return response.json().then(errorData => {
          throw new Error(errorData.error || 'Failed to upload image');
        });
      }
      return response.json();
    });
  },
};

// Agent API methods (using existing endpoints)
export const agentApi = {
  getAll: (params?: { page?: number; limit?: number }) =>
    apiClient.get('/agents', params),
  
  getById: (id: string | number) =>
    apiClient.get(`/agents/${id}`),
  
  create: (data: any) =>
    apiClient.post('/agents', data),
  
  update: (id: string | number, data: any) =>
    apiClient.put(`/agents/${id}`, data),
  
  delete: (id: string | number) =>
    apiClient.delete(`/agents/${id}`),
  
  generateMessage: (data: any) =>
    apiClient.post('/agents/runtime/generate-message', data),
};
