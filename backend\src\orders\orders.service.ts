import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager, Between, ILike, In } from 'typeorm';
import { Order, OrderStatus, OrderPriority } from './order.entity';
import { OrderItem } from './order-item.entity';
import { Customer } from '../customers/customer.entity';
import { Store } from '../stores/store.entity';
import { User } from '../users/user.entity';

export enum OrderStatusService {
	DRAFT = 'draft',
	PENDING = 'pending',
	CONFIRMED = 'confirmed',
	PROCESSING = 'processing',
	SHIPPED = 'shipped',
	DELIVERED = 'delivered',
	CANCELLED = 'cancelled',
	RETURNED = 'returned',
}

export enum OrderPriorityService {
	LOW = 'low',
	NORMAL = 'normal',
	HIGH = 'high',
	URGENT = 'urgent',
}

export type CreateOrderItemInput = {
	productId: string;
	productName: string;
	quantity: number;
	unitPrice: number;
	taxAmount?: number;
};

export type CreateOrderInput = {
	status?: OrderStatusService;
	priority?: OrderPriorityService;
	useTax?: boolean;
	taxRate?: number;
	orderDate?: Date;
	expectedDeliveryDate?: Date | null;
	preferredDeliveryLocation?: string | null;
	userId: string;
	storeId: string;
	customerId: string;
	customerPhone?: string;
	customerEmail?: string;
	customerName?: string;
	customerAddress?: string;
	createdBy: string;
	items: CreateOrderItemInput[];
};

export type UpdateOrderInput = {
	id: string;
	status?: OrderStatusService;
	priority?: OrderPriorityService;
	useTax?: boolean;
	taxRate?: number;
	orderDate?: Date;
	expectedDeliveryDate?: Date | null;
	preferredDeliveryLocation?: string | null;
	cancellationReason?: string | null;
	updatedBy: string;
	items?: CreateOrderItemInput[];
};

export type OrderFilter = {
	storeId?: string;
	userId?: string;
	customerId?: string;
	status?: OrderStatusService;
	priority?: OrderPriorityService;
};

export function computeOrderTotals(
	items: Array<{ quantity: number; unitPrice: number; taxAmount?: number }>,
	useTax: boolean,
	taxRate: number,
) {
	const subtotal = items.reduce((sum, it) => sum + it.quantity * it.unitPrice, 0);
	const taxFromRate = useTax ? subtotal * taxRate : 0;
	const taxFromLines = items.reduce((sum, it) => sum + (it.taxAmount ?? 0), 0);
	const taxAmount = Math.max(taxFromRate, taxFromLines);
	const total = subtotal + taxAmount;
	return { subtotal, taxAmount, total };
}

async function generateNextOrderNumber(tx: EntityManager) {
	const now = new Date();
	const yyyy = now.getFullYear();
	const mm = String(now.getMonth() + 1).padStart(2, '0');
	const dd = String(now.getDate()).padStart(2, '0');
	const datePart = `${yyyy}${mm}${dd}`;
	const prefix = `ORD-${datePart}-`;
	const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);
	const startOfNextDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0, 0);
	
	const todayCount = await tx.getRepository(Order).count({
		where: {
			createdAt: Between(startOfDay, startOfNextDay)
		}
	});
	
	return `${prefix}${String(todayCount + 1).padStart(4, '0')}`;
}

@Injectable()
export class OrdersService {
	constructor(
		@InjectRepository(Order)
		private orderRepository: Repository<Order>,
		@InjectRepository(OrderItem)
		private orderItemRepository: Repository<OrderItem>,
	) {}

	async createOrder(input: CreateOrderInput): Promise<Order> {
		return this.orderRepository.manager.transaction(async (tx) => {
			const orderNumber = await generateNextOrderNumber(tx);
			const { subtotal, taxAmount, total } = computeOrderTotals(
				input.items,
				input.useTax || false,
				input.taxRate || 0
			);

			const order = new Order();
			order.orderNumber = orderNumber;
			order.status = input.status || OrderStatusService.PENDING as any;
			order.priority = input.priority || OrderPriorityService.NORMAL as any;
			order.useTax = input.useTax || false;
			order.taxRate = input.taxRate || 0;
			order.orderDate = input.orderDate || new Date();
			order.expectedDeliveryDate = input.expectedDeliveryDate;
			order.preferredDeliveryLocation = input.preferredDeliveryLocation;
			order.subtotal = subtotal;
			order.taxAmount = taxAmount;
			order.total = total;
			order.userId = input.userId;
			order.storeId = input.storeId;
			order.customerId = input.customerId;
			order.createdBy = input.createdBy;

			const savedOrder = await tx.save(order);

			const orderItems = input.items.map(item => {
				const lineTotal = item.quantity * item.unitPrice;
				const orderItem = new OrderItem();
				orderItem.orderId = savedOrder.id;
				orderItem.productId = item.productId;
				orderItem.productName = item.productName;
				orderItem.quantity = item.quantity;
				orderItem.unitPrice = item.unitPrice;
				orderItem.lineTotal = lineTotal;
				orderItem.taxAmount = item.taxAmount || 0;
				orderItem.createdBy = input.createdBy;
				return orderItem;
			});

			await tx.save(orderItems);

			return this.findById(savedOrder.id);
		});
	}

	async updateOrder(input: UpdateOrderInput): Promise<Order> {
		return this.orderRepository.manager.transaction(async (tx) => {
			const order = await tx.findOne(Order, { where: { id: input.id } });
			if (!order) {
				throw new Error('Order not found');
			}

			const updateData: Partial<Order> = {
				updatedBy: input.updatedBy,
			};

			if (input.status !== undefined) updateData.status = input.status as any;
			if (input.priority !== undefined) updateData.priority = input.priority as any;
			if (input.useTax !== undefined) updateData.useTax = input.useTax;
			if (input.taxRate !== undefined) updateData.taxRate = input.taxRate;
			if (input.orderDate !== undefined) updateData.orderDate = input.orderDate;
			if (input.expectedDeliveryDate !== undefined) updateData.expectedDeliveryDate = input.expectedDeliveryDate;
			if (input.preferredDeliveryLocation !== undefined) updateData.preferredDeliveryLocation = input.preferredDeliveryLocation;
			if (input.cancellationReason !== undefined) updateData.cancellationReason = input.cancellationReason;

			if (input.items) {
				// Remove existing items
				await tx.delete(OrderItem, { orderId: input.id });

				// Create new items
				const orderItems = input.items.map(item => {
					const lineTotal = item.quantity * item.unitPrice;
					const orderItem = new OrderItem();
					orderItem.orderId = input.id;
					orderItem.productId = item.productId;
					orderItem.productName = item.productName;
					orderItem.quantity = item.quantity;
					orderItem.unitPrice = item.unitPrice;
					orderItem.lineTotal = lineTotal;
					orderItem.taxAmount = item.taxAmount || 0;
					orderItem.createdBy = input.updatedBy;
					return orderItem;
				});

				await tx.save(orderItems);

				// Recalculate totals
				const { subtotal, taxAmount, total } = computeOrderTotals(
					input.items,
					order.useTax,
					order.taxRate
				);
				updateData.subtotal = subtotal;
				updateData.taxAmount = taxAmount;
				updateData.total = total;
			}

			await tx.update(Order, { id: input.id }, updateData);

			return this.findById(input.id);
		});
	}

	async findById(id: string): Promise<Order> {
		return this.orderRepository.findOne({
			where: { id },
			relations: ['customer', 'store', 'orderItems', 'orderItems.product'],
		});
	}

	async findByOrderNumber(orderNumber: string): Promise<Order> {
		return this.orderRepository.findOne({
			where: { orderNumber },
			relations: ['customer', 'store', 'orderItems', 'orderItems.product'],
		});
	}

	async findOrders(filter: OrderFilter, page: number = 1, limit: number = 20): Promise<{ orders: Order[]; total: number }> {
		const where: any = { isDeleted: false };

		if (filter.storeId) where.storeId = filter.storeId;
		if (filter.userId) where.userId = filter.userId;
		if (filter.customerId) where.customerId = filter.customerId;
		if (filter.status) where.status = filter.status;
		if (filter.priority) where.priority = filter.priority;

		const [orders, total] = await this.orderRepository.findAndCount({
			where,
			relations: ['customer', 'store', 'orderItems'],
			skip: (page - 1) * limit,
			take: limit,
			order: { createdAt: 'DESC' },
		});

		return { orders, total };
	}

	async deleteOrder(id: string, userId: string): Promise<void> {
		await this.orderRepository.update(id, {
			isDeleted: true,
			updatedBy: userId,
		});
	}

	async getOrderStats(storeId: string, startDate?: Date, endDate?: Date): Promise<{
		totalOrders: number;
		totalRevenue: number;
		averageOrderValue: number;
		ordersByStatus: Record<string, number>;
	}> {
		const where: any = { storeId, isDeleted: false };

		if (startDate || endDate) {
			where.createdAt = {};
			if (startDate) where.createdAt.gte = startDate;
			if (endDate) where.createdAt.lte = endDate;
		}

		const orders = await this.orderRepository.find({ where });

		const totalOrders = orders.length;
		const totalRevenue = orders.reduce((sum, order) => sum + Number(order.total), 0);
		const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

		const ordersByStatus = orders.reduce((acc, order) => {
			acc[order.status] = (acc[order.status] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);

		return {
			totalOrders,
			totalRevenue,
			averageOrderValue,
			ordersByStatus,
		};
	}
}
