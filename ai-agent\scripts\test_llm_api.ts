import { LlmApi } from '../src/llm-api';
import { Agent } from '../src/agent';
import { setLlmProviderFromEnvironmentVariables } from '../src/llm-provider';
import { createTool } from '../src/tools';
import { Message } from '../src/types';

// Mock tools for testing
const mockGetStoreUrl = createTool(
  async (): Promise<string> => {
    return 'http://localhost:3000';
  },
  {
    name: 'getStoreUrl',
    description: 'Get the store URL for the current store',
    parameterTypes: {},
    requiredParams: []
  }
);

const mockPlaceOrder = createTool(
  async (productName: string, quantity: number, customerName: string, phoneNumber: string, location: string): Promise<any> => {
    const orderId = `ORD-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${Math.floor(Math.random() * 1000).toString().padStart(4, '0')}`;
    return {
      success: true,
      orderId,
      message: `Order placed successfully! Order ID: ${orderId}`,
      deliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
      deliveryTime: '09:00 AM',
      deliveryAddress: location,
      deliveryPhone: phoneNumber,
      trackingUrl: `http://localhost:3000/track/${orderId}`
    };
  },
  {
    name: 'placeOrder',
    description: 'Place an order for a customer',
    parameterTypes: {
      productName: 'string',
      quantity: 'number',
      customerName: 'string',
      phoneNumber: 'string',
      location: 'string'
    },
    requiredParams: ['productName', 'quantity', 'customerName', 'phoneNumber', 'location']
  }
);

const mockSearchCustomers = createTool(
  async (phoneNumber?: string, name?: string): Promise<any[]> => {
    // Mock customer data
    if (phoneNumber === '0562408768' || name === 'Mahdi') {
      return [{
        id: 'cust-001',
        name: 'Mahdi',
        phoneNumber: '0562408768',
        email: '<EMAIL>',
        location: 'In Saoula'
      }];
    }
    return [];
  },
  {
    name: 'searchCustomers',
    description: 'Search for customers by phone number or name',
    parameterTypes: {
      phoneNumber: 'string',
      name: 'string'
    },
    requiredParams: []
  }
);

const mockUpdateCustomer = createTool(
  async (customerId: string, updates: any): Promise<any> => {
    return {
      success: true,
      message: 'Customer updated successfully',
      customerId,
      updates
    };
  },
  {
    name: 'updateCustomer',
    description: 'Update customer information',
    parameterTypes: {
      customerId: 'string',
      updates: 'object'
    },
    requiredParams: ['customerId', 'updates']
  }
);

const mockCheckOrderUnderway = createTool(
  async (orderNumber: string): Promise<any> => {
    return {
      orderId: orderNumber,
      status: 'In Progress',
      deliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
      deliveryTime: '09:00 AM',
      deliveryAddress: 'In Saoula',
      deliveryPhone: '0562408768',
      trackingUrl: `http://localhost:3000/track/${orderNumber}`
    };
  },
  {
    name: 'checkOrderUnderway',
    description: 'Check the status of an order',
    parameterTypes: {
      orderNumber: 'string'
    },
    requiredParams: ['orderNumber']
  }
);

const mockUpdateOrder = createTool(
  async (orderNumber: string, updates: any): Promise<any> => {
    return {
      success: true,
      message: 'Order updated successfully',
      orderNumber,
      updates
    };
  },
  {
    name: 'updateOrder',
    description: 'Update order information',
    parameterTypes: {
      orderNumber: 'string',
      updates: 'object'
    },
    requiredParams: ['orderNumber', 'updates']
  }
);

async function testLlmApi() {
  console.log('🚀 Starting LLM API Test...\n');

  try {
    // Set up LLM provider from environment variables
    setLlmProviderFromEnvironmentVariables();
    
    // Check if API key is set
    if (!process.env.API_KEY) {
      console.error('❌ API_KEY environment variable is not set!');
      console.log('Please set the API_KEY environment variable before running this test.');
      console.log('You can also set BASE_URL and MODEL if needed.');
      return;
    }

    // Create a test agent with the sales assistant instructions
    const agent = new Agent(
      'Sales Assistant',
      `# Role
You are a helpful and friendly sales assistant bot. You will be talking only to customers. Your main responsibilities are:
1. Greet customers warmly and introduce yourself
2. Explain your capabilities and how you can help them
3. Present any previous orders that the client has made
4. Present product information in an engaging way
5. Help customers make purchasing decisions
6. Process orders efficiently
7. You only talk to customers

When interacting with customers:
- Be professional but conversational
- Focus on understanding client needs
- Provide clear and accurate product information
- Be transparent about pricing and delivery
- Handle objections professionally
- Always maintain a helpful and positive tone

# Additional Notes
Remember to:
- Ask clarifying questions when needed
- Provide specific product recommendations based on client needs
- Explain the ordering process clearly
- Confirm order details before processing
- Thank clients for their business
- When you list products, use the following format:
    - <Product Name>
    - <Product Description>
    - <Product Price>
- When you list products, only list a maximum of 20 products at a time
- Only show the product image url when specifically asked
- For more detailed information and to purchase, please visit our store. Use the getStoreUrl tool to get the correct store URL.

# Ordering Process
1. Client provides product details
2. Bot confirms order details and asks for <customer_name> and <location_name> and <phone_number>
3. Client provides <client_name> and <location_name> and <phone number>
4. If order is successful the Bot processes order and sends confirmation with receipt and delivery details to client and message for status of order otherwise will tell the customer there was issue.
5. Bot asks if there is anything else the customer wants to order

# Order Tracking Process
- Client can track the order by asking or when they place an order
- Bot will send a message with the status of the order (Use random values for now, we are in testing phase)
- Format for order status message:
    - Order ID: <order_id>
    - Order Status: <order_status>
    - Order Delivery Date: <order_delivery_date>
    - Order Delivery Time: <order_delivery_time>
    - Order Delivery Location: <order_delivery_address>
    - Order Delivery Phone Number: <order_delivery_phone_number>
    - Order Tracking Url: <order_url>

# Important Notes
- Always respond in a friendly and helpful manner
- Remember to keep the conversation engaging and interesting
- The primary language of communication is French
- Always respond in French
- Use emojis to make the conversation more engaging
- Price currency is DZD
- We dont use emails to send receipts or track orders, we use messages
- Lookup older messages for user information like phone or name or email

# Technical Implementation Details
You are provided the full conversation history as context, including previous tool calls and their results. You can see what tools were previously executed, their inputs, outputs, success status, and execution times. Use this information to make informed decisions about what tools to call next.
When the user asks for products or a product list, answer based on the product list provided in your context. Do not fabricate product data.
Avoid fabricating customer data. If customer details are missing for ordering, ask the user for customer name and phone number and optionally email, then place the order using placeOrder.
NEVER reveal or mention tool names, function names, implementation details, phases, or any backend/system internals to the user. Do not include phrases like "Phase 1", "Phase 2", "Call:", or "Output:" in any user-facing message. Present only a natural, human-friendly response.
If a tool returns an error, summarize the outcome naturally without exposing the tool or error text. Keep answers concise and helpful. Ask clarifying questions if needed.
The customer can check his order using the link http://localhost:3000/track/<order_number> example: http://localhost:3000/track/ORD-20250821-0157. Use the getStoreUrl tool to get the correct store URL.`,
      [
        mockGetStoreUrl,
        mockPlaceOrder,
        mockSearchCustomers,
        mockUpdateCustomer,
        mockCheckOrderUnderway,
        mockUpdateOrder
      ]
    );

    // Test input messages
    const testMessages: Message[] = [
      {
        role: 'system',
        content: '# Available Products\nHere are the products available in our store:\n1. pr123\n   Price: DZD 20'
      },
      {
        role: 'system',
        content: '# Current Customer Information\n\nNo customer information is available for this conversation. You will need to collect customer details when processing orders.'
      },
      {
        role: 'assistant',
        content: 'Bonjour et bienvenue ! 😊 Je suis votre assistant de vente, prêt à vous aider à découvrir nos produits, passer une commande ou répondre à toutes vos questions. Comment puis-je vous aider aujourd\'hui ? Voulez-vous découvrir nos articles, obtenir des recommandations ou procéder à un achat ?'
      },
      {
        role: 'user',
        content: 'Hi\n\nGive me pr123, give me three of that'
      },
      {
        role: 'assistant',
        content: 'Bonjour et merci pour votre demande ! 😊\n\nVous souhaitez commander 3 unités du produit pr123. Avant de procéder à la commande, puis-je avoir votre nom et votre numéro de téléphone, ainsi que la localisation de livraison, s\'il vous plaît ? Cela nous permettra de préparer votre commande et de vous assurer une livraison rapide. Merci de me fournir ces informations !'
      },
      {
        role: 'user',
        content: '0562408768, Mahdi, In Saoula'
      },
      {
        role: 'assistant',
        content: 'Merci pour ces informations, Mahdi ! Pour confirmer, voici le récapitulatif de votre commande :\n\n- Produit : pr123\n- Quantité : 3\n- Nom : Mahdi\n- Numéro de téléphone : 0562408768\n- Lieu de livraison : In Saoula\n\nJe procède à l\'enregistrement de votre commande. Un instant s\'il vous plaît ! 😊'
      },
      {
        role: 'user',
        content: 'try again'
      },
      {
        role: 'user',
        content: 'try again'
      }
    ];

    console.log('📝 Test Input Messages:');
    console.log(JSON.stringify(testMessages, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');

    // Generate LLM response
    console.log('🤖 Generating LLM Response...\n');
    const result = await LlmApi.generateLlmResponse(agent, testMessages);

    console.log('✅ Test Completed Successfully!\n');
    
    // Display results
    console.log('📤 Message Response:');
    console.log(result.messageResponse);
    console.log('\n' + '='.repeat(80) + '\n');

    console.log('🔧 Tool Calls:');
    if (result.toolCalls.length > 0) {
      result.toolCalls.forEach((toolCall, index) => {
        console.log(`\nTool Call ${index + 1}:`);
        console.log(`  Function: ${toolCall.function?.name}`);
        console.log(`  Arguments: ${toolCall.function?.arguments}`);
        console.log(`  ID: ${toolCall.id}`);
      });
    } else {
      console.log('No tool calls were made.');
    }
    console.log('\n' + '='.repeat(80) + '\n');

    console.log('📊 Tool Outputs:');
    if (result.toolOutputs.length > 0) {
      result.toolOutputs.forEach((output, index) => {
        console.log(`\nTool Output ${index + 1}:`);
        console.log(`  Tool Call ID: ${output.tool_call_id}`);
        console.log(`  Content: ${output.content}`);
      });
    } else {
      console.log('No tool outputs.');
    }
    console.log('\n' + '='.repeat(80) + '\n');

    console.log('⏱️ Execution Details:');
    if (result.executionDetails.length > 0) {
      result.executionDetails.forEach((detail, index) => {
        console.log(`\nExecution Detail ${index + 1}:`);
        console.log(`  Tool: ${detail.toolName}`);
        console.log(`  Success: ${detail.success}`);
        console.log(`  Execution Time: ${detail.executionTime}ms`);
        if (detail.errorMessage) {
          console.log(`  Error: ${detail.errorMessage}`);
        }
      });
    } else {
      console.log('No execution details.');
    }
    console.log('\n' + '='.repeat(80) + '\n');

    console.log('💰 Cost Information:');
    console.log(`  Model: ${result.llmCosts.model}`);
    console.log(`  Total Input Tokens: ${result.llmCosts.totalInputTokens}`);
    console.log(`  Total Output Tokens: ${result.llmCosts.totalOutputTokens}`);
    console.log(`  Total Cost: $${result.llmCosts.totalCost.toFixed(6)}`);
    
    if (result.llmCosts.costBreakdown.length > 0) {
      console.log('\n  Cost Breakdown:');
      result.llmCosts.costBreakdown.forEach((call, index) => {
        console.log(`    Call ${index + 1} (${call.callType}):`);
        console.log(`      Input: ${call.inputTokens} tokens`);
        console.log(`      Output: ${call.outputTokens} tokens`);
        console.log(`      Cost: $${call.cost.toFixed(6)}`);
      });
    }

  } catch (error) {
    console.error('❌ Test Failed with Error:');
    console.error(error);
    
    if (error instanceof Error) {
      console.error('\nError Details:');
      console.error(`  Message: ${error.message}`);
      console.error(`  Stack: ${error.stack}`);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testLlmApi().catch(console.error);
}

export { testLlmApi };
