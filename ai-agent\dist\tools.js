"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.functionTool = functionTool;
exports.createTool = createTool;
exports.getToolSpec = getToolSpec;
exports.isToolFunction = isToolFunction;
const schema_1 = require("./schema");
/**
 * Decorator function to mark a function as an LLM-callable tool.
 *
 * This function adds metadata to the function that allows it to be
 * exposed to the LLM as a tool with proper schema information.
 *
 * @param config - Configuration for the tool including description and parameter types
 * @returns Decorator function
 */
function functionTool(config = {}) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        if (typeof originalMethod !== 'function') {
            throw new Error('functionTool can only be applied to methods');
        }
        // Add tool metadata
        const toolFunction = (0, schema_1.addToolMetadata)(originalMethod, config.description, config.parameterTypes, config.requiredParams, config.name);
        descriptor.value = toolFunction;
        return descriptor;
    };
}
/**
 * Function to manually mark a function as a tool (alternative to decorator)
 *
 * @param func - The function to be marked as a tool
 * @param config - Configuration for the tool
 * @returns The original function with added tool metadata
 */
function createTool(func, config = {}) {
    const description = config.description || func.name || 'Tool function';
    return (0, schema_1.addToolMetadata)(func, description, config.parameterTypes, config.requiredParams, config.name);
}
/**
 * Extract tool specifications from a function with tool metadata
 */
function getToolSpec(func) {
    return func._toolSpec || null;
}
/**
 * Check if a function has tool metadata
 */
function isToolFunction(func) {
    return !!func._toolSpec;
}
//# sourceMappingURL=tools.js.map