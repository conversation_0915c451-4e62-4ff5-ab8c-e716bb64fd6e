#!/usr/bin/env tsx

import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { join } from 'path';

// Load environment variables
config();

async function seedDatabase() {
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'teno_store_db',
    synchronize: false,
    logging: true,
    entities: [join(__dirname, '../**/*.entity{.ts,.js}')],
    migrations: [join(__dirname, '../migrations/*{.ts,.js}')],
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    await dataSource.initialize();
    console.log('✅ Database connection established');

    // Check if database is empty
    const userCount = await dataSource.getRepository('User').count();
    
    if (userCount > 0) {
      console.log('ℹ️  Database already contains data, skipping seed');
      return;
    }

    console.log('🌱 Starting database seeding...');

    // Add your seed data here
    // Example:
    // const userRepository = dataSource.getRepository('User');
    // await userRepository.save({
    //   email: '<EMAIL>',
    //   name: 'Admin User',
    //   // ... other fields
    // });

    console.log('✅ Database seeding completed successfully');

    await dataSource.destroy();
    console.log('✅ Database connection closed');
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
}

seedDatabase();
