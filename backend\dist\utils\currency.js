"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mapCountryToCurrency = mapCountryToCurrency;
function mapCountryToCurrency(countryCode) {
    if (!countryCode)
        return null;
    const code = countryCode.toUpperCase();
    const countryToCurrency = {
        US: 'USD',
        CA: 'CAD',
        MX: 'MXN',
        GB: 'GBP',
        IE: 'EUR',
        FR: 'EUR',
        DE: 'EUR',
        ES: 'EUR',
        IT: 'EUR',
        NL: 'EUR',
        BE: 'EUR',
        PT: 'EUR',
        AT: 'EUR',
        FI: 'EUR',
        GR: 'EUR',
        LU: 'EUR',
        MT: 'EUR',
        CY: 'EUR',
        EE: 'EUR',
        LV: 'EUR',
        LT: 'EUR',
        SK: 'EUR',
        SI: 'EUR',
        HR: 'EUR',
        BG: 'BGN',
        RO: 'RON',
        PL: 'PLN',
        CZ: 'CZK',
        HU: 'HUF',
        SE: 'SEK',
        NO: 'NOK',
        DK: 'DKK',
        CH: 'CHF',
        IS: 'ISK',
        AU: 'AUD',
        NZ: 'NZD',
        JP: 'JPY',
        CN: 'CNY',
        HK: 'HKD',
        SG: 'SGD',
        IN: 'INR',
        ID: 'IDR',
        TH: 'THB',
        MY: 'MYR',
        PH: 'PHP',
        AE: 'AED',
        SA: 'SAR',
        QA: 'QAR',
        KW: 'KWD',
        BH: 'BHD',
        OM: 'OMR',
        ZA: 'ZAR',
        NG: 'NGN',
        KE: 'KES',
        EG: 'EGP',
        IL: 'ILS',
        TR: 'TRY',
        DZ: 'DZD',
        BR: 'BRL',
        AR: 'ARS',
        CL: 'CLP',
        CO: 'COP',
        PE: 'PEN',
        UY: 'UYU',
    };
    return countryToCurrency[code] || 'USD';
}
//# sourceMappingURL=currency.js.map