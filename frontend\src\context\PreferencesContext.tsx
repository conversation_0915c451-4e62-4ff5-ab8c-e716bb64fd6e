import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useAuth } from './AuthContext';
import { CountryCode, SupportedCurrency, SupportedLanguage, getBrowserPreferenceDefaults } from '../utils/preferences';
import { fetchWithCredentials, getBackendUrl } from '../utils/auth';

interface PreferencesContextValue {
	language: SupportedLanguage;
	currency: SupportedCurrency;
	country: CountryCode;
	setLanguage: (language: SupportedLanguage) => void;
	setCurrency: (currency: SupportedCurrency) => void;
	setCountry: (country: CountryCode) => void;
}

const PreferencesContext = createContext<PreferencesContextValue | undefined>(undefined);

type PersistedPrefs = {
	language?: SupportedLanguage;
	currency?: SupportedCurrency;
	country?: CountryCode;
};

function getBrowserDefaults(): Required<PersistedPrefs> {
	const { language, country, currency } = getBrowserPreferenceDefaults();
	return { language, country, currency };
}

export function PreferencesProvider({ children }: { children: React.ReactNode }) {
	const { user } = useAuth();
	const defaults = useMemo(() => getBrowserDefaults(), []);
	const [language, setLanguageState] = useState<SupportedLanguage>(defaults.language);
	const [currency, setCurrencyState] = useState<SupportedCurrency>(defaults.currency);
	const [country, setCountryState] = useState<CountryCode>(defaults.country);

	const storageKey = useMemo(() => {
		const userId = user?.id ? String(user.id) : 'anon';
		const key = `teno:prefs:${userId}`;
		console.debug('[Prefs] storageKey computed', { userId, key });
		return key;
	}, [user?.id]);

	// Hydrate from localStorage on mount or when user changes
	useEffect(() => {
		try {
			const raw = localStorage.getItem(storageKey);
			console.debug('[Prefs] hydrate start', { storageKey, raw });
			if (raw) {
				const parsed = JSON.parse(raw) as PersistedPrefs;
				const nextLanguage = parsed.language || defaults.language;
				const nextCurrency = parsed.currency || defaults.currency;
				const nextCountry = parsed.country || defaults.country;
				console.debug('[Prefs] hydrate parsed', { parsed, nextLanguage, nextCurrency, nextCountry });
				setLanguageState(nextLanguage);
				setCurrencyState(nextCurrency);
				setCountryState(nextCountry);
			} else {
				console.debug('[Prefs] hydrate no existing, using defaults', defaults);
				setLanguageState(defaults.language);
				setCurrencyState(defaults.currency);
				setCountryState(defaults.country);
			}
		} catch {
			console.debug('[Prefs] hydrate error, falling back to defaults', defaults);
			setLanguageState(defaults.language);
			setCurrencyState(defaults.currency);
			setCountryState(defaults.country);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [storageKey]);

	// Re-hydrate on auth changes (login/logout) since key scope changes
	useEffect(() => {
		const onAuthChange = () => {
			try {
				const raw = localStorage.getItem(storageKey);
				if (raw) {
					const parsed = JSON.parse(raw) as PersistedPrefs;
					setLanguageState(parsed.language || defaults.language);
					setCurrencyState(parsed.currency || defaults.currency);
					setCountryState(parsed.country || defaults.country);
				} else {
					setLanguageState(defaults.language);
					setCurrencyState(defaults.currency);
					setCountryState(defaults.country);
				}
			} catch {
				setLanguageState(defaults.language);
				setCurrencyState(defaults.currency);
				setCountryState(defaults.country);
			}
		};
		if (typeof window !== 'undefined') {
			window.addEventListener('teno:auth:change', onAuthChange as EventListener);
		}
		return () => {
			if (typeof window !== 'undefined') {
				window.removeEventListener('teno:auth:change', onAuthChange as EventListener);
			}
		};
	}, [storageKey, defaults.language, defaults.currency, defaults.country]);

	// If a user is present, fetch server-side preferences and apply
	useEffect(() => {
		const userId = user?.id ? String(user.id) : null;
		if (!userId) return;
		let aborted = false;
		(async () => {
			try {
				    const url = `${getBackendUrl()}/users/${encodeURIComponent(userId)}`;
				console.debug('[Prefs] fetching server preferences', { userId, url });
				const resp = await fetchWithCredentials(url);
				if (!resp.ok) return;
				const payload = await resp.json();
				if (aborted || !payload) return;
				const nextLanguage = (payload.preferredLanguage as SupportedLanguage) || defaults.language;
				const nextCurrency = (payload.preferredCurrency as SupportedCurrency) || defaults.currency;
				const nextCountry = (payload.countryCode as CountryCode) || defaults.country;
				console.debug('[Prefs] server preferences received', { nextLanguage, nextCurrency, nextCountry });
				setLanguageState(nextLanguage);
				setCurrencyState(nextCurrency);
				setCountryState(nextCountry);
				try {
					localStorage.setItem(storageKey, JSON.stringify({ language: nextLanguage, currency: nextCurrency, country: nextCountry }));
				} catch {}
			} catch {}
		})();
		return () => { aborted = true; };
	}, [user?.id, storageKey, defaults.language, defaults.currency, defaults.country]);

	const persist = useCallback((next: PersistedPrefs) => {
		try {
			const current: PersistedPrefs = {
				language,
				currency,
				country,
			};
			const merged = { ...current, ...next };
			console.debug('[Prefs] persist', { storageKey, current, next, merged });
			localStorage.setItem(storageKey, JSON.stringify(merged));
		} catch {}
	}, [language, currency, country, storageKey]);

	const setLanguage = useCallback((lng: SupportedLanguage) => {
		setLanguageState(lng);
		persist({ language: lng });
	}, [persist]);

	const setCurrency = useCallback((cur: SupportedCurrency) => {
		console.debug('[Prefs] setCurrency', { from: currency, to: cur });
		setCurrencyState(cur);
		persist({ currency: cur });
	}, [persist, currency]);

	const setCountry = useCallback((cc: CountryCode) => {
		setCountryState(cc);
		persist({ country: cc });
	}, [persist]);

	const value = useMemo<PreferencesContextValue>(() => ({
		language,
		currency,
		country,
		setLanguage,
		setCurrency,
		setCountry,
	}), [language, currency, country, setLanguage, setCurrency, setCountry]);

	return (
		<PreferencesContext.Provider value={value}>
			{children}
		</PreferencesContext.Provider>
	);
}

export function usePreferences(): PreferencesContextValue {
	const ctx = useContext(PreferencesContext);
	if (!ctx) {
		throw new Error('usePreferences must be used within a PreferencesProvider');
	}
	return ctx;
}


