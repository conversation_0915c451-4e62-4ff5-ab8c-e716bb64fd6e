import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../context/AuthContext';

export default function AuthCallback() {
  const router = useRouter();
  const { refresh } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const processCallback = async () => {
      try {
        const { token, error: authError } = router.query;

        if (authError) {
          setError(`Authentication failed: ${authError}`);
          setIsProcessing(false);
          return;
        }

        if (!token || typeof token !== 'string') {
          setError('No authentication token received');
          setIsProcessing(false);
          return;
        }

        console.log('[AuthCallback] Received token:', token.substring(0, 20) + '...');

        // Store the token in a secure cookie (remove secure flag for localhost development)
        const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        const cookieOptions = isLocalhost 
          ? `path=/; max-age=86400; samesite=lax`
          : `path=/; max-age=86400; secure; samesite=strict`;
        
        document.cookie = `access_token_client=${encodeURIComponent(token)}; ${cookieOptions}`;
        
        console.log('[AuthCallback] Token stored in cookie successfully');

        // Also store in localStorage as backup
        try {
          localStorage.setItem('teno:auth:token', token);
          console.log('[AuthCallback] Token stored in localStorage successfully');
        } catch (e) {
          console.warn('Could not store token in localStorage:', e);
        }

        // Refresh auth context to get user data
        await refresh();

        // Redirect to the next page or dashboard
        const nextParam = router.query.next as string;
        const target = nextParam && nextParam.startsWith('/') ? nextParam : '/dashboard';
        
        console.log('[AuthCallback] Redirecting to:', target);
        router.replace(target);
      } catch (err) {
        console.error('Error processing auth callback:', err);
        setError('Failed to complete authentication');
        setIsProcessing(false);
      }
    };

    if (router.isReady) {
      processCallback();
    }
  }, [router.isReady, router.query, router, refresh]);

  if (isProcessing) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Completing Authentication
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Please wait while we complete your login...
            </p>
          </div>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Authentication Error
            </h2>
            <p className="mt-2 text-sm text-red-600">
              {error}
            </p>
            <div className="mt-4">
              <button
                onClick={() => router.push('/login')}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
}
