import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Customer } from './customer.entity';

@Injectable()
export class CustomersService {
  constructor(
    @InjectRepository(Customer)
    private customersRepository: Repository<Customer>,
  ) {}

  async findAll(): Promise<Customer[]> {
    return this.customersRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Customer> {
    const customer = await this.customersRepository.findOne({
      where: { id, isDeleted: false },
    });
    
    if (!customer) {
      throw new NotFoundException(`Customer with ID ${id} not found`);
    }
    
    return customer;
  }

  async findByStoreId(storeId: string): Promise<Customer[]> {
    return this.customersRepository.find({
      where: { storeId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async create(createCustomerDto: Partial<Customer> & { userId?: string | number }): Promise<Customer> {
    // Extract userId and map it to the correct fields
    const { userId, ...customerData } = createCustomerDto;

    // Create the customer with proper field mapping
    const customer = this.customersRepository.create({
      ...customerData,
      createdBy: userId?.toString(),
    });

    return this.customersRepository.save(customer);
  }

  async update(id: string, updateCustomerDto: Partial<Customer>): Promise<Customer> {
    const customer = await this.findOne(id);
    Object.assign(customer, updateCustomerDto);
    return this.customersRepository.save(customer);
  }

  async remove(id: string): Promise<void> {
    const customer = await this.findOne(id);
    customer.isDeleted = true;
    await this.customersRepository.save(customer);
  }
}
