{"version": 3, "file": "check-migrations.js", "sourceRoot": "", "sources": ["../../src/scripts/check-migrations.ts"], "names": [], "mappings": ";;;AAEA,qCAAqC;AACrC,mCAAgC;AAChC,+BAA4B;AAC5B,0CAAsC;AAGtC,IAAA,eAAM,GAAE,CAAC;AAET,KAAK,UAAU,eAAe;IAC5B,MAAM,UAAU,GAAG,IAAI,oBAAU,CAAC;QAChC,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;QACxC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,CAAC;QAC7C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,UAAU;QAC3C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;QACvC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,eAAe;QAChD,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,yBAAyB,CAAC,CAAC;QACtD,UAAU,EAAE,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,0BAA0B,CAAC,CAAC;QACzD,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;KACnF,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAGnD,MAAM,iBAAiB,GAAG,MAAM,UAAU,CAAC,cAAc,EAAE,CAAC;QAG5D,MAAM,aAAa,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QACvD,IAAI,mBAAmB,GAAa,EAAE,CAAC;QACvC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAA,kBAAO,EAAC,aAAa,CAAC,CAAC;YAC3C,mBAAmB,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACxC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAC7C,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAGD,IAAI,kBAAkB,GAAU,EAAE,CAAC;QACnC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACnD,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;OAItC,CAAC,CAAC;YACH,kBAAkB,GAAG,MAAM,IAAI,EAAE,CAAC;YAClC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,0BAA0B,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,yBAAyB,iBAAiB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,4BAA4B,mBAAmB,CAAC,MAAM,IAAI,CAAC,CAAC;QAExE,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACtC,kBAAkB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;gBAC9C,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC1G,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,GAAG,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC;QAED,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,mBAAmB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;gBAC/C,MAAM,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;gBACxE,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC;QAED,IAAI,iBAAiB,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;YAC/C,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAC3C,CAAC;QAED,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAChD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,eAAe,EAAE,CAAC"}