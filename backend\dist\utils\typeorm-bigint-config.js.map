{"version": 3, "file": "typeorm-bigint-config.js", "sourceRoot": "", "sources": ["../../src/utils/typeorm-bigint-config.ts"], "names": [], "mappings": ";;AAkBA,8EAQC;AAMD,oEA+CC;AAMD,gDA0BC;AAMD,4DAyBC;AAKD,wCAkBC;AAKD,8BAsBC;AA3LD,qDAAiF;AAajF,SAAgB,iCAAiC,CAC/C,WAA8B;IAE9B,OAAO;QACL,GAAG,WAAW;KAGf,CAAC;AACJ,CAAC;AAMD,SAAgB,4BAA4B,CAC1C,YAAiB,EACjB,OAGC;IAED,MAAM,eAAe,GAAG,YAAY,CAAC,OAAO,CAAC;IAC7C,MAAM,cAAc,GAAG,YAAY,CAAC,MAAM,CAAC;IAC3C,MAAM,gBAAgB,GAAG,YAAY,CAAC,QAAQ,CAAC;IAC/C,MAAM,yBAAyB,GAAG,YAAY,CAAC,iBAAiB,CAAC;IAEjE,IAAI,OAAO,EAAE,gBAAgB,KAAK,KAAK,EAAE,CAAC;QAExC,YAAY,CAAC,OAAO,GAAG,KAAK;YAC1B,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,OAAO,IAAA,oCAAmB,EAAC,OAAO,CAAC,CAAC;QACtC,CAAC,CAAC;QAGF,YAAY,CAAC,MAAM,GAAG,KAAK;YACzB,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,OAAO,MAAM,CAAC,CAAC,CAAC,IAAA,oCAAmB,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACrD,CAAC,CAAC;QAGF,YAAY,CAAC,iBAAiB,GAAG,KAAK;YACpC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,MAAM,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1E,OAAO;gBACL,IAAA,oCAAmB,EAAC,UAAU,CAAC;gBAC/B,IAAA,oCAAmB,EAAC,QAAQ,CAAC;aAC9B,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,IAAI,OAAO,EAAE,YAAY,KAAK,KAAK,EAAE,CAAC;QAEpC,YAAY,CAAC,QAAQ,GAAG,KAAK;YAC3B,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;YACvB,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAMD,SAAgB,kBAAkB,CAChC,MAAW,EACX,WAAmB,EACnB,UAA+D;IAE/D,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;IAExC,IAAI,cAAc,EAAE,CAAC;QACnB,UAAU,CAAC,KAAK,GAAG,KAAK,WAAU,GAAG,IAAO;YAC1C,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACtD,OAAO,IAAA,oCAAmB,EAAC,MAAM,CAAC,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/D,OAAO,CAAC,IAAI,CAAC,iCAAiC,WAAW,0BAA0B,CAAC,CAAC;oBAErF,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;wBAChB,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC5C,CAAC;gBACH,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAMD,SAAgB,wBAAwB;IAEtC,IAAA,yCAAwB,GAAE,CAAC;IAG3B,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC;IAChC,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;IAClC,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;IAEpC,OAAO,CAAC,GAAG,GAAG,UAAS,GAAG,IAAW;QACnC,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAA,oCAAmB,EAAC,GAAG,CAAC,CAAC,CAAC;QAClE,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEF,OAAO,CAAC,IAAI,GAAG,UAAS,GAAG,IAAW;QACpC,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAA,oCAAmB,EAAC,GAAG,CAAC,CAAC,CAAC;QAClE,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IAC/C,CAAC,CAAC;IAEF,OAAO,CAAC,KAAK,GAAG,UAAS,GAAG,IAAW;QACrC,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAA,oCAAmB,EAAC,GAAG,CAAC,CAAC,CAAC;QAClE,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IAChD,CAAC,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;AACtD,CAAC;AAKD,SAAgB,cAAc,CAAC,GAAQ;IACrC,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACtC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACjD,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAKD,SAAgB,SAAS,CAAI,GAAM;IACjC,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACtC,OAAO,GAAG,CAAC;IACb,CAAC;IAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC,QAAQ,EAAkB,CAAC;IACxC,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,CAAC,GAAG,CAAC,SAAS,CAAiB,CAAC;IAC5C,CAAC;IAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAQ,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/C,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC"}