# Scripts

This folder contains utility scripts for testing and development.

## test_llm_api.ts

A comprehensive test script for the LLM API that demonstrates:

- Agent creation with custom instructions
- Mock tool implementations
- LLM API testing with conversation history
- Tool call execution and output analysis
- Cost tracking and performance metrics

### Usage

#### Option 1: Using the example scripts (Recommended)

**For Windows (PowerShell):**
```powershell
# From the ai-agent directory
.\scripts\run_example.ps1
```

**For Linux/Mac (Bash):**
```bash
# From the ai-agent directory
chmod +x scripts/run_example.sh
./scripts/run_example.sh
```

**For Node.js:**
```bash
# From the ai-agent directory
node scripts/run_example.js
```

#### Option 2: Manual setup

1. **Set up environment variables:**
   ```bash
   # Copy the example environment file
   cp scripts/example.env .env
   
   # Edit .env and add your OpenRouter API key
   # API_KEY=your_actual_api_key_here
   ```

2. **Run the test:**
   ```bash
   # From the ai-agent directory
   npm run test:llm
   
   # Or directly with ts-node
   npx ts-node scripts/test_llm_api.ts
   ```

### What it tests

The script tests the LLM API with a sales assistant agent that:
- Has access to mock tools (getStoreUrl, placeOrder, searchCustomers, etc.)
- Processes a conversation about ordering products
- Demonstrates tool calling capabilities
- Shows cost tracking and execution details

### Expected output

The script will display:
- Input messages used for testing
- LLM response content
- Tool calls made by the LLM
- Tool execution outputs
- Performance metrics and costs
- Any errors that occur during execution

### Customization

You can modify the test by:
- Changing the agent instructions
- Adding/removing mock tools
- Modifying the test conversation
- Adjusting the mock tool implementations
