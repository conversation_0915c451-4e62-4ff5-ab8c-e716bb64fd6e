# Image Upload System for Products

## Overview

The product creation and editing system now includes a proper image upload functionality that allows users to browse for images from their local device instead of manually entering image URLs.

## How It Works

### 1. Frontend Components

- **ImageUpload Component** (`frontend/src/components/ImageUpload.tsx`): A reusable component that handles image selection, preview, and upload
- **Product Forms**: Both the product creation modal and product edit page now use the ImageUpload component

### 2. Backend API Endpoints

- **Upload Endpoint** (`/api/images`): Accepts POST requests with image files
- **Download Endpoint** (`/api/images/[filename]`): Serves uploaded images

### 3. Image Processing

- Images are automatically resized to 256x256 pixels using Sharp
- Supported formats: JPG, PNG, GIF
- Maximum file size: 10MB
- Images are stored with unique UUID-based filenames

## User Experience

### Creating a Product
1. Click "Add Product" button
2. Fill in product details (name, description, price, SKU)
3. Click "Browse for Image" to select an image file
4. Image is automatically uploaded and previewed
5. Click "Create Product" to save

### Editing a Product
1. Navigate to product detail page
2. Click "Edit" button
3. Use "Change Image" to upload a new image
4. Click "Save Changes" to update

## Technical Implementation

### Frontend
- Uses HTML5 File API for file selection
- FormData for file upload
- Real-time preview using FileReader
- Error handling and loading states
- Responsive design with Tailwind CSS

### Backend
- Formidable for multipart form parsing
- Sharp for image processing and resizing
- UUIDv7 for unique filename generation
- Proper CORS configuration
- File system storage in `backend/images/` directory

### API Integration
- `imageApi.upload()` function in `frontend/src/utils/api.ts`
- Automatic image URL assignment to product `imageUrl` field
- Seamless integration with existing product CRUD operations

## File Structure

```
frontend/
├── src/
│   ├── components/
│   │   └── ImageUpload.tsx          # Image upload component
│   ├── pages/
│   │   └── products/
│   │       ├── index.tsx            # Product list with creation modal
│   │       └── [id].tsx             # Product detail/edit page
│   └── utils/
│       └── api.ts                   # API utilities including imageApi
backend/
├── src/
│   └── pages/
│       └── api/
│           └── images/
│               ├── index.ts          # Image upload endpoint
│               └── [filename].ts     # Image download endpoint
└── images/                           # Image storage directory
```

## Configuration

### Environment Variables
- `FRONTEND_URL`: Frontend URL for CORS configuration (defaults to `http://localhost:3000`)
- `NEXT_PUBLIC_API_BASE_URL`: Backend API URL (defaults to `http://localhost:8000`)

### Next.js Rewrites
The frontend automatically proxies image API requests to the backend:
```javascript
// frontend/next.config.js
{
  source: '/api/images/:path*',
  destination: `${backendUrl}/api/images/:path*`,
}
```

## Benefits

1. **User-Friendly**: No need to manually enter image URLs
2. **Automatic Processing**: Images are automatically resized and optimized
3. **Secure**: File type validation and size limits
4. **Responsive**: Works on all device sizes
5. **Integrated**: Seamlessly integrated with existing product management
6. **Maintainable**: Clean, reusable component architecture

## Future Enhancements

- Drag and drop support
- Multiple image upload
- Image cropping tools
- Cloud storage integration
- Image optimization and compression
- Thumbnail generation
