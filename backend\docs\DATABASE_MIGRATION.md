# Migration Commands

## Generate Migration from Entity Changes
```bash
npm run migration:generate -- --name=MigrationName
```
Generates a new migration file based on detected entity schema changes

## Run Migrations
```bash
npm run migration:run
```
Executes all pending migrations in the correct order

## Check Migration Status
```bash
npm run migration:status
```
Shows which migrations have been executed and which are pending

## Seed Database
```bash
npm run db:seed
```
Populates the database with initial data (only runs if database is empty)
