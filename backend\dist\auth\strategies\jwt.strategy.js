"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const passport_jwt_1 = require("passport-jwt");
const passport_1 = require("@nestjs/passport");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const users_service_1 = require("../../users/users.service");
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(configService, usersService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_SECRET') || 'your-secret-key',
        });
        this.configService = configService;
        this.usersService = usersService;
        console.log('[JWT Strategy] Initialized with secret:', this.configService.get('JWT_SECRET')?.substring(0, 10) + '...');
    }
    async validate(payload) {
        console.log('[JWT Strategy] Validating payload:', payload);
        console.log('[JWT Strategy] payload.sub:', payload.sub);
        console.log('[JWT Strategy] payload.email:', payload.email);
        try {
            const user = await this.usersService.findOne(payload.sub);
            console.log('[JWT Strategy] User found:', user ? { id: user.id, email: user.email, isDeleted: user.isDeleted } : 'null');
            if (!user || user.isDeleted) {
                console.log('[JWT Strategy] User not found or deleted, throwing UnauthorizedException');
                throw new common_1.UnauthorizedException();
            }
            console.log('[JWT Strategy] User validated successfully, returning:', { id: user.id, email: user.email, name: user.name, image: user.image });
            return { id: user.id, email: user.email, name: user.name, image: user.image };
        }
        catch (error) {
            console.error('[JWT Strategy] Error during validation:', error);
            throw new common_1.UnauthorizedException();
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        users_service_1.UsersService])
], JwtStrategy);
//# sourceMappingURL=jwt.strategy.js.map