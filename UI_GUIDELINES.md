# UI Guidelines for Teno Store

This document outlines the design system, component patterns, and UI decisions for creating consistent and beautiful interfaces across the Teno Store application.

## Table of Contents

1. [Design Philosophy](#design-philosophy)
2. [Design Themes](#design-themes)
3. [Color System](#color-system)
4. [Typography](#typography)
5. [Layout Patterns](#layout-patterns)
6. [Component Library](#component-library)
7. [State Management Patterns](#state-management-patterns)
8. [Best Practices](#best-practices)
9. [Examples](#examples)

## Design Philosophy

### Core Principles
- **Modern & Professional**: Clean, contemporary interfaces that inspire confidence
- **Accessible**: WCAG 2.1 AA compliant with proper contrast ratios and keyboard navigation
- **Responsive**: Mobile-first design that scales beautifully across all devices
- **Consistent**: Unified visual language across all pages and components
- **Performance**: Optimized for fast loading and smooth interactions

### User Experience Goals
- **Clarity**: Clear information hierarchy and intuitive navigation
- **Efficiency**: Minimal friction for common tasks
- **Feedback**: Immediate visual feedback for all user interactions
- **Trust**: Professional appearance that builds user confidence

## Design Themes

The application supports three design themes, optimized for different use cases:

### 1. Agentic Dark Theme (Premium/Auth Pages)
**Use for**: Login, Register, Premium features, Dashboard
- Dark background (`#0B1020`) with subtle gradients
- Glass morphism effects with backdrop blur
- Emerald/Cyan/Indigo brand gradient accents
- High contrast for readability

### 2. App Console Dark Theme (Authenticated App)
**Use for**: Products, Inventory, Orders, Sales, Customers, Conversations (internal), Dashboard
- Background: `bg-slate-900`
- Surfaces: `bg-slate-800` with `rounded-lg shadow-lg`
- Borders/Dividers: `border-white/10`, `divide-white/10`
- Text: Primary `text-slate-100`, secondary `text-slate-300`, muted `text-slate-400`
- Accents: Emerald/Cyan/Indigo for emphasis

### 3. Clean Light Theme (Public/Content Pages)
**Use for**: Home page, Public content, Documentation
- Light gradient backgrounds (`blue-50` to `indigo-100`)
- White card containers with subtle shadows
- Blue/Green accent colors
- Optimized for content readability

## Color System

### CSS Custom Properties
```css
:root {
  --bg-primary: #0B1020;      /* Dark theme primary background */
  --brand-emerald: #10B981;    /* Primary brand color */
  --brand-cyan: #22D3EE;       /* Secondary brand color */
  --brand-indigo: #6366F1;     /* Tertiary brand color */
}
```

### Brand Colors
- **Primary**: Emerald (`#10B981`) - Success states, primary CTAs
- **Secondary**: Cyan (`#22D3EE`) - Highlights, links
- **Accent**: Indigo (`#6366F1`) - Secondary actions, badges

### Semantic Colors
- **Success**: Green variants (`green-600`, `green-100`)
- **Warning**: Yellow variants (`yellow-600`, `yellow-100`) 
- **Error**: Red variants (`red-600`, `red-100`)
- **Info**: Blue variants (`blue-600`, `blue-100`)

### Neutral Palette
- **Dark Theme**: Slate variants (`slate-900`, `slate-300`, `slate-100`)
- **Light Theme**: Gray variants (`gray-900`, `gray-600`, `gray-300`)

## Typography

### Font Hierarchy
- **Display**: 3xl-4xl font size, `font-extrabold` weight
- **Heading**: 2xl-3xl font size, `font-semibold` weight  
- **Subheading**: xl-2xl font size, `font-medium` weight
- **Body**: sm-base font size, `font-normal` weight
- **Caption**: xs-sm font size, `font-medium` weight

### Text Color Classes
- **Dark Theme**: 
  - Primary: `text-slate-100`
  - Secondary: `text-slate-300/80` (using `.subtle-text`)
  - Muted: `text-slate-500`
- **Light Theme**:
  - Primary: `text-gray-900`
  - Secondary: `text-gray-600` 
  - Muted: `text-gray-500`

## Layout Patterns

### Page Structure
```jsx
<div className="min-h-screen [theme-background]">
  <Head>
    <title>Page Title - Teno Store</title>
    <meta name="description" content="Page description" />
  </Head>
  
  {/* Navigation (if applicable) */}
  
  {/* Main Content */}
  <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    {/* Page content */}
  </div>
</div>
```

### Container Patterns
- **Full Width**: `min-h-screen` for full viewport pages
- **Centered Content**: `max-w-md w-full mx-auto` for forms
- **Wide Content**: `max-w-7xl mx-auto` for dashboards
- **Responsive Padding**: `px-4 sm:px-6 lg:px-8`

### Grid Layouts
- **Two Column**: `grid md:grid-cols-2 gap-8`
- **Card Grid**: `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6`
- **Form Layout**: `space-y-4` or `space-y-6`

## Component Library

### 1. Cards

#### Glass Card (Dark Theme)
```jsx
<div className="glass-card">
  <div className="card-accent" />
  {/* Card content */}
</div>
```
- **Use for**: Premium features, auth forms, modal content
- **Features**: Backdrop blur, subtle borders, gradient accents

#### Standard Card (Light Theme)
```jsx
<div className="bg-white rounded-lg shadow-lg p-6">
  {/* Card content */}
</div>
```
- **Use for**: Content cards, user profiles, data display
- **Features**: Clean shadows, rounded corners, ample padding

#### Console Card (Dark App Theme)
```jsx
<div className="bg-slate-800 rounded-lg shadow-lg">
  <div className="p-6">{/* Content */}</div>
</div>
```
- **Use for**: Data lists, tables, summaries in authenticated app
- **Features**: Low-contrast borders `border-white/10`, hover `hover:bg-white/5`

#### Glass/Gradient Modal Shell (Dark App Theme)
```jsx
<div className="relative bg-gradient-to-br from-slate-800/95 to-slate-900/95 border border-white/10 rounded-2xl shadow-2xl overflow-hidden">
  <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-indigo-500/20 rounded-2xl blur opacity-60" />
  <div className="relative bg-slate-900/90 rounded-2xl p-8">{/* Modal content */}</div>
</div>
```
- **Use for**: Create/Edit/Delete modals
- **Features**: Soft gradient border glow, slate content surface

### 2. Buttons

#### Primary Button (Dark Theme)
```jsx
<button className="group primary-button">
  <span>Button Text</span>
  <span className="button-glow group-hover:opacity-100" />
</button>
```
- **Use for**: Main CTAs, form submissions
- **Features**: Hover glow effect, proper disabled states

#### Standard Button (Light Theme)
```jsx
<button className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">
  Button Text
</button>
```
- **Use for**: General actions, navigation
- **Features**: Color variants, smooth transitions

#### Secondary Button
```jsx
<button className="border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 px-4 py-2 rounded-md transition-colors">
  Secondary Action
</button>
```

#### Console Primary Button (Dark App Theme)
```jsx
<button className="bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors disabled:opacity-50">
  Action
</button>
```

#### Console Gradient CTA (Modal)
```jsx
<button className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-xl font-medium shadow-lg shadow-emerald-500/25 disabled:opacity-50">
  Submit
</button>
```

#### Console Subtle Button
```jsx
<button className="border border-slate-600 text-slate-300 bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-md transition-colors">
  Secondary
</button>
```

#### Icon Action Buttons (App Console Dark Theme)
Use these for table row actions like Edit/Delete to keep actions compact and consistent.

```jsx
// Edit Icon Button
<button className="text-slate-300 hover:text-emerald-400 rounded-md p-2 border border-white/10 hover:bg-white/5" title="Edit" aria-label="Edit">
  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
  </svg>
  <span className="sr-only">Edit</span>
  </button>

// Delete Icon Button
<button className="text-slate-300 hover:text-red-400 rounded-md p-2 border border-white/10 hover:bg-white/5" title="Delete" aria-label="Delete">
  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
  <span className="sr-only">Delete</span>
</button>
```

Notes:
- Use `rounded-md p-2` with a subtle `border-white/10` and `hover:bg-white/5`.
- Color the hover text: emerald for edit, red for delete.
- Always include `title` and `aria-label` for accessibility; add visually hidden text with `sr-only` if needed.

### 3. Form Elements
### 3.5. Modal Design (App Console Dark Theme)

Use this exact structure for all modals in authenticated app pages.

```jsx
{/* Backdrop */}
<div className="fixed inset-0 z-50 flex items-center justify-center p-4">
  <div className="absolute inset-0 bg-black/50" />

  {/* Shell with gradient glow */}
  <div className="relative w-full max-w-lg md:max-w-2xl lg:max-w-4xl overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95">
    <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-indigo-500/20 blur opacity-60" />

    {/* Content */}
    <div className="relative flex flex-col max-h-[80vh]">
      {/* Header */}
      <div className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-slate-100">Modal Title</h3>
        <button className="text-slate-400 hover:text-slate-200 rounded-md p-1">
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
        </button>
      </div>

      {/* Body (scrollable) */}
      <div className="p-6 overflow-y-auto">
        {/* Modal content */}
      </div>

      {/* Footer */}
      <div className="px-6 py-3 border-t border-white/10 flex items-center justify-end gap-3">
        <button className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-md transition-colors">Cancel</button>
        <button className="bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors">Confirm</button>
      </div>
    </div>
  </div>
</div>
```

Notes:
- Always use `rounded-2xl`, `border-white/10`, gradient glow, and slate content.
- Header and footer use `border-white/10`; body scroll limited by `max-h-[80vh]`.
- Primary actions: emerald or cyan; secondary actions: slate subtle button.

#### Focus Management in Modals (Required)

When a modal opens, the first actionable field must receive keyboard focus automatically. This improves accessibility and speeds up data entry.

Use a ref + effect to focus the first input on mount:

```tsx
import { useEffect, useRef } from 'react';

function ExampleModal({ onClose }: { onClose: () => void }) {
  const firstInputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    firstInputRef.current?.focus();
  }, []);

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center p-4 z-50">
      <div className="relative bg-slate-900/90 rounded-2xl p-8 border border-white/10">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-slate-100">Create Item</h2>
          <button onClick={onClose} className="text-slate-400 hover:text-slate-200 p-1 rounded-md">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>

        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-200 mb-1">Name</label>
            <input
              ref={firstInputRef}
              type="text"
              className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-xl text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
              placeholder="Enter name"
            />
          </div>
          {/* Other fields... */}
        </form>
      </div>
    </div>
  );
}
```

Alternatively, for simple cases you may use the `autoFocus` prop on the first input. Prefer the ref approach when conditionally rendering or when inputs may unmount/remount.

#### Keyboard Shortcuts (Required)

Use consistent, discoverable keyboard shortcuts across all entity list pages.

- **F1**: Open the “+ Add [Entity]” create modal on list pages (Products, Customers, Orders, etc.).
  - Do not trigger while focus is inside inputs, textareas, selects, or contenteditable elements.
  - Prevent the default browser action for F1 when handled.
  - Only activate when the page prerequisites are met (e.g., a store is selected).

Reusable pattern:

```tsx
import { useEffect } from 'react';

export function useAddEntityShortcut(isEnabled: boolean, onOpen: () => void) {
  useEffect(() => {
    if (!isEnabled) return;

    const handler = (event: KeyboardEvent) => {
      if (event.key !== 'F1') return;

      const target = event.target as HTMLElement | null;
      const tag = target?.tagName?.toLowerCase();
      const isFormField = tag === 'input' || tag === 'textarea' || tag === 'select' || (target?.isContentEditable ?? false);
      if (isFormField) return;

      event.preventDefault();
      onOpen();
    };

    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [isEnabled, onOpen]);
}
```

Usage on a page component:

```tsx
// Example: Products list page
const isShortcutEnabled = Boolean(currentStoreId) && !showAddModal;
useAddEntityShortcut(isShortcutEnabled, () => setShowAddModal(true));
```


#### Input Field (Light)
```jsx
<div>
  <label className="block text-sm font-medium text-gray-700 mb-1">
    Label
  </label>
  <input
    type="text"
    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
    placeholder="Placeholder text"
  />
</div>
```

#### Form Container
```jsx
<form className="space-y-4">
  {/* Form fields */}
</form>
```

#### Input Field (Dark App Theme)
```jsx
<input
  className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-xl text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-emerald-400/50"
  placeholder="Placeholder"
/>
```

### 4. Navigation

#### Top Navigation
```jsx
<nav className="bg-white shadow">
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div className="flex justify-between h-16">
      <div className="flex items-center">
        <h1 className="text-xl font-semibold text-gray-900">Teno Store</h1>
      </div>
      <div className="flex items-center space-x-4">
        {/* Navigation items */}
      </div>
    </div>
  </div>
</nav>
```

### 5. Status Indicators

#### Loading Spinner
```jsx
<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-400 mx-auto"></div>
```

#### Error Message
```jsx
<div className="bg-red-500/10 border border-red-500/20 text-red-300 px-4 py-3 rounded-md">Error message</div>
```

#### Success Message
```jsx
<div className="bg-green-500/10 border border-green-500/20 text-green-300 px-4 py-3 rounded-md">Success message</div>
```

#### Info Message
```jsx
<div className="bg-blue-500/10 border border-blue-500/20 text-blue-300 px-4 py-3 rounded-md">Info message</div>
```

### 6. Badges & Tags

#### Status Badge
```jsx
<span className={`text-xs px-2 py-1 rounded-full ${
  published 
    ? 'bg-emerald-500/10 text-emerald-300 ring-1 ring-emerald-500/20' 
    : 'bg-yellow-500/10 text-yellow-300 ring-1 ring-yellow-500/20'
}`}> 
  Status
</span>
```

#### Icon Badge
```jsx
<div className="icon-badge">
  <svg className="h-7 w-7 text-emerald-400">
    {/* Icon SVG */}
  </svg>
</div>
```

## State Management Patterns

### Loading States
- **Page Loading**: Full-screen spinner with branded colors
- **Component Loading**: Inline spinners with appropriate sizing
- **Button Loading**: Disabled state with loading text

### Error Handling
- **Form Errors**: Inline validation with red borders and messages
- **API Errors**: Toast notifications or alert banners
- **Page Errors**: Dedicated error pages with recovery actions

### Success Feedback
- **Form Success**: Green borders and success messages
- **Action Feedback**: Toast notifications
- **State Changes**: Immediate UI updates with optimistic updates

## Best Practices

### 1. Component Organization
```jsx
// Page structure template
export default function PageName() {
  // 1. Hooks and state
  const [state, setState] = useState();
  const router = useRouter();
  
  // 2. Effects and handlers
  useEffect(() => {}, []);
  const handleAction = () => {};
  
  // 3. Early returns for loading/error states
  if (loading) return <LoadingComponent />;
  if (error) return <ErrorComponent />;
  
  // 4. Main render
  return (
    <>
      <Head>
        <title>Page Title - Teno Store</title>
        <meta name="description" content="Description" />
      </Head>
      
      <div className="[theme-classes]">
        {/* Page content */}
      </div>
    </>
  );
}
```

### 2. Theme Selection Guide
- **Choose Dark Theme for**:
  - Authentication pages (login/register)
  - Premium/Pro features
  - Dashboard interfaces
  - Admin panels
  - Internal app pages (Products, Inventory, Orders, Sales, Customers, Conversations)

- **Choose Light Theme for**:
  - Public landing pages
  - Content-heavy pages
  - Documentation
  - Marketing pages

### 3. Responsive Design
- Always use Tailwind's responsive prefixes (`sm:`, `md:`, `lg:`)
- Test on mobile devices regularly
- Use `space-y-*` for vertical spacing
- Prefer `gap-*` for grid/flex layouts

### 4. Accessibility
- Include proper `alt` text for images
- Use semantic HTML elements
- Ensure keyboard navigation works
- Test with screen readers
- Maintain proper color contrast ratios

### 5. Performance
- Use `next/head` for SEO meta tags
- Optimize images with `next/image`
- Implement proper loading states
- Use React.memo for expensive components

### 6. General Terms for New and Other Pages

#### Page Structure Requirements
- **All pages must have**: Proper Head component with title and meta description
- **Consistent navigation**: Use TopTaskBar and SideTaskBar for authenticated pages
- **Proper error handling**: Include loading states, error messages, and empty states
- **Keyboard shortcuts**: Implement F1 for "Add New" actions where applicable
- **Focus management**: First actionable element should receive focus in modals

#### Modal Standards
- **Always use gradient modal shell**: `bg-gradient-to-br from-slate-800/95 to-slate-900/95` with border glow
- **Proper focus management**: First input should autofocus on modal open
- **Consistent button layout**: Cancel (secondary) on left, primary action on right
- **Keyboard navigation**: ESC to close, proper tab order
- **Accessibility**: Include proper ARIA labels and screen reader support

#### Table/Data Display Standards
- **Console card containers**: `bg-slate-800 rounded-lg shadow-lg` with `border-white/10`
- **Table headers**: `bg-white/5` with `border-white/10` dividers
- **Row hover states**: `hover:bg-white/5` for better interaction feedback
- **Action buttons**: Use icon buttons with proper tooltips and ARIA labels
- **Pagination**: Consistent pagination component with proper disabled states

#### Form Standards
- **Input styling**: `bg-slate-800/60 border border-slate-600/50 rounded-xl` with focus rings
- **Label consistency**: `text-sm font-medium text-slate-200` for all form labels
- **Validation states**: Red borders and error messages for invalid fields
- **Required field indicators**: Use `*` with `text-red-400` for required fields
- **Button states**: Include loading states with spinners and disabled states

#### Color Usage Standards
- **Primary actions**: Emerald/Cyan gradients for primary CTAs
- **Secondary actions**: Slate subtle buttons with hover states
- **Success states**: Emerald colors with proper contrast
- **Error states**: Red colors with clear error messaging
- **Warning states**: Yellow/Orange for caution states
- **Info states**: Blue for informational content

#### Typography Standards
- **Page titles**: `text-3xl font-extrabold tracking-tight text-slate-100`
- **Section headers**: `text-2xl font-semibold text-slate-100`
- **Body text**: `text-slate-300` for primary content, `text-slate-400` for secondary
- **Caption text**: `text-xs text-slate-400` for small details
- **Button text**: `text-sm font-medium` for consistency

#### Responsive Design Standards
- **Mobile-first approach**: Design for mobile, enhance for larger screens
- **Breakpoint usage**: Use `sm:`, `md:`, `lg:` prefixes appropriately
- **Table responsiveness**: Horizontal scroll for complex tables on mobile
- **Button sizing**: Full width on mobile, auto width on larger screens
- **Spacing**: Use Tailwind's spacing scale consistently

#### State Management Standards
- **Loading states**: Spinner with `animate-spin` and proper loading text
- **Error states**: Red background cards with clear error messaging
- **Success states**: Toast notifications or success banners
- **Empty states**: Illustrated empty states with clear call-to-action
- **Optimistic updates**: Immediate UI feedback for better UX

## Examples

### Auth Page Pattern (Dark Theme)
```jsx
<div className="app-bg">
  <div className="glow-overlay" />
  <div className="grid-overlay" />
  
  <div className="relative max-w-md w-full space-y-8">
    <div className="text-center">
      <div className="icon-badge">
        {/* Brand icon */}
      </div>
      <h2 className="mt-6 text-3xl font-extrabold tracking-tight brand-gradient-text">
        Page Title
      </h2>
      <p className="mt-2 subtle-text">
        Page description
      </p>
    </div>
    
    <div className="glass-card">
      <div className="card-accent" />
      {/* Form content */}
    </div>
  </div>
</div>
```

### Dashboard Page Pattern (Light Theme)
```jsx
<div className="min-h-screen bg-gray-50">
  <nav className="bg-white shadow">
    {/* Navigation */}
  </nav>
  
  <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <div className="grid md:grid-cols-2 gap-8">
      <div className="bg-white rounded-lg shadow-lg p-6">
        {/* Card content */}
      </div>
    </div>
  </div>
</div>
```

### Form Pattern
```jsx
<form onSubmit={handleSubmit} className="space-y-6">
  {error && (
    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
      {error}
    </div>
  )}
  
  <div>
    <label className="block text-sm font-medium mb-1">
      Field Label
    </label>
    <input
      type="text"
      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      value={value}
      onChange={handleChange}
    />
  </div>
  
  <button
    type="submit"
    disabled={isLoading}
    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
  >
    {isLoading ? 'Loading...' : 'Submit'}
  </button>
</form>
```

---

## Quick Reference

### Theme Classes
- **Dark Theme**: `app-bg`, `glass-card`, `primary-button`, `brand-gradient-text`
- **Light Theme**: `bg-gray-50`, `bg-white`, `shadow-lg`, standard Tailwind classes

### Common Patterns
- Page title: `text-3xl font-extrabold text-gray-900`
- Section heading: `text-2xl font-semibold text-gray-800 mb-4`
- Card container: `bg-white rounded-lg shadow-lg p-6`
- Form spacing: `space-y-4` or `space-y-6`
- Button: `px-4 py-2 rounded-md transition-colors`

### Responsive Breakpoints
- Mobile: Default (no prefix)
- Tablet: `sm:` (640px+)
- Desktop: `md:` (768px+)
- Large: `lg:` (1024px+)
- Extra Large: `xl:` (1280px+)

This guide should serve as the foundation for all UI development in the Teno Store application. When in doubt, refer to existing pages for patterns and consistency.
