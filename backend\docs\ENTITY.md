# Entity Common Fields

## Standard Entity Fields

All entities in the Teno Store system should include these common fields for consistency and audit trails:

### Required Fields
- `id`: Primary key 64 bit
- `createdAt`: Creation timestamp
- `updatedAt`: Last update timestamp
- `isDeleted`: Soft delete flag (default: false)

### User Tracking Fields
- `createdBy`: User 'Id' who created the entity
- `updatedBy`: User 'Id' who last updated the entity

### Scoping
- `userId`: userId the entity belongs to.
- `storeId`: storeId the entity belongs to.