import axios from 'axios';
import { Agent } from '../src/agent';
import { createTool } from '../src/tools';

/**
 * Interface for product information
 */
interface Product {
  id: string;
  name: string;
  price: number;
  description: string;
  inStock: boolean;
}

/**
 * Interface for customer information
 */
interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
}

/**
 * Tool for searching products
 */
const searchProducts = createTool(
  async function searchProducts(query: string): Promise<Product[]> {
    try {
      // This would typically connect to your backend API
      const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3000';
      const response = await axios.get(`${apiBaseUrl}/api/products/search`, {
        params: { q: query },
        timeout: 5000
      });
      
      return response.data;
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  },
  {
    description: 'Search for products by name or description',
    parameterTypes: {
      query: String
    },
    requiredParams: ['query']
  }
);

/**
 * Tool for getting product details
 */
const getProductDetails = createTool(
  async function getProductDetails(productId: string): Promise<Product | null> {
    try {
      const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3000';
      const response = await axios.get(`${apiBaseUrl}/api/products/${productId}`, {
        timeout: 5000
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting product details:', error);
      return null;
    }
  },
  {
    description: 'Get detailed information about a specific product',
    parameterTypes: {
      productId: String
    },
    requiredParams: ['productId']
  }
);

/**
 * Tool for creating a quote
 */
const createQuote = createTool(
  async function createQuote(
    customerId: string,
    productIds: string[],
    quantities: number[]
  ): Promise<{ quoteId: string; total: number }> {
    try {
      const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3000';
      const response = await axios.post(`${apiBaseUrl}/api/quotes`, {
        customerId,
        items: productIds.map((productId, index) => ({
          productId,
          quantity: quantities[index] || 1
        }))
      }, {
        timeout: 5000
      });
      
      return response.data;
    } catch (error) {
      console.error('Error creating quote:', error);
      throw new Error('Failed to create quote');
    }
  },
  {
    description: 'Create a sales quote for a customer with specified products and quantities',
    parameterTypes: {
      customerId: String,
      productIds: Array,
      quantities: Array
    },
    requiredParams: ['customerId', 'productIds', 'quantities']
  }
);

/**
 * Tool for getting customer information
 */
const getCustomerInfo = createTool(
  async function getCustomerInfo(customerId: string): Promise<Customer | null> {
    try {
      const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3000';
      const response = await axios.get(`${apiBaseUrl}/api/customers/${customerId}`, {
        timeout: 5000
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting customer info:', error);
      return null;
    }
  },
  {
    description: 'Get information about a customer by their ID',
    parameterTypes: {
      customerId: String
    },
    requiredParams: ['customerId']
  }
);

/**
 * Create a sales agent with product and quote management capabilities
 */
export function createSaleAgent(): Agent {
  return new Agent(
    'SaleAgent',
    `You are a professional sales assistant for Dido Distribution. You help customers find products, provide pricing information, and create quotes.
    
    Your capabilities:
    - Search for products in the inventory
    - Provide detailed product information and pricing
    - Create quotes for customers
    - Access customer information
    
    Guidelines:
    - Always be helpful and professional
    - Provide accurate product information and pricing
    - Suggest related or alternative products when appropriate
    - Confirm details before creating quotes
    - Ask for customer ID when creating quotes`,
    [searchProducts, getProductDetails, createQuote, getCustomerInfo],
    'Sales assistant for product information and quote generation'
  );
}
