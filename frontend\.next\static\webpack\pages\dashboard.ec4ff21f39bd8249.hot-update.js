"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./src/pages/dashboard.tsx":
/*!*********************************!*\
  !*** ./src/pages/dashboard.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _components_TopTaskBar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/TopTaskBar */ \"./src/components/TopTaskBar.tsx\");\n/* harmony import */ var _components_SideTaskBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/SideTaskBar */ \"./src/components/SideTaskBar.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    var _storesData_data;\n    _s();\n    const { user, isLoading, error, refresh, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { autoSelectFirstStore } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_5__.useStore)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [storesData, setStoresData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [storesLoading, setStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasCheckedStores, setHasCheckedStores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check for auth success\n        if (router.query.auth === \"success\") {\n            // Remove the query param from URL\n            router.replace(\"/dashboard\", undefined, {\n                shallow: true\n            });\n        }\n        // Fetch user data\n        refresh();\n    }, [\n        router.query.auth,\n        refresh\n    ]);\n    // Fetch stores when user is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            console.log(\"Fetching stores for user:\", user.id);\n            setStoresLoading(true);\n            _utils_api__WEBPACK_IMPORTED_MODULE_6__.storeApi.getByUserId(user.id, {\n                page: 1,\n                limit: 100\n            }).then((data)=>{\n                var _data_data;\n                console.log(\"Stores data received:\", data);\n                setStoresData(data);\n                // Auto-select the first store if available\n                if ((data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.length) > 0) {\n                    autoSelectFirstStore(data.data);\n                }\n                setHasCheckedStores(true);\n            }).catch((error)=>{\n                console.error(\"Failed to fetch stores:\", error);\n                setHasCheckedStores(true);\n            }).finally(()=>{\n                setStoresLoading(false);\n            });\n        }\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Fallback: if authenticated here and have zero stores, send to setup\n    // Only redirect if we've actually checked for stores and found none\n    // AND we're not coming from store creation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && user && !storesLoading && hasCheckedStores) {\n            var _storesData_meta, _storesData_data;\n            var _storesData_meta_total, _ref;\n            const totalStores = (_ref = (_storesData_meta_total = storesData === null || storesData === void 0 ? void 0 : (_storesData_meta = storesData.meta) === null || _storesData_meta === void 0 ? void 0 : _storesData_meta.total) !== null && _storesData_meta_total !== void 0 ? _storesData_meta_total : storesData === null || storesData === void 0 ? void 0 : (_storesData_data = storesData.data) === null || _storesData_data === void 0 ? void 0 : _storesData_data.length) !== null && _ref !== void 0 ? _ref : 0;\n            const isFromStoreCreation = router.query.from === \"store-creation\";\n            const hasStoreCreationFlag = (()=>{\n                try {\n                    return localStorage.getItem(\"teno:store-creation-complete\") === \"true\";\n                } catch (e) {\n                    return false;\n                }\n            })();\n            console.log(\"Dashboard redirect check:\", {\n                totalStores,\n                isFromStoreCreation,\n                hasStoreCreationFlag,\n                storesData,\n                hasCheckedStores\n            });\n            // Only redirect to setup if user has no active stores\n            if (totalStores === 0 && !isFromStoreCreation && !hasStoreCreationFlag) {\n                console.log(\"Redirecting to setup store - no active stores found\");\n                router.replace(\"/setup/store\");\n            } else if (totalStores > 0) {\n                console.log(\"Active stores found, staying on dashboard\");\n                // Clear the store creation flag since we have stores now\n                try {\n                    localStorage.removeItem(\"teno:store-creation-complete\");\n                } catch (e) {}\n            } else if (isFromStoreCreation || hasStoreCreationFlag) {\n                console.log(\"Coming from store creation, staying on dashboard\");\n                // Clear the store creation flag\n                try {\n                    localStorage.removeItem(\"teno:store-creation-complete\");\n                } catch (e) {}\n            }\n        }\n    }, [\n        isLoading,\n        user,\n        storesLoading,\n        storesData,\n        router,\n        hasCheckedStores\n    ]);\n    // Clean up the 'from' query parameter after processing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (router.query.from === \"store-creation\") {\n            // Remove the query param from URL to keep it clean\n            router.replace(\"/dashboard\", undefined, {\n                shallow: true\n            });\n        }\n    }, [\n        router.query.from,\n        router\n    ]);\n    // Clean up localStorage flag when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            try {\n                localStorage.removeItem(\"teno:store-creation-complete\");\n            } catch (e) {}\n        };\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    const handleLogout = async ()=>{\n        await logout();\n        router.push(\"/\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Authentication Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleLogout,\n                        className: \"bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700\",\n                        children: \"Logout\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        router.push(\"/login\");\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Dashboard - Teno Store\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Manage your stores and business\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TopTaskBar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SideTaskBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-2\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        user.name,\n                                                        \"!\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        storesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center py-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this) : (storesData === null || storesData === void 0 ? void 0 : (_storesData_data = storesData.data) === null || _storesData_data === void 0 ? void 0 : _storesData_data.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                            children: storesData.data.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer\",\n                                                    onClick: ()=>router.push(\"/store/\".concat(store.id)),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                            children: store.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: store.description || \"No description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Currency: \",\n                                                                        store.currency\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Language: \",\n                                                                        store.preferredLanguage\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, store.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"No stores found. Let's create your first store!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/setup/store\"),\n                                                    className: \"bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors\",\n                                                    children: \"Create Store\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Dashboard, \"/dJ/hPEvsrXFHKorOTxSk3Rj64s=\", false, function() {\n    return [\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _context_StoreContext__WEBPACK_IMPORTED_MODULE_5__.useStore,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/dashboard.tsx\n"));

/***/ })

});