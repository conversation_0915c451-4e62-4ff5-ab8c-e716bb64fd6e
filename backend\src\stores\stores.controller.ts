import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { StoresService } from './stores.service';
import { Store } from './store.entity';
import { PaginatedResponse, createPaginationMeta } from '../types/pagination';

// Define the DTO interface here to match the service
interface CreateStoreDto {
  name: string;
  description?: string;
  currency?: string;
  preferredLanguage?: string;
  userId: string | number;
}

@ApiTags('stores')
@Controller('stores')
export class StoresController {
  constructor(private readonly storesService: StoresService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new store' })
  @ApiResponse({ status: 201, description: 'Store created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createStoreDto: CreateStoreDto) {
    return this.storesService.create(createStoreDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all stores' })
  @ApiResponse({ status: 200, description: 'Stores retrieved successfully' })
  async findAll(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10'
  ): Promise<PaginatedResponse<Store>> {
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 10;

    const stores = await this.storesService.findAll();
    const total = stores.length;

    // Apply pagination to the results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedStores = stores.slice(startIndex, endIndex);

    return {
      data: paginatedStores,
      meta: createPaginationMeta(total, pageNum, limitNum)
    };
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get stores by user ID' })
  @ApiResponse({ status: 200, description: 'Stores retrieved successfully' })
  async findByUserId(
    @Param('userId') userId: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10'
  ): Promise<PaginatedResponse<Store>> {
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 10;

    const stores = await this.storesService.findByUserId(userId);
    const total = stores.length;

    // Apply pagination to the results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedStores = stores.slice(startIndex, endIndex);

    return {
      data: paginatedStores,
      meta: createPaginationMeta(total, pageNum, limitNum)
    };
  }

  @Get('uuid/:uuid')
  @ApiOperation({ summary: 'Get store by UUID' })
  @ApiResponse({ status: 200, description: 'Store retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  findByUuid(@Param('uuid') uuid: string) {
    return this.storesService.findByUuid(uuid);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a store by ID' })
  @ApiResponse({ status: 200, description: 'Store retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  findOne(@Param('id') id: string) {
    return this.storesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a store' })
  @ApiResponse({ status: 200, description: 'Store updated successfully' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  update(@Param('id') id: string, @Body() updateStoreDto: Partial<Store>) {
    return this.storesService.update(id, updateStoreDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a store' })
  @ApiResponse({ status: 200, description: 'Store deleted successfully' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  remove(@Param('id') id: string) {
    return this.storesService.remove(id);
  }
}
