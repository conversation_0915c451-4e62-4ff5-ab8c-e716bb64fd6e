import { LlmApi } from '../src/llm-api';
import { <PERSON><PERSON><PERSON><PERSON> } from '../src/llm-provider';
import { InfoAgent } from './info-agent';

async function demonstrateTimingAndCostTracking() {
  // Set up the LLM provider
  LLMProvider.setLlmProvider({
    apiKey: process.env.OPENAI_API_KEY || 'your-api-key-here',
    model: 'gpt-4o-mini'
  });

  // Create an agent
  const agent = new InfoAgent();

  // Sample conversation
  const messages = [
    { role: 'user' as const, content: 'What is the current time and date?' }
  ];

  console.log('🚀 Starting agent conversation with timing and cost tracking...\n');

  try {
    // Generate response
    const result = await LlmApi.generateLlmResponse(agent, messages);

    console.log('\n📊 RESPONSE ANALYSIS:');
    console.log('=' .repeat(50));
    
    // Access detailed timing information
    console.log(`\n⏱️  TIMING BREAKDOWN:`);
    console.log(`Total LLM Execution Time: ${result.totalLlmExecutionTime}ms`);
    console.log(`Total Tool Execution Time: ${result.totalExecutionTime}ms`);
    
    // Access individual call details
    const initialCall = result.initialCall;
    const followupCall = result.followupCall;
    
    if (initialCall) {
      console.log(`\n🔵 INITIAL CALL:`);
      console.log(`  Duration: ${initialCall.executionTime}ms`);
      console.log(`  Tokens: ${initialCall.inputTokens} in, ${initialCall.outputTokens} out`);
      console.log(`  Cost: $${initialCall.cost.toFixed(6)}`);
      console.log(`  Started: ${new Date(initialCall.startTime).toLocaleTimeString()}`);
    }
    
    if (followupCall) {
      console.log(`\n🟢 FOLLOWUP CALL:`);
      console.log(`  Duration: ${followupCall.executionTime}ms`);
      console.log(`  Tokens: ${followupCall.inputTokens} in, ${followupCall.outputTokens} out`);
      console.log(`  Cost: $${followupCall.cost.toFixed(6)}`);
      console.log(`  Started: ${new Date(followupCall.startTime).toLocaleTimeString()}`);
    }
    
    // Access all call details
    console.log(`\n📋 ALL CALL DETAILS:`);
    result.llmCallDetails.forEach((call, index) => {
      console.log(`  Call ${index + 1} (${call.callType}): ${call.executionTime}ms, $${call.cost.toFixed(6)}`);
    });
    
    // Cost summary
    console.log(`\n💰 COST SUMMARY:`);
    console.log(`Total Cost: $${result.totalCost.toFixed(6)}`);
    console.log(`Total Tokens: ${result.totalInputTokens + result.totalOutputTokens}`);
    
    // Response content
    console.log(`\n💬 AGENT RESPONSE:`);
    console.log(result.content);
    
  } catch (error) {
    console.error('❌ Error during conversation:', error);
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  demonstrateTimingAndCostTracking().catch(console.error);
}

export { demonstrateTimingAndCostTracking };
