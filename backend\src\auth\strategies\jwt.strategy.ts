import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../users/users.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private usersService: UsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET') || 'your-secret-key',
    });
    
    console.log('[JWT Strategy] Initialized with secret:', this.configService.get<string>('JWT_SECRET')?.substring(0, 10) + '...');
  }

  async validate(payload: any) {
    console.log('[JWT Strategy] Validating payload:', payload);
    console.log('[JWT Strategy] payload.sub:', payload.sub);
    console.log('[JWT Strategy] payload.email:', payload.email);
    
    try {
      const user = await this.usersService.findOne(payload.sub);
      console.log('[JWT Strategy] User found:', user ? { id: user.id, email: user.email, isDeleted: user.isDeleted } : 'null');
      
      if (!user || user.isDeleted) {
        console.log('[JWT Strategy] User not found or deleted, throwing UnauthorizedException');
        throw new UnauthorizedException();
      }
      
      console.log('[JWT Strategy] User validated successfully, returning:', { id: user.id, email: user.email, name: user.name, image: user.image });
      return { id: user.id, email: user.email, name: user.name, image: user.image };
    } catch (error) {
      console.error('[JWT Strategy] Error during validation:', error);
      throw new UnauthorizedException();
    }
  }
}
