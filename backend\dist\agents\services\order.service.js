"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderPriorityService = exports.OrderStatusService = void 0;
exports.createOrder = createOrder;
exports.updateOrder = updateOrder;
exports.checkOrderUnderwayByPhone = checkOrderUnderwayByPhone;
const orders_service_1 = require("../../orders/orders.service");
Object.defineProperty(exports, "OrderStatusService", { enumerable: true, get: function () { return orders_service_1.OrderStatusService; } });
Object.defineProperty(exports, "OrderPriorityService", { enumerable: true, get: function () { return orders_service_1.OrderPriorityService; } });
async function createOrder(input, db) {
    const orderRepo = db.getRepository('Order');
    const ordersService = new orders_service_1.OrdersService(orderRepo, db.getRepository('OrderItem'));
    const serviceInput = {
        status: input.status,
        priority: input.priority,
        useTax: input.useTax,
        taxRate: input.taxRate,
        orderDate: input.orderDate,
        expectedDeliveryDate: input.expectedDeliveryDate,
        preferredDeliveryLocation: input.preferredDeliveryLocation,
        userId: input.userId,
        storeId: input.storeId.toString(),
        customerId: input.customerId,
        createdBy: input.createdBy,
        items: input.items.map(item => ({
            productId: item.productId,
            productName: item.productName,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            taxAmount: item.taxAmount || 0,
        })),
    };
    return ordersService.createOrder(serviceInput);
}
async function updateOrder(input, db) {
    const orderRepo = db.getRepository('Order');
    const ordersService = new orders_service_1.OrdersService(orderRepo, db.getRepository('OrderItem'));
    const serviceInput = {
        id: input.id,
        status: input.status,
        priority: input.priority,
        useTax: input.useTax,
        taxRate: input.taxRate,
        orderDate: input.orderDate,
        expectedDeliveryDate: input.expectedDeliveryDate,
        preferredDeliveryLocation: input.preferredDeliveryLocation,
        cancellationReason: input.cancellationReason,
        updatedBy: input.updatedBy,
        items: input.items?.map(item => ({
            productId: item.productId,
            productName: item.productName,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            taxAmount: item.taxAmount || 0,
        })),
    };
    return ordersService.updateOrder(serviceInput);
}
async function checkOrderUnderwayByPhone(phone, db) {
    const orderRepo = db.getRepository('Order');
    const customerRepo = db.getRepository('Customer');
    const customer = await customerRepo.findOne({
        where: { phone, isDeleted: false }
    });
    if (!customer) {
        return null;
    }
    const underwayStatuses = [
        orders_service_1.OrderStatusService.DRAFT,
        orders_service_1.OrderStatusService.PENDING,
        orders_service_1.OrderStatusService.CONFIRMED,
        orders_service_1.OrderStatusService.PROCESSING,
    ];
    const order = await orderRepo.findOne({
        where: {
            customerId: customer.id,
            status: underwayStatuses,
            isDeleted: false,
        },
        relations: ['customer'],
        order: { createdAt: 'DESC' }
    });
    return order;
}
//# sourceMappingURL=order.service.js.map