# Dido Agent SDK (TypeScript)

A TypeScript/Node.js framework for building LLM-powered agents with tool-calling capabilities.

## Features

- **Simple Agent Creation**: Easy-to-use API for defining agents with custom instructions
- **Tool Integration**: Seamless integration of custom tools and functions
- **Type Safety**: Full TypeScript support with proper type definitions
- **Pre-built Agents**: Ready-to-use agents for common tasks
- **OpenAI Compatible**: Works with OpenAI API and compatible providers
- **Flexible Configuration**: Environment variables or programmatic setup
- **Detailed Metrics**: Comprehensive tracking of LLM call timing, costs, and token usage
- **Performance Monitoring**: Real-time insights into agent performance and efficiency

## Quick Start

### Installation

```bash
npm install
```

### Basic Usage

```typescript
import { Agent, Runner, createTool, setLlmProvider } from 'dido-agent-sdk';

// Configure the LLM provider
setLlmProvider('your-api-key');

// Create a simple tool
const calculator = createTool(
  function add(a: number, b: number): number {
    return a + b;
  },
  {
    description: 'Add two numbers',
    parameterTypes: { a: Number, b: Number },
    requiredParams: ['a', 'b']
  }
);

// Create an agent
const agent = new Agent(
  'MathAgent',
  'You help with mathematical calculations.',
  [calculator]
);

// Run the agent
const result = await Runner.run(agent, 'What is 5 + 3?');
console.log(result);
```

### Pre-built Agents

```typescript
import { createInfoAgent, createSaleAgent, Runner } from 'dido-agent-sdk';

// Information agent with web search capabilities
const infoAgent = createInfoAgent();
await Runner.run(infoAgent, 'What is the current time?');

// Sales agent for product management
const saleAgent = createSaleAgent();
await Runner.run(saleAgent, 'Search for electronics products');
```

## Configuration

### Environment Variables

```bash
API_KEY=your_api_key_here
BASE_URL=https://openrouter.ai/api/v1       # Optional, defaults to OpenRouter
MODEL=google/gemini-2.5-flash-lite         # Optional, model to use
```

### Programmatic Configuration

```typescript
import { setLlmProvider } from 'dido-agent-sdk';

setLlmProvider('your-api-key', 'https://custom-endpoint.com/v1');
```

## Development

### Build

```bash
npm run build
```

### Development Mode

```bash
npm run dev
```

### Examples

#### Basic Usage
```typescript
import { LlmApi } from './src/llm-api';
import { InfoAgent } from './examples/info-agent';

const agent = new InfoAgent();
const result = await LlmApi.generateLlmResponse(agent, [
  { role: 'user', content: 'What is the current time?' }
]);

// Access detailed metrics
console.log(`Total LLM execution time: ${result.totalLlmExecutionTime}ms`);
console.log(`Total cost: $${result.totalCost.toFixed(6)}`);
console.log(`Total tokens: ${result.totalInputTokens + result.totalOutputTokens}`);
```

#### Detailed Metrics and Timing
```typescript
// Access individual call details
const initialCall = result.initialCall;
const followupCall = result.followupCall;

if (initialCall) {
  console.log(`Initial call took ${initialCall.executionTime}ms`);
  console.log(`Cost: $${initialCall.cost.toFixed(6)}`);
  console.log(`Tokens: ${initialCall.inputTokens} in, ${initialCall.outputTokens} out`);
}

// Get all call details
result.llmCallDetails.forEach(call => {
  console.log(`${call.callType}: ${call.executionTime}ms, $${call.cost.toFixed(6)}`);
});
```

#### Performance Monitoring
```typescript
// Monitor agent performance over time
const metrics = {
  totalLlmTime: result.totalLlmExecutionTime,
  totalToolTime: result.totalExecutionTime,
  totalCost: result.totalCost,
  efficiency: result.totalLlmExecutionTime / (result.totalLlmExecutionTime + result.totalExecutionTime)
};

console.log('Performance metrics:', metrics);
```

See the `examples/` directory for comprehensive usage examples.

## Documentation

- **[Usage Guide](./AGENT_SDK_USAGE.md)**: Detailed guide with examples
- **[API Reference](./src/)**: Type definitions and interfaces
- **[Examples](./examples/)**: Complete working examples

## Architecture

```
src/
├── agent.ts           # Core Agent class
├── runner.ts          # Agent execution engine
├── tools.ts           # Tool creation utilities
├── llm-provider.ts    # LLM configuration
├── schema.ts          # Type schema generation
├── types.ts           # TypeScript interfaces
└── agents/            # Pre-built agent implementations
    ├── info-agent.ts
    └── sale-agent.ts
```

## Migration from Python

This TypeScript version is a complete rewrite of the original Python Agent SDK. Key differences:

- **Async/Await**: All operations are asynchronous
- **Type Safety**: Full TypeScript support
- **Modern APIs**: Uses modern Node.js and npm ecosystem
- **Class-based**: Object-oriented design with classes
- **Tool Decorators**: Support for both functional and decorator-based tool definition

## License

MIT
