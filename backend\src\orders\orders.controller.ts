import { Controller, Get, Post, Body, Param, Query, Put, Delete } from '@nestjs/common';
import { OrdersService, CreateOrderInput, UpdateOrderInput, OrderFilter } from './orders.service';

@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  async createOrder(@Body() createOrderInput: CreateOrderInput) {
    return this.ordersService.createOrder(createOrderInput);
  }

  @Get()
  async getOrders(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Query('storeId') storeId?: string,
    @Query('userId') userId?: string,
    @Query('customerId') customerId?: string,
    @Query('status') status?: string,
    @Query('priority') priority?: string,
  ) {
    const filter: OrderFilter = {
      storeId,
      userId,
      customerId,
      status: status as any,
      priority: priority as any,
    };
    
    return this.ordersService.findOrders(filter, parseInt(page), parseInt(limit));
  }

  @Get(':id')
  async getOrder(@Param('id') id: string) {
    return this.ordersService.findById(id);
  }

  @Get('order-number/:orderNumber')
  async getOrderByNumber(@Param('orderNumber') orderNumber: string) {
    return this.ordersService.findByOrderNumber(orderNumber);
  }

  @Put(':id')
  async updateOrder(@Param('id') id: string, @Body() updateOrderInput: UpdateOrderInput) {
    updateOrderInput.id = id;
    return this.ordersService.updateOrder(updateOrderInput);
  }

  @Delete(':id')
  async deleteOrder(@Param('id') id: string, @Body('userId') userId: string) {
    return this.ordersService.deleteOrder(id, userId);
  }

  @Get('stats/:storeId')
  async getOrderStats(
    @Param('storeId') storeId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    return this.ordersService.getOrderStats(storeId, start, end);
  }
}
