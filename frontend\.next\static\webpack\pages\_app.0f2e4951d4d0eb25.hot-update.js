"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/utils/auth.ts":
/*!***************************!*\
  !*** ./src/utils/auth.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearClientAuthArtifacts: function() { return /* binding */ clearClientAuthArtifacts; },\n/* harmony export */   debugLogin: function() { return /* binding */ debugLogin; },\n/* harmony export */   fetchWithCredentials: function() { return /* binding */ fetchWithCredentials; },\n/* harmony export */   getBackendUrl: function() { return /* binding */ getBackendUrl; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   handleUnauthorized: function() { return /* binding */ handleUnauthorized; },\n/* harmony export */   performLogout: function() { return /* binding */ performLogout; },\n/* harmony export */   redirectToGoogleAuth: function() { return /* binding */ redirectToGoogleAuth; }\n/* harmony export */ });\nconst getBackendUrl = ()=>{\n    return \"http://localhost:8000\" || 0;\n};\nconst clearClientAuthArtifacts = ()=>{\n    console.log(\"[clearClientAuthArtifacts] Clearing all auth artifacts\");\n    try {\n        // Clear local cache of user\n        if (typeof localStorage !== \"undefined\") {\n            localStorage.removeItem(\"teno:auth:user\");\n            localStorage.removeItem(\"teno:auth:token\");\n            console.log(\"[clearClientAuthArtifacts] Cleared localStorage tokens\");\n        }\n    } catch (e) {}\n    try {\n        // Proactively drop any readable client token if it exists\n        if (typeof document !== \"undefined\") {\n            // Expire both potential names just in case\n            document.cookie = \"access_token_client=; Path=/; Max-Age=0; SameSite=Lax\";\n            document.cookie = \"access_token=; Path=/; Max-Age=0; SameSite=Lax\";\n            console.log(\"[clearClientAuthArtifacts] Cleared cookie tokens\");\n        }\n    } catch (e) {}\n};\nconst handleUnauthorized = ()=>{\n    console.log(\"[handleUnauthorized] Called - clearing auth artifacts and redirecting to login\");\n    // Ensure client artifacts are cleared immediately\n    clearClientAuthArtifacts();\n    try {\n        // Notify any listeners (e.g., UI) that auth state became unauthorized\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"teno:auth:unauthorized\"));\n        }\n    } catch (e) {}\n    // Best-effort redirect to login preserving next path\n    try {\n        if (true) {\n            const next = encodeURIComponent(window.location.pathname + window.location.search);\n            const target = \"/login?loggedOut=1&next=\".concat(next);\n            // Avoid infinite loops if we are already on login\n            if (!window.location.pathname.startsWith(\"/login\")) {\n                console.log(\"[handleUnauthorized] Redirecting to:\", target);\n                window.location.replace(target);\n            }\n        }\n    } catch (e) {}\n};\nconst fetchWithCredentials = async (input, init)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...init && init.headers ? init.headers : {}\n    };\n    // Try to get token from multiple sources in order of preference\n    try {\n        if (typeof document !== \"undefined\" && !headers[\"Authorization\"]) {\n            let token;\n            // First try cookie (primary method)\n            const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);\n            if (cookieMatch) {\n                token = decodeURIComponent(cookieMatch[1]);\n                console.log(\"[fetchWithCredentials] Token found in cookie:\", token.substring(0, 20) + \"...\");\n            } else {\n                console.log(\"[fetchWithCredentials] No token found in cookie\");\n            }\n            // Fallback to localStorage if cookie not found\n            if (!token) {\n                try {\n                    token = localStorage.getItem(\"teno:auth:token\") || undefined;\n                    if (token) {\n                        console.log(\"[fetchWithCredentials] Token found in localStorage:\", token.substring(0, 20) + \"...\");\n                    } else {\n                        console.log(\"[fetchWithCredentials] No token found in localStorage\");\n                    }\n                } catch (e) {\n                    console.warn(\"Could not access localStorage:\", e);\n                }\n            }\n            if (token) {\n                headers[\"Authorization\"] = \"Bearer \".concat(token);\n                console.log(\"[fetchWithCredentials] Token added to Authorization header\");\n            } else {\n                console.log(\"[fetchWithCredentials] No token available for Authorization header\");\n            }\n        }\n    } catch (e) {\n        console.warn(\"Error getting auth token:\", e);\n    }\n    const response = await fetch(input, {\n        ...init,\n        credentials: \"include\",\n        headers\n    });\n    // If backend says unauthorized/forbidden, clear local auth and nudge UI\n    if (response.status === 401 || response.status === 403) {\n        handleUnauthorized();\n    }\n    return response;\n};\nconst getCurrentUser = async ()=>{\n    // Check if we have a token before making the API call\n    let hasToken = false;\n    try {\n        if (typeof document !== \"undefined\") {\n            const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);\n            if (cookieMatch) {\n                hasToken = true;\n            }\n        }\n        if (!hasToken && typeof localStorage !== \"undefined\") {\n            try {\n                const token = localStorage.getItem(\"teno:auth:token\");\n                if (token) {\n                    hasToken = true;\n                }\n            } catch (e) {\n                console.warn(\"Could not access localStorage:\", e);\n            }\n        }\n    } catch (e) {\n        console.warn(\"Error checking for token:\", e);\n    }\n    if (!hasToken) {\n        console.log(\"[getCurrentUser] No token found, skipping API call\");\n        return null;\n    }\n    const url = \"\".concat(getBackendUrl(), \"/auth/me\");\n    console.log(\"[getCurrentUser] Making API call to:\", url);\n    const response = await fetchWithCredentials(url);\n    if (!response.ok) {\n        console.log(\"[getCurrentUser] API call failed with status:\", response.status);\n        return null;\n    }\n    const data = await response.json();\n    console.log(\"[getCurrentUser] API call successful, user:\", data.user);\n    var _data_user;\n    return (_data_user = data.user) !== null && _data_user !== void 0 ? _data_user : null;\n};\nconst performLogout = async ()=>{\n    const url = \"\".concat(getBackendUrl(), \"/auth/logout\");\n    try {\n        await fetchWithCredentials(url, {\n            method: \"POST\"\n        });\n    } finally{\n        // Always clear client artifacts regardless of server response\n        clearClientAuthArtifacts();\n    }\n};\nconst redirectToGoogleAuth = (nextPath)=>{\n    let url = \"\".concat(getBackendUrl(), \"/auth/google\");\n    try {\n        // Prefer explicit nextPath, otherwise pick it up from current URL (?next=...)\n        let nextParam = nextPath;\n        if (!nextParam && \"object\" !== \"undefined\") {\n            const sp = new URLSearchParams(window.location.search);\n            const qp = sp.get(\"next\") || undefined;\n            nextParam = qp || undefined;\n        }\n        if (nextParam) {\n            // Only allow app-internal paths starting with '/'\n            const safeNext = decodeURIComponent(nextParam);\n            if (safeNext.startsWith(\"/\")) {\n                url += \"?next=\".concat(encodeURIComponent(safeNext));\n            }\n        }\n    } catch (e) {}\n    if (true) {\n        window.location.href = url;\n    }\n};\nconst debugLogin = async (email, name)=>{\n    const url = \"\".concat(getBackendUrl(), \"/auth/dev-login\");\n    console.log(\"[debugLogin] Attempting debug login for:\", email);\n    const response = await fetch(url, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email,\n            name\n        })\n    });\n    if (!response.ok) {\n        console.error(\"[debugLogin] Login failed with status:\", response.status);\n        return null;\n    }\n    const data = await response.json();\n    console.log(\"[debugLogin] Login successful, received data:\", data);\n    // Extract and store the token\n    if (data.access_token) {\n        console.log(\"[debugLogin] Storing access token\");\n        // Store in cookie\n        const isLocalhost =  true && (window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\");\n        const cookieOptions = isLocalhost ? \"path=/; max-age=86400; samesite=lax\" : \"path=/; max-age=86400; secure; samesite=strict\";\n        document.cookie = \"access_token_client=\".concat(encodeURIComponent(data.access_token), \"; \").concat(cookieOptions);\n        // Store in localStorage as backup\n        try {\n            localStorage.setItem(\"teno:auth:token\", data.access_token);\n        } catch (e) {\n            console.warn(\"[debugLogin] Could not store token in localStorage:\", e);\n        }\n        console.log(\"[debugLogin] Token stored successfully\");\n    } else {\n        console.warn(\"[debugLogin] No access_token in response\");\n    }\n    var _data_user;\n    return (_data_user = data.user) !== null && _data_user !== void 0 ? _data_user : null;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/auth.ts\n"));

/***/ }),

/***/ "./src/utils/useAuthGuard.ts":
/*!***********************************!*\
  !*** ./src/utils/useAuthGuard.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthGuard: function() { return /* binding */ useAuthGuard; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n\n\n\n\n// Routes that don't require authentication\nconst PUBLIC_ROUTES = [\n    \"/login\",\n    \"/auth/callback\",\n    \"/setup/store\"\n];\nfunction useAuthGuard() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, isLoading, error } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { currentStoreId, setCurrentStoreId } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const [storesData, setStoresData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isStoresLoading, setIsStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const handledCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const isRedirectingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isPublic = (path)=>PUBLIC_ROUTES.some((route)=>path.startsWith(route));\n    // Fetch stores data for the current user\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!user) return;\n        const fetchStores = async ()=>{\n            setIsStoresLoading(true);\n            try {\n                // Import the store API dynamically to avoid circular dependencies\n                const { storeApi } = await __webpack_require__.e(/*! import() */ \"src_utils_api_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../utils/api */ \"./src/utils/api.ts\"));\n                const result = await storeApi.getByUserId(user.id, {\n                    page: 1,\n                    limit: 100\n                });\n                setStoresData(result);\n            } catch (error) {\n                console.error(\"Error fetching stores:\", error);\n                setStoresData({\n                    data: [],\n                    meta: {\n                        total: 0\n                    }\n                });\n            } finally{\n                setIsStoresLoading(false);\n            }\n        };\n        fetchStores();\n    }, [\n        user\n    ]);\n    // Main auth guard logic\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Add a small delay to allow auth state to stabilize after token storage\n        const timeoutId = setTimeout(()=>{\n            // Avoid redirecting while we are still determining auth state\n            if (isLoading) return;\n            const currentPath = router.pathname;\n            const asPath = router.asPath;\n            const fullPath = asPath || currentPath;\n            const searchParams = new URLSearchParams( true ? window.location.search : 0);\n            const loggedOutFlag = searchParams.get(\"loggedOut\");\n            // Ensure we only handle/log once per path after loading has completed\n            if (handledCache.current[currentPath]) {\n                return;\n            }\n            console.log(\"[AuthGuard] route/useEffect fired\", {\n                pathname: currentPath,\n                asPath: router.asPath,\n                isLoading,\n                hasUser: !!user,\n                error\n            });\n            if (!user && !isPublic(currentPath)) {\n                if (isRedirectingRef.current) {\n                    console.log(\"[AuthGuard] Redirect already in progress; skipping\");\n                    return;\n                }\n                isRedirectingRef.current = true;\n                const nextParam = encodeURIComponent(fullPath || \"/\");\n                const loginUrl = \"/login?next=\".concat(nextParam);\n                console.log(\"[AuthGuard] Not authenticated; redirecting to\", loginUrl);\n                router.replace(loginUrl).finally(()=>{\n                    setTimeout(()=>{\n                        isRedirectingRef.current = false;\n                    }, 0);\n                });\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (isPublic(currentPath) && currentPath === \"/login\" && loggedOutFlag === \"1\") {\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && isPublic(currentPath) && currentPath === \"/login\") {\n                if (isStoresLoading) {\n                    return;\n                }\n                if (storesData === null) {\n                    return;\n                }\n                if (!isRedirectingRef.current) {\n                    isRedirectingRef.current = true;\n                    let target = \"/dashboard\";\n                    try {\n                        var _storesData_meta, _storesData_data;\n                        const nextParam = searchParams.get(\"next\") || undefined;\n                        var _storesData_meta_total, _ref;\n                        const totalActive = (_ref = (_storesData_meta_total = storesData === null || storesData === void 0 ? void 0 : (_storesData_meta = storesData.meta) === null || _storesData_meta === void 0 ? void 0 : _storesData_meta.total) !== null && _storesData_meta_total !== void 0 ? _storesData_meta_total : storesData === null || storesData === void 0 ? void 0 : (_storesData_data = storesData.data) === null || _storesData_data === void 0 ? void 0 : _storesData_data.length) !== null && _ref !== void 0 ? _ref : 0;\n                        if (nextParam && nextParam.startsWith(\"/\")) {\n                            target = nextParam;\n                        } else {\n                            if (totalActive === 0) {\n                                target = \"/setup/store\";\n                            }\n                        }\n                    } catch (e) {}\n                    console.log(\"[AuthGuard] Already authenticated; redirecting away from public auth page to\", target);\n                    router.replace(target).finally(()=>{\n                        setTimeout(()=>{\n                            isRedirectingRef.current = false;\n                        }, 0);\n                    });\n                }\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && !isPublic(currentPath)) {\n                var _storesData_meta1, _storesData_data1;\n                if (storesData === null) {\n                    console.log(\"[AuthGuard] Stores data not yet fetched, waiting...\");\n                    return;\n                }\n                var _storesData_meta_total1, _ref1;\n                const totalActive = (_ref1 = (_storesData_meta_total1 = storesData === null || storesData === void 0 ? void 0 : (_storesData_meta1 = storesData.meta) === null || _storesData_meta1 === void 0 ? void 0 : _storesData_meta1.total) !== null && _storesData_meta_total1 !== void 0 ? _storesData_meta_total1 : storesData === null || storesData === void 0 ? void 0 : (_storesData_data1 = storesData.data) === null || _storesData_data1 === void 0 ? void 0 : _storesData_data1.length) !== null && _ref1 !== void 0 ? _ref1 : 0;\n                console.log(\"[AuthGuard] Store check:\", {\n                    currentPath,\n                    totalActive,\n                    storesData,\n                    isStoresLoading,\n                    loggedOutFlag\n                });\n                if (!isStoresLoading && totalActive === 0 && currentPath !== \"/setup/store\" && loggedOutFlag !== \"1\") {\n                    if (!isRedirectingRef.current) {\n                        isRedirectingRef.current = true;\n                        console.log(\"[AuthGuard] Authenticated user without active stores; redirecting to /setup/store\");\n                        router.replace(\"/setup/store\").finally(()=>{\n                            setTimeout(()=>{\n                                isRedirectingRef.current = false;\n                            }, 0);\n                        });\n                        handledCache.current[currentPath] = true;\n                        return;\n                    }\n                } else if (totalActive > 0 && !currentStoreId) {\n                    var _storesData_data_, _storesData_data2;\n                    const firstId = storesData === null || storesData === void 0 ? void 0 : (_storesData_data2 = storesData.data) === null || _storesData_data2 === void 0 ? void 0 : (_storesData_data_ = _storesData_data2[0]) === null || _storesData_data_ === void 0 ? void 0 : _storesData_data_.id;\n                    if (firstId) setCurrentStoreId(String(firstId));\n                }\n            }\n            if (!(user && !isPublic(currentPath) && isStoresLoading)) {\n                handledCache.current[currentPath] = true;\n            }\n        }, 100); // 100ms delay to allow auth state to stabilize\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        router.pathname,\n        isLoading,\n        user,\n        error,\n        isStoresLoading,\n        storesData,\n        currentStoreId,\n        router,\n        setCurrentStoreId\n    ]);\n    return {\n        user,\n        isLoading,\n        error,\n        storesData,\n        isStoresLoading,\n        currentStoreId\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/useAuthGuard.ts\n"));

/***/ })

});