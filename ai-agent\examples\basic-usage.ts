/**
 * Basic usage examples for the Agent SDK
 */

import { config } from 'dotenv';
import { Agent, LlmApi, createTool, setLlmProvider, LLMProvider, setLlmProviderFromEnvironmentVariables } from '../src';
import { createInfoAgent } from './info-agent';
import { createSaleAgent } from './sale-agent';

// Load environment variables from .env file
config();
// Explicitly load provider config from environment variables
setLlmProviderFromEnvironmentVariables();

// Set up the LLM provider - this will use environment variables (API_KEY, BASE_URL, MODEL)
// Only set manually if not using environment variables
// setLlmProvider('your-api-key-here');

/**
 * Example 1: Simple calculator tool
 */
function createCalculatorAgent(): Agent {
  const calculator = createTool(
    function calculate(expression: string): number {
      try {
        // Simple evaluation (in production, use a proper math parser)
        return Function(`"use strict"; return (${expression})`)();
      } catch (error) {
        throw new Error(`Invalid expression: ${expression}`);
      }
    },
    {
      description: 'Calculate mathematical expressions',
      parameterTypes: { expression: String },
      requiredParams: ['expression']
    }
  );

  return new Agent(
    'CalculatorAgent',
    'You are a calculator assistant. Help users with mathematical calculations.',
    [calculator]
  );
}

/**
 * Example usage functions
 */
async function runCalculatorExample() {
  const agent = createCalculatorAgent();
  const result = await LlmApi.generateLlmResponse(agent, [{ role: 'user', content: 'What is 15 * 8 + 23?' }]);
  console.log('Calculator result:', result.content);
  if (result.hasToolCalls) {
    console.log('Tool calls made:', result.calls.length);
    result.calls.forEach(call => {
      console.log(`  - ${call.function?.name}: ${call.function?.arguments}`);
    });
    console.log('Execution summary:');
    console.log(`  Total execution time: ${result.totalExecutionTime}ms`);
    console.log(`  Successful: ${result.successfulExecutions.length}, Failed: ${result.failedExecutions.length}`);
  }
  console.log('Cost summary:');
  console.log(`  Model: ${result.llmCosts.model}`);
  console.log(`  Total cost: $${result.totalCost.toFixed(6)}`);
  console.log(`  Input tokens: ${result.totalInputTokens.toLocaleString()}`);
  console.log(`  Output tokens: ${result.totalOutputTokens.toLocaleString()}`);
}

async function runInfoAgentExample() {
  const agent = createInfoAgent();
  const result = await LlmApi.generateLlmResponse(agent, [{ role: 'user', content: 'What is the current time?' }]);
  console.log('Info agent result:', result.content);
  if (result.hasToolCalls) {
    console.log('Tool calls made:', result.calls.length);
    result.calls.forEach(call => {
      console.log(`  - ${call.function?.name}: ${call.function?.arguments}`);
    });
    console.log('Execution summary:');
    console.log(`  Total execution time: ${result.totalExecutionTime}ms`);
    console.log(`  Successful: ${result.successfulExecutions.length}, Failed: ${result.failedExecutions.length}`);
  }
  console.log('Cost summary:');
  console.log(`  Model: ${result.llmCosts.model}`);
  console.log(`  Total cost: $${result.totalCost.toFixed(6)}`);
  console.log(`  Input tokens: ${result.totalInputTokens.toLocaleString()}`);
  console.log(`  Output tokens: ${result.totalOutputTokens.toLocaleString()}`);
}

async function runSaleAgentExample() {
  const agent = createSaleAgent();
  const result = await LlmApi.generateLlmResponse(agent, [{ role: 'user', content: 'Search for products related to "electronics"' }]);
  console.log('Sale agent result:', result.content);
  if (result.hasToolCalls) {
    console.log('Tool calls made:', result.calls.length);
    result.calls.forEach(call => {
      console.log(`  - ${call.function?.name}: ${call.function?.arguments}`);
    });
    console.log('Execution summary:');
    console.log(`  Total execution time: ${result.totalExecutionTime}ms`);
    console.log(`  Successful: ${result.successfulExecutions.length}, Failed: ${result.failedExecutions.length}`);
  }
  console.log('Cost summary:');
  console.log(`  Model: ${result.llmCosts.model}`);
  console.log(`  Total cost: $${result.totalCost.toFixed(6)}`);
  console.log(`  Input tokens: ${result.totalInputTokens.toLocaleString()}`);
  console.log(`  Output tokens: ${result.totalOutputTokens.toLocaleString()}`);
}

/**
 * Example with custom tools using function approach (decorators need different setup)
 */
class WeatherService {
  async getWeather(city: string): Promise<string> {
    // Mock weather data - in production, use a real weather API
    const weather = {
      temperature: Math.floor(Math.random() * 30) + 10,
      condition: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)]
    };
    
    return `Weather in ${city}: ${weather.temperature}°C, ${weather.condition}`;
  }
}

async function runWeatherAgentExample() {
  const weatherService = new WeatherService();
  
  // Create tool from the method
  const weatherTool = createTool(
    function get_weather(city: string): Promise<string> {
      return weatherService.getWeather(city);
    },
    {
      description: 'Get weather information for a city',
      parameterTypes: { city: String },
      requiredParams: ['city']
    }
  );
  
  const agent = new Agent(
    'WeatherAgent',
    'You are a weather assistant. Provide weather information for cities.',
    [weatherTool]
  );
  
  const result = await LlmApi.generateLlmResponse(agent, [{ role: 'user', content: 'What is the weather like in London?' }]);
  console.log('Weather agent result:', result.content);
  if (result.hasToolCalls) {
    console.log('Tool calls made:', result.calls.length);
    result.calls.forEach(call => {
      console.log(`  - ${call.function?.name}: ${call.function?.arguments}`);
    });
    console.log('Tool outputs:');
    result.outputs.forEach(output => {
      console.log(`  - ${output.tool_call_id}: ${output.content}`);
    });
    console.log('Execution summary:');
    console.log(`  Total execution time: ${result.totalExecutionTime}ms`);
    console.log(`  Successful: ${result.successfulExecutions.length}, Failed: ${result.failedExecutions.length}`);
  }
  console.log('Cost summary:');
  console.log(`  Model: ${result.llmCosts.model}`);
  console.log(`  Total cost: $${result.totalCost.toFixed(6)}`);
  console.log(`  Input tokens: ${result.totalInputTokens.toLocaleString()}`);
  console.log(`  Output tokens: ${result.totalOutputTokens.toLocaleString()}`);
}

// Main execution
async function main() {
  try {
    console.log('Running Agent SDK examples...\n');
    
    // Check if API key is configured
    if (!LLMProvider.apiKey) {
      console.error('❌ Error: API_KEY environment variable is not set!');
      console.log('📝 Please create a .env file with:');
      console.log('   API_KEY=your_actual_api_key_here');
      console.log('   BASE_URL=https://openrouter.ai/api/v1');
      console.log('   MODEL=google/gemini-2.5-flash-lite');
      console.log('\n💡 You can copy .env_example to .env and edit it with your API key.');
      return;
    }
    
    console.log('✅ API_KEY found, running examples...\n');
    
    // Show debugging information
    console.log('🔧 Configuration:');
    console.log(`   API_KEY: ${LLMProvider.apiKey ? LLMProvider.apiKey.substring(0, 8) + '...' : 'Not set'}`);
    console.log(`   BASE_URL: ${LLMProvider.baseUrl || 'Default OpenAI URL'}`);
    console.log(`   MODEL: ${LLMProvider.model || 'gpt-4o-mini (default)'}`);
    console.log('');
    
    await runCalculatorExample();
    console.log('---');
    
    await runInfoAgentExample();
    console.log('---');
    
    await runWeatherAgentExample();
    console.log('---');
    
    // Note: Sale agent example requires backend API to be running
    // await runSaleAgentExample();
    
  } catch (error) {
    console.error('Error running examples:', error);
  }
}

// Run examples
main();
