import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(private configService: ConfigService) {
    super({
      clientID: configService.get<string>('GOOGLE_CLIENT_ID'),
      clientSecret: configService.get<string>('GOOGLE_CLIENT_SECRET'),
      callbackURL: configService.get<string>('GOOGLE_CALLBACK_URL') || 'http://localhost:8000/auth/google/callback',
      scope: ['email', 'profile'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    const { name, emails, photos } = profile;
    
    // Safely extract email with fallback
    const email = emails && emails.length > 0 ? emails[0].value : null;
    if (!email) {
      return done(new Error('Email is required for Google OAuth'), null);
    }
    
    // Safely extract name with fallback
    let displayName = 'Unknown User';
    if (name) {
      if (name.givenName && name.familyName) {
        displayName = `${name.givenName} ${name.familyName}`;
      } else if (name.givenName) {
        displayName = name.givenName;
      } else if (name.familyName) {
        displayName = name.familyName;
      }
    }
    
    // Safely extract photo with fallback
    const picture = photos && photos.length > 0 ? photos[0].value : null;
    
    const user = {
      id: profile.id,
      email,
      name: displayName,
      picture,
    };
    
    done(null, user);
  }
}
