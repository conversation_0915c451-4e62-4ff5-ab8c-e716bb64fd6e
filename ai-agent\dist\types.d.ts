/**
 * Common types used throughout the Agent SDK
 */
import type { ChatCompletionTool, ChatCompletionMessageParam, ChatCompletionMessageToolCall } from 'openai/resources/chat/completions';
export interface ToolSpec extends ChatCompletionTool {
}
export interface JsonSchema {
    type: string;
    properties?: Record<string, JsonSchema>;
    required?: string[];
    items?: JsonSchema;
    default?: any;
    [key: string]: any;
}
export interface Message {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content?: string | null;
    tool_calls?: ToolCall[];
    tool_call_id?: string;
}
export interface ToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}
export interface ToolOutput {
    role: 'tool';
    content: string;
    tool_call_id: string;
}
export type ToolFunction = (...args: any[]) => any;
/**
 * Result class that holds both the LLM message response and tool calls
 */
export declare class LlmResponseResult {
    readonly messageResponse: string;
    readonly toolCalls: ChatCompletionMessageToolCall[];
    readonly toolOutputs: ToolOutput[];
    readonly executionDetails: ToolExecutionDetail[];
    readonly llmCosts: LlmCostInfo;
    constructor(messageResponse: string, toolCalls: ChatCompletionMessageToolCall[], toolOutputs: ToolOutput[], executionDetails: ToolExecutionDetail[], llmCosts: LlmCostInfo);
    /**
     * Get the message response content
     */
    get content(): string;
    /**
     * Get all tool calls made by the LLM
     */
    get calls(): ChatCompletionMessageToolCall[];
    /**
     * Get all tool outputs/results
     */
    get outputs(): ToolOutput[];
    /**
     * Check if any tool calls were made
     */
    get hasToolCalls(): boolean;
    /**
     * Get tool call by name
     */
    getToolCallByName(name: string): ChatCompletionMessageToolCall | undefined;
    /**
     * Get tool output by tool call ID
     */
    getToolOutputById(toolCallId: string): ToolOutput | undefined;
    /**
     * Get execution details for a specific tool call
     */
    getExecutionDetail(toolCallId: string): ToolExecutionDetail | undefined;
    /**
     * Get all successful tool executions
     */
    get successfulExecutions(): ToolExecutionDetail[];
    /**
     * Get all failed tool executions
     */
    get failedExecutions(): ToolExecutionDetail[];
    /**
     * Get total execution time for all tool calls
     */
    get totalExecutionTime(): number;
    /**
     * Get total cost for all LLM calls
     */
    get totalCost(): number;
    /**
     * Get total input tokens
     */
    get totalInputTokens(): number;
    /**
     * Get total output tokens
     */
    get totalOutputTokens(): number;
    /**
     * Get total execution time for all LLM calls
     */
    get totalLlmExecutionTime(): number;
    /**
     * Get detailed information about the initial LLM call
     */
    get initialCall(): LlmCallCost | undefined;
    /**
     * Get detailed information about the followup LLM call
     */
    get followupCall(): LlmCallCost | undefined;
    /**
     * Get all LLM call details with timing
     */
    get llmCallDetails(): LlmCallCost[];
}
/**
 * Detailed information about tool execution
 */
export interface ToolExecutionDetail {
    toolCallId: string;
    toolName: string;
    executionTime: number;
    success: boolean;
    errorMessage?: string;
    startTime: number;
    endTime: number;
}
/**
 * Cost information for LLM calls
 */
export interface LlmCostInfo {
    model: string;
    totalInputTokens: number;
    totalOutputTokens: number;
    totalCost: number;
    costBreakdown: LlmCallCost[];
}
/**
 * Cost breakdown for individual LLM calls
 */
export interface LlmCallCost {
    callType: 'initial' | 'followup';
    inputTokens: number;
    outputTokens: number;
    cost: number;
    model: string;
    startTime: number;
    endTime: number;
    executionTime: number;
}
/**
 * Utility function to estimate LLM costs based on model and token counts
 * Prices are approximate and may vary by provider
 */
export declare function estimateLlmCost(model: string, inputTokens: number, outputTokens: number): number;
export type OpenAIMessage = ChatCompletionMessageParam;
//# sourceMappingURL=types.d.ts.map