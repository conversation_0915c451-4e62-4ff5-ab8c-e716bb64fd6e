import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
export declare class AuthService {
    private usersService;
    private jwtService;
    constructor(usersService: UsersService, jwtService: JwtService);
    validateUser(email: string, password: string): Promise<any>;
    login(user: any): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            name: any;
            image: any;
        };
    }>;
    googleLogin(profile: any): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            name: any;
            image: any;
        };
    }>;
    getProfile(userId: string): Promise<import("../users/user.entity").User>;
    devLogin(email: string, name?: string): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            name: any;
            image: any;
        };
    }>;
}
