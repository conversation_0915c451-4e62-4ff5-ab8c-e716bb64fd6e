import { ProductStatus } from '../shared/enums';
export declare class Product {
    id: string;
    name: string;
    description: string;
    price: number;
    stockQuantity: number;
    sku: string;
    barcode: string;
    category: string;
    brand: string;
    image: string;
    weight: number;
    dimensions: string;
    status: ProductStatus;
    isDeleted: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    updatedBy: string;
    storeId: string;
    createdByUser: any;
    updatedByUser: any;
    store: any;
    orderItems: any[];
}
