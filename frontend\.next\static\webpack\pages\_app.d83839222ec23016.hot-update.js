"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/utils/useAuthGuard.ts":
/*!***********************************!*\
  !*** ./src/utils/useAuthGuard.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthGuard: function() { return /* binding */ useAuthGuard; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n\n\n\n\n// Routes that don't require authentication\nconst PUBLIC_ROUTES = [\n    \"/login\",\n    \"/auth/callback\",\n    \"/setup/store\"\n];\nfunction useAuthGuard() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, isLoading, error } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { currentStoreId, setCurrentStoreId } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const [storesData, setStoresData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isStoresLoading, setIsStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const handledCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const isRedirectingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isPublic = (path)=>PUBLIC_ROUTES.some((route)=>path.startsWith(route));\n    // Fetch stores data for the current user\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!user) return;\n        const fetchStores = async ()=>{\n            setIsStoresLoading(true);\n            try {\n                // Import the store API dynamically to avoid circular dependencies\n                const { storeApi } = await __webpack_require__.e(/*! import() */ \"src_utils_api_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../utils/api */ \"./src/utils/api.ts\"));\n                const result = await storeApi.getByUserId(user.id, {\n                    page: 1,\n                    limit: 100\n                });\n                setStoresData(result);\n            } catch (error) {\n                console.error(\"Error fetching stores:\", error);\n                setStoresData({\n                    data: [],\n                    meta: {\n                        total: 0\n                    }\n                });\n            } finally{\n                setIsStoresLoading(false);\n            }\n        };\n        fetchStores();\n    }, [\n        user\n    ]);\n    // Main auth guard logic\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Add a small delay to allow auth state to stabilize after token storage\n        const timeoutId = setTimeout(()=>{\n            // Avoid redirecting while we are still determining auth state\n            if (isLoading) return;\n            const currentPath = router.pathname;\n            const asPath = router.asPath;\n            const fullPath = asPath || currentPath;\n            const searchParams = new URLSearchParams( true ? window.location.search : 0);\n            const loggedOutFlag = searchParams.get(\"loggedOut\");\n            // Ensure we only handle/log once per path after loading has completed\n            if (handledCache.current[currentPath]) {\n                return;\n            }\n            console.log(\"[AuthGuard] route/useEffect fired\", {\n                pathname: currentPath,\n                asPath: router.asPath,\n                isLoading,\n                hasUser: !!user,\n                error\n            });\n            if (!user && !isPublic(currentPath)) {\n                if (isRedirectingRef.current) {\n                    console.log(\"[AuthGuard] Redirect already in progress; skipping\");\n                    return;\n                }\n                isRedirectingRef.current = true;\n                const nextParam = encodeURIComponent(fullPath || \"/\");\n                const loginUrl = \"/login?next=\".concat(nextParam);\n                console.log(\"[AuthGuard] Not authenticated; redirecting to\", loginUrl);\n                router.replace(loginUrl).finally(()=>{\n                    setTimeout(()=>{\n                        isRedirectingRef.current = false;\n                    }, 0);\n                });\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (isPublic(currentPath) && currentPath === \"/login\" && loggedOutFlag === \"1\") {\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && isPublic(currentPath) && currentPath === \"/login\") {\n                if (isStoresLoading) {\n                    return;\n                }\n                if (storesData === null) {\n                    return;\n                }\n                if (!isRedirectingRef.current) {\n                    isRedirectingRef.current = true;\n                    let target = \"/dashboard\";\n                    try {\n                        var _storesData_meta, _storesData_data;\n                        const nextParam = searchParams.get(\"next\") || undefined;\n                        var _storesData_meta_total, _ref;\n                        const totalActive = (_ref = (_storesData_meta_total = storesData === null || storesData === void 0 ? void 0 : (_storesData_meta = storesData.meta) === null || _storesData_meta === void 0 ? void 0 : _storesData_meta.total) !== null && _storesData_meta_total !== void 0 ? _storesData_meta_total : storesData === null || storesData === void 0 ? void 0 : (_storesData_data = storesData.data) === null || _storesData_data === void 0 ? void 0 : _storesData_data.length) !== null && _ref !== void 0 ? _ref : 0;\n                        if (nextParam && nextParam.startsWith(\"/\")) {\n                            target = nextParam;\n                        } else {\n                            if (totalActive === 0) {\n                                target = \"/setup/store\";\n                            }\n                        }\n                    } catch (e) {}\n                    console.log(\"[AuthGuard] Already authenticated; redirecting away from public auth page to\", target);\n                    router.replace(target).finally(()=>{\n                        setTimeout(()=>{\n                            isRedirectingRef.current = false;\n                        }, 0);\n                    });\n                }\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && !isPublic(currentPath)) {\n                var _storesData_meta1, _storesData_data1;\n                if (storesData === null) {\n                    console.log(\"[AuthGuard] Stores data not yet fetched, waiting...\");\n                    return;\n                }\n                var _storesData_meta_total1, _ref1;\n                const totalActive = (_ref1 = (_storesData_meta_total1 = storesData === null || storesData === void 0 ? void 0 : (_storesData_meta1 = storesData.meta) === null || _storesData_meta1 === void 0 ? void 0 : _storesData_meta1.total) !== null && _storesData_meta_total1 !== void 0 ? _storesData_meta_total1 : storesData === null || storesData === void 0 ? void 0 : (_storesData_data1 = storesData.data) === null || _storesData_data1 === void 0 ? void 0 : _storesData_data1.length) !== null && _ref1 !== void 0 ? _ref1 : 0;\n                console.log(\"[AuthGuard] Store check:\", {\n                    currentPath,\n                    totalActive,\n                    storesData,\n                    isStoresLoading,\n                    loggedOutFlag\n                });\n                if (!isStoresLoading && totalActive === 0 && currentPath !== \"/setup/store\" && loggedOutFlag !== \"1\") {\n                    if (!isRedirectingRef.current) {\n                        isRedirectingRef.current = true;\n                        console.log(\"[AuthGuard] Authenticated user without active stores; redirecting to /setup/store\");\n                        router.replace(\"/setup/store\").finally(()=>{\n                            setTimeout(()=>{\n                                isRedirectingRef.current = false;\n                            }, 0);\n                        });\n                        handledCache.current[currentPath] = true;\n                        return;\n                    }\n                } else if (totalActive > 0 && !currentStoreId) {\n                    var _storesData_data_, _storesData_data2;\n                    const firstId = storesData === null || storesData === void 0 ? void 0 : (_storesData_data2 = storesData.data) === null || _storesData_data2 === void 0 ? void 0 : (_storesData_data_ = _storesData_data2[0]) === null || _storesData_data_ === void 0 ? void 0 : _storesData_data_.id;\n                    if (firstId) setCurrentStoreId(String(firstId));\n                }\n            }\n            if (!(user && !isPublic(currentPath) && isStoresLoading)) {\n                handledCache.current[currentPath] = true;\n            }\n        }, 100); // 100ms delay to allow auth state to stabilize\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        router.pathname,\n        isLoading,\n        user,\n        error,\n        isStoresLoading,\n        storesData,\n        currentStoreId,\n        router,\n        setCurrentStoreId\n    ]);\n    return {\n        user,\n        isLoading,\n        error,\n        storesData,\n        isStoresLoading,\n        currentStoreId\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/useAuthGuard.ts\n"));

/***/ })

});