import { OrderStatusService } from '../../services/order.service';

export const UNDERWAY_STATUSES: Array<OrderStatusService> = [
	OrderStatusService.DRAFT,
	OrderStatusService.PENDING,
	OrderStatusService.CONFIRMED,
	OrderStatusService.PROCESSING,
];

export function canConvertToBigInt(value: unknown): boolean {
	if (typeof value === 'bigint') return true;
	if (typeof value === 'number') return Number.isInteger(value) && value >= 0;
	if (typeof value === 'string') return /^\d+$/.test(value.trim());
	return false;
}

export async function resolveItemsAgainstCatalog(
	items: Array<{ productId?: string | number | bigint; productName?: string; quantity: number; taxAmount?: number }>,
	storeId: bigint,
	db: any,
) {
	const idCandidates = items
		.map((it) => it.productId)
		.filter((v) => typeof v !== 'undefined' && canConvertToBigInt(v as any))
		.map((v) => BigInt(v as any));

	const nameCandidates = items
		.map((it) => (typeof it.productName === 'string' && it.productName.trim().length > 0
			? it.productName.trim()
			: (typeof it.productId === 'string' && !canConvertToBigInt(it.productId)
				? String(it.productId).trim()
				: undefined)))
		.filter((v): v is string => typeof v === 'string' && v.length > 0);

	// Get the product repository
	const productRepo = db.getRepository('Product');

	// Get all products from the store for comprehensive searching
	const allStoreProducts: Array<{ id: bigint; name: string; price: unknown }> = await productRepo.find({
		where: {
			isDeleted: false,
			storeId,
		},
		select: { id: true, name: true, price: true },
	});

	// Create maps from ALL store products for comprehensive lookup
	const productById = new Map(allStoreProducts.map((p: { id: bigint; name: string; price: unknown }) => [p.id.toString(), p]));
	const productByName = new Map(allStoreProducts.map((p: { id: bigint; name: string; price: unknown }) => [p.name.toLowerCase(), p]));

	// For ID candidates, find exact matches
	const products: Array<{ id: bigint; name: string; price: unknown }> = [];
	if (idCandidates.length > 0) {
		const idMatches = allStoreProducts.filter(p => idCandidates.includes(p.id));
		products.push(...idMatches);
	}

	// For name candidates, find matches using our improved logic
	if (nameCandidates.length > 0) {
		for (const searchName of nameCandidates) {
			const trimmedSearch = searchName.trim();

			// Try to find exact match (case-insensitive)
			let found = false;
			for (const product of allStoreProducts) {
				if (product.name.toLowerCase() === trimmedSearch.toLowerCase()) {
					products.push(product);
					found = true;
					break; // Take first exact match
				}
			}

			// If no exact match, try partial matches
			if (!found) {
				for (const product of allStoreProducts) {
					const productName = product.name.toLowerCase();
					const searchTerm = trimmedSearch.toLowerCase();

					if (productName.includes(searchTerm) || searchTerm.includes(productName)) {
						products.push(product);
						break; // Take first partial match
					}
				}
			}
		}
	}

	// Get all available products for better error messages
	const availableProducts = await productRepo.find({
		where: {
			isDeleted: false,
			storeId,
		},
		select: { id: true, name: true, price: true },
		take: 10 // Limit for performance
	});

	const preparedItems = items.map((it, index) => {
		let resolved: any | undefined;
		let searchTerm = '';

		if (typeof it.productId !== 'undefined' && canConvertToBigInt(it.productId)) {
			resolved = productById.get(BigInt(it.productId as any).toString());
			searchTerm = `ID: ${it.productId}`;
		} else if (typeof it.productName === 'string' && it.productName.trim().length > 0) {
			const searchName = it.productName.trim().toLowerCase();
			searchTerm = `Name: "${it.productName}"`;

			// Try exact match first
			resolved = productByName.get(searchName);

			// If no exact match, try partial matches
			if (!resolved) {
				for (const [productName, product] of Array.from(productByName.entries())) {
					if (productName.includes(searchName) || searchName.includes(productName)) {
						resolved = product;
						break;
					}
				}
			}
		} else if (typeof it.productId === 'string') {
			const searchName = it.productId.trim().toLowerCase();
			searchTerm = `Name: "${it.productId}"`;
			resolved = productByName.get(searchName);
		}

		if (!resolved) {
			const itemInfo = [];
			if (it.productId !== undefined) itemInfo.push(`productId: ${it.productId} (type: ${typeof it.productId})`);
			if (it.productName !== undefined) itemInfo.push(`productName: "${it.productName}" (type: ${typeof it.productName})`);

			const availableProductsInfo = availableProducts.map((p: any) => `"${p.name}" (ID: ${p.id})`).join(', ');
			const errorMsg = `Product not found for item ${index + 1}. Searched for: [${itemInfo.join(', ')}].\n` +
				`Available products in store: ${availableProductsInfo}${availableProducts.length >= 10 ? ' (showing first 10)' : ''}`;

			throw new Error(errorMsg);
		}

		const unitPrice = Number(resolved.price as unknown as any);
		const lineTotal = unitPrice * it.quantity + (it.taxAmount ?? 0);
		return {
			productId: resolved.id as unknown as bigint,
			productName: resolved.name,
			quantity: it.quantity,
			unitPrice,
			lineTotal,
			taxAmount: it.taxAmount ?? 0,
		};
	});

	return preparedItems;
}
