import { updateCustomer } from '../../services/customer.service';

export async function updateCustomerTool(
	params: { 
		customerId: string; 
		phone?: string; 
		address?: string; 
		email?: string; 
		name?: string;
	},
	db: any,
	conversationUuid: string
) {
	const { customerId, phone, address, email, name } = params || ({} as any);
	
	// Validate required parameters
	if (!customerId) {
		throw new Error('Customer ID is required');
	}

	// Validate database connection and get repositories
	if (!db || !db.isInitialized) {
		throw new Error('Database connection is not properly initialized');
	}

	const conversationRepo = db.getRepository('Conversation');
	const customerRepo = db.getRepository('Customer');

	// Read conversation ownership to derive store/user context
	const convo = await conversationRepo.findOne({
		where: { uuid: conversationUuid, isDeleted: false },
		select: ['id', 'userId', 'storeId', 'createdBy', 'context'],
	});
	if (!convo) throw new Error('Conversation not found');

	// Check if customer exists
	const existingCustomer = await customerRepo.findOne({
		where: { id: customerId, storeId: convo.storeId, isDeleted: false },
	});
	if (!existingCustomer) throw new Error('Customer not found');

	// Prepare update data - only include fields that are provided
	const updateData: any = {};
	if (phone !== undefined) updateData.phone = phone;
	if (address !== undefined) updateData.address = address;
	if (email !== undefined) updateData.email = email;
	if (name !== undefined) updateData.name = name;

	// If no fields to update, return early
	if (Object.keys(updateData).length === 0) {
		return {
			message: 'No fields to update',
			customer: existingCustomer
		};
	}

	// Update customer
	const updatedCustomer = await customerRepo.update(
		{ id: customerId },
		{
			...updateData,
			updatedBy: convo.createdBy as unknown as bigint,
			updatedAt: new Date(),
		}
	);

	// Update conversation context with customer info
	const currentCtx = await conversationRepo.findOne({
		where: { id: convo.id as unknown as bigint },
		select: ['context'],
	});

	if (currentCtx && currentCtx.context) {
		const updatedContext = {
			...currentCtx.context,
			customer: {
				id: customerId.toString(),
				name: updatedCustomer.name,
				email: updatedCustomer.email,
				phone: updatedCustomer.phone,
				address: updatedCustomer.address,
			},
		};

		await conversationRepo.update(
			{ id: convo.id as unknown as bigint },
			{ context: updatedContext }
		);
	}

	return {
		message: 'Customer updated successfully',
		customer: updatedCustomer,
		updatedFields: Object.keys(updateData)
	};
}
