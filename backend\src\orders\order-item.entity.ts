import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';

@Entity('order_items')
export class OrderItem {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'bigint' })
  orderId: string;

  @Column({ type: 'bigint' })
  productId: string;

  @Column({ type: 'varchar' })
  productName: string;

  @Column({ type: 'int' })
  quantity: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  unitPrice: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  lineTotal: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint' })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('Order', (order: any) => order.orderItems)
  order: any;

  @ManyToOne('Product', (product: any) => product.orderItems)
  product: any;

  @ManyToOne('User', (user: any) => user.createdOrderItems)
  createdByUser: any;

  @ManyToOne('User', (user: any) => user.updatedOrderItems)
  updatedByUser: any;
}
