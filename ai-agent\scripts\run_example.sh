#!/bin/bash

# Example script to run the LLM API test
# This script shows how to set up the environment and run the test

echo "🚀 Setting up environment for LLM API test..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from example..."
    cp scripts/example.env .env
    echo "⚠️  Please edit .env file and add your actual API_KEY"
    echo "   Then run this script again"
    exit 1
fi

# Load environment variables
echo "📖 Loading environment variables..."
source .env

# Check if API_KEY is set
if [ -z "$API_KEY" ] || [ "$API_KEY" = "your_openrouter_api_key_here" ]; then
    echo "❌ API_KEY not set in .env file"
    echo "   Please edit .env file and add your actual OpenRouter API key"
    exit 1
fi

echo "✅ Environment setup complete!"
echo "🔑 API Key: ${API_KEY:0:10}..."
echo "🌐 Base URL: $BASE_URL"
echo "🤖 Model: $MODEL"
echo ""

# Run the test
echo "🧪 Running LLM API test..."
npm run test:llm

echo ""
echo "✅ Test completed!"
