import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { storeApi } from '../../utils/api';
import { useAuth } from '../../context/AuthContext';
import { useStore } from '../../context/StoreContext';
import { CURRENCY_OPTIONS, LANGUAGE_OPTIONS } from '../../utils/preferences';

export default function SetupStorePage() {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const { setCurrentStoreId } = useStore();
  const firstInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    name: '',
    currency: 'USD',
    preferredLanguage: 'en'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [authLoading, user, router]);

  // Focus management for accessibility
  useEffect(() => {
    if (firstInputRef.current && !authLoading && user) {
      firstInputRef.current.focus();
    }
  }, [authLoading, user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.id) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const store = await storeApi.create({
        name: formData.name,
        currency: formData.currency,
        preferredLanguage: formData.preferredLanguage,
        userId: user.id
      });

      console.log('Store created successfully:', store);

      // Set the new store as current
      setCurrentStoreId((store as any)?.id);
      console.log('Current store ID set to:', (store as any)?.id);
      
      // Set a flag in localStorage to prevent redirect loop
      try {
        localStorage.setItem('teno:store-creation-complete', 'true');
      } catch (e) {
        console.warn('Could not set localStorage flag:', e);
      }
      
      // Add a small delay to ensure database transaction is committed
      setTimeout(() => {
        console.log('Redirecting to dashboard...');
        // Redirect to dashboard with a flag to prevent redirect loop
        router.push('/dashboard?from=store-creation');
      }, 100);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create store');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (authLoading) {
    return (
      <div className="app-bg">
        <div className="glow-overlay" />
        <div className="grid-overlay" />
        <div className="relative flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-400"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Setup Store - Teno Store</title>
        <meta name="description" content="Create your first store" />
      </Head>

      <div className="app-bg">
        <div className="glow-overlay" />
        <div className="grid-overlay" />
        
        <div className="relative max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="icon-badge">
              <svg className="h-8 w-8 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-extrabold tracking-tight brand-gradient-text">
              Create Your First Store
            </h2>
                          <p className="mt-2 subtle-text">
                Let&apos;s get you started with your online business
              </p>
          </div>

          <div className="glass-card">
            <div className="card-accent" />
            
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-slate-200 mb-2">
                  Store Name <span className="text-red-400">*</span>
                </label>
                <input
                  ref={firstInputRef}
                  id="name"
                  name="name"
                  type="text"
                  required
                  className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-xl text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-emerald-400/50"
                  placeholder="Enter store name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
              </div>

              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-slate-200 mb-2">
                  Currency <span className="text-red-400">*</span>
                </label>
                <select
                  id="currency"
                  name="currency"
                  required
                  className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-xl text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-emerald-400/50"
                  value={formData.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                >
                  {CURRENCY_OPTIONS.map((currency) => (
                    <option key={currency} value={currency} className="bg-slate-800 text-slate-100">
                      {currency}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="language" className="block text-sm font-medium text-slate-200 mb-2">
                  Preferred Language <span className="text-red-400">*</span>
                </label>
                <select
                  id="language"
                  name="language"
                  required
                  className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-xl text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-emerald-400/50"
                  value={formData.preferredLanguage}
                  onChange={(e) => handleInputChange('preferredLanguage', e.target.value)}
                >
                  {LANGUAGE_OPTIONS.map((lang) => (
                    <option key={lang} value={lang} className="bg-slate-800 text-slate-100">
                      {lang}
                    </option>
                  ))}
                </select>
              </div>

              {error && (
                <div className="bg-red-500/10 border border-red-500/20 text-red-300 px-4 py-3 rounded-md">
                  {error}
                </div>
              )}

              <div>
                <button
                  type="submit"
                  disabled={isSubmitting || !formData.name.trim()}
                  className="group primary-button"
                >
                  <span>{isSubmitting ? 'Creating...' : 'Create Store'}</span>
                  <span className="button-glow group-hover:opacity-100" />
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
}


