
### Error Responses
- **401 Unauthorized**: No token, invalid token, or expired token
- **403 Forbidden**: User lacks required permissions (if role-based access is implemented)

## Pagination Requirement
All endpoints that return a list of items must implement pagination. This ensures efficient data transfer and consistent client experience, especially for large datasets. Use query parameters such as `page` and `limit` (or similar) to control pagination. Always return pagination metadata in the response.

### Required Pagination Response Fields
All paginated endpoints MUST return the following fields in the response:
- `totalPages`: Total number of pages available (calculated as `Math.ceil(total / limit)`)
- `totalEntries` or `total`: Total number of items across all pages
- `page`: Current page number
- `limit`: Number of items per page
- `hasNext`: Bo<PERSON>an indicating if there are more pages
- `hasPrev`: Bo<PERSON>an indicating if there are previous pages

### Standard Pagination Response Structure
Use the `PaginatedResponseDto<T>` class which automatically includes all required fields:
```typescript
{
  "data": [...], // Array of items
  "meta": {
    "total": 150,        // Total entries
    "page": 1,           // Current page
    "limit": 10,         // Items per page
    "totalPages": 15,    // Total pages
    "hasNext": true,     // Has next page
    "hasPrev": false     // Has previous page
  }
}
```

Filter endpoints that accept POST requests should include optional `page` and `limit` fields in the request DTO for consistent pagination handling.

## Filter Restrictions

1. **Always enforce `isDeleted: false`** in customer queries - never allow this as a filter parameter
2. **Validate filter DTOs** to ensure only allowed fields are accepted

## Minimal amount of endpoints needed per entity

- We should not have an endpoint for each field update.
- We should have one endpoint for updating all fields.
- We should have one endpoint for filtering using all fields.
- When filtering fields, an empty filter means get all values and ignore the field.
- 



### Overview
When implementing numeric filters (e.g., `minAmount`, `maxAmount`, `minQuantity`, `maxPrice`), always handle empty values, NaN, and provide appropriate fallbacks.