/**
 * Multi-turn chat loop example
 */

import { config } from 'dotenv';
import readline from 'readline';
import { Agent, LlmApi, LLMProvider, setLlmProviderFromEnvironmentVariables } from '../src';
import type { Message } from '../src';

config();
setLlmProviderFromEnvironmentVariables();

async function main() {
  if (!LLMProvider.apiKey) {
    console.error('API key not configured. Please set API_KEY in .env and rerun.');
    return;
  }

  console.log('Chat Agent Example');
  console.log('Model:', LLMProvider.model || 'gpt-4o-mini (default)');
  console.log('Base URL:', LLMProvider.baseUrl || '(default)');
  console.log('Type "/exit" to quit.');
  console.log('');

  const agent = new Agent(
    'ChatAgent',
    'You are a helpful assistant. Keep responses concise. If tools are available, use them when helpful.',
    []
  );

  const history: Message[] = [];

  const rl = readline.createInterface({ input: process.stdin, output: process.stdout });

  const ask = (q: string) => new Promise<string>(resolve => rl.question(q, resolve));

  while (true) {
    const input = (await ask('You: ')).trim();
    if (!input) continue;
    if (input.toLowerCase() === '/exit') break;

    const reply = await LlmApi.generateLlmResponse(agent, [...history, { role: 'user', content: input }]);
    console.log('Assistant:', reply.content);
    
    if (reply.hasToolCalls) {
      console.log(`[${reply.calls.length} tool calls made]`);
      reply.calls.forEach(call => {
        console.log(`  - ${call.function?.name}: ${call.function?.arguments}`);
      });
    }

    history.push({ role: 'user', content: input });
    history.push({ role: 'assistant', content: reply.content });
  }

  rl.close();
}

main().catch(err => {
  console.error('Error in chat loop:', err);
});


