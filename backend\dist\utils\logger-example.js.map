{"version": 3, "file": "logger-example.js", "sourceRoot": "", "sources": ["../../src/utils/logger-example.ts"], "names": [], "mappings": ";;AA4DS,0CAAe;AAAE,oCAAY;AAAE,gDAAkB;AAvD1D,qCAAiE;AAGjE,SAAS,eAAe;IAEtB,IAAA,YAAG,EAAC,uBAAuB,CAAC,CAAC;IAC7B,IAAA,cAAK,EAAC,0BAA0B,CAAC,CAAC;IAClC,IAAA,aAAI,EAAC,2BAA2B,CAAC,CAAC;IAClC,IAAA,aAAI,EAAC,yBAAyB,CAAC,CAAC;IAChC,IAAA,cAAK,EAAC,yBAAyB,CAAC,CAAC;IAGjC,IAAA,YAAG,EAAC,YAAY,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAGjD,gBAAM,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC1C,gBAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAG1C,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAE9B,UAAU,CAAC,GAAG,EAAE;QACd,gBAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACnC,CAAC,EAAE,GAAG,CAAC,CAAC;IAGR,gBAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACpC,gBAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC1B,gBAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IAC9B,gBAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC1B,gBAAM,CAAC,QAAQ,EAAE,CAAC;AACpB,CAAC;AAGD,KAAK,UAAU,YAAY;IACzB,IAAI,CAAC;QACH,gBAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAExC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACvD,gBAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAA,cAAK,EAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;IACxC,CAAC;AACH,CAAC;AAGD,SAAS,kBAAkB,CAAC,MAAe;IACzC,IAAI,MAAM,EAAE,CAAC;QACX,gBAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;SAAM,CAAC;QACN,gBAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACrC,CAAC;AACH,CAAC;AAMD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IACxC,eAAe,EAAE,CAAC;IAClB,YAAY,EAAE,CAAC;IACf,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAC9B,kBAAkB,EAAE,CAAC;AACvB,CAAC"}