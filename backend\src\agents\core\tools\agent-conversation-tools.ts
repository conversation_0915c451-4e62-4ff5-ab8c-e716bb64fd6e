import { DataSource } from 'typeorm';
import { createTool } from '../../../../../ai-agent/dist';
import {
	placeOrderTool as importedPlaceOrderTool,
	checkOrderUnderwayTool as importedCheckOrderUnderwayTool,
	searchCustomersTool as importedSearchCustomersTool,
	updateOrderTool as importedUpdateOrderTool,
	updateCustomerTool as importedUpdateCustomerTool
} from '.';

export function buildConversationTools(
	db: DataSource,
	conversationUuid: string
) {
	console.log(`🔧 buildConversationTools called with:`, {
		dbType: typeof db,
		dbInitialized: db?.isInitialized,
		conversationUuid,
		timestamp: new Date().toISOString()
	});

	// Validate database connection
	if (!db || !db.isInitialized) {
		console.error(`❌ Database connection validation failed:`, {
			dbExists: !!db,
			dbInitialized: db?.isInitialized,
			dbType: typeof db
		});
		throw new Error('Database connection is not properly initialized');
	}

	console.log(`✅ Database connection validated successfully`);

	const placeOrderTool = async (params: any) => {
		console.log(`🔧 placeOrderTool called with:`, {
			params,
			conversationUuid,
			timestamp: new Date().toISOString()
		});

		// Validate the database connection
		if (!db || !db.isInitialized) {
			console.error(`❌ Database connection validation failed:`, {
				dbExists: !!db,
				dbInitialized: db?.isInitialized,
				dbType: typeof db
			});
			throw new Error('Database connection is not properly initialized');
		}

		console.log(`✅ Database connection validated successfully`);
		return importedPlaceOrderTool(params, db, conversationUuid);
	};

	// const checkOrderUnderwayTool = async (params: any) => {
	// 	return importedCheckOrderUnderwayTool(params, db, conversationUuid);
	// };

	// const searchCustomersTool = async (params: any) => {
	// 	return importedSearchCustomersTool(params, db, conversationUuid);
	// };

	const updateOrderTool = async (params: any) => {
		console.log(`🔧 updateOrderTool called with:`, {
			params,
			conversationUuid,
			timestamp: new Date().toISOString()
		});

		// Validate the database connection
		if (!db || !db.isInitialized) {
			console.error(`❌ Database connection validation failed for updateOrder:`, {
				dbExists: !!db,
				dbInitialized: db?.isInitialized,
				dbType: typeof db
			});
			throw new Error('Database connection is not properly initialized');
		}

		console.log(`✅ Database connection validated successfully for updateOrder`);
		return importedUpdateOrderTool(params, db, conversationUuid);
	};

	// const updateCustomerTool = async (params: any) => {
	// 	return importedUpdateCustomerTool(params, db, conversationUuid);
	// };

	const placeOrder = createTool(placeOrderTool, {
		name: 'placeOrder',
		description:
			'Place a new order. Provide customerName, customerPhone, customerAddress, and items. Each item needs quantity and productId (numeric ID) for proper identification. Optionally include customerEmail, useTax, taxRate, and priority. ',
		parameterTypes: {
			customerName: { type: 'string' },
			customerPhone: { type: 'string' },
			customerAddress: { type: 'string' },
			customerEmail: { type: 'string', optional: true },
			items: {
				type: 'array',
				items: {
					type: 'object',
					properties: {
						productId: { type: 'number' },
						productName: {type: 'string', optional: true },
						quantity: { type: 'number' },
						taxAmount: { type: 'number', optional: true },
					},
					required: ['productId', 'quantity'],
				},
			},
			useTax: { type: 'boolean', optional: true },
			taxRate: { type: 'number', optional: true },
			priority: { type: 'string', optional: true },
		},
		requiredParams: ['customerName', 'customerPhone', 'customerAddress', 'items'],
	});

	// const checkOrderUnderway = createTool(checkOrderUnderwayTool, {
	// 	name: 'checkOrderUnderway',
	// 	description:
	// 		'Check if there is already an order underway for a given customer email. Optionally pass items to check for a similar order.',
	// 	parameterTypes: {
	// 		customerEmail: { type: 'string' },
	// 		items: {
	// 			type: 'array',
	// 			optional: true,
	// 			items: {
	// 				type: 'object',
	// 				properties: {
	// 					productId: { type: 'string' },
	// 					quantity: { type: 'number' },
	// 					taxAmount: { type: 'number', optional: true },
	// 				},
	// 				required: ['productId', 'quantity'],
	// 			},
	// 		},
	// 	},
	// 	requiredParams: ['customerEmail'],
	// });

	// const searchCustomers = createTool(searchCustomersTool, {
	// 	name: 'searchCustomers',
	// 	description:
	// 		'Search customers by name, email, or phone within the store. Trims inputs and ignores empty fields; combines provided filters. Saves results to context.customers and, when a single match is found, also sets context.customer and their recent orders.',
	// 	parameterTypes: {
	// 		name: { type: 'string', optional: true },
	// 		email: { type: 'string', optional: true },
	// 		phone: { type: 'string', optional: true },
	// 	},
	// });

	const updateOrder = createTool(updateOrderTool, {
		name: 'updateOrder',
		description:
			'Update an existing order with delta-like behavior. Provide orderNumber and any combination of: customerName, customerEmail, customerPhone, useTax, taxRate, priority, status, expectedDeliveryDate, preferredDeliveryLocation. For items, use itemsToAdd to add new items (quantities merge with existing by default, or set replaceQuantities=true to replace), itemsToRemove to remove specific quantities, or removeAllItems to clear all items. Only works with orders in DRAFT, PENDING, CONFIRMED, or PROCESSING status (orders that have not been shipped yet). For items, prefer using productName over productId for better reliability.',
		parameterTypes: {
			orderNumber: { type: 'string' },
			customerName: { type: 'string', optional: true },
			customerEmail: { type: 'string', optional: true },
			customerPhone: { type: 'string', optional: true },
			itemsToAdd: {
				type: 'array',
				optional: true,
				items: {
					type: 'object',
					properties: {
						productId: { type: 'string', optional: true },
						productName: { type: 'string', optional: true },
						quantity: { type: 'number' },
						taxAmount: { type: 'number', optional: true },
					},
					required: ['quantity'],
				},
			},
			itemsToRemove: {
				type: 'array',
				optional: true,
				items: {
					type: 'object',
					properties: {
						productId: { type: 'string', optional: true },
						productName: { type: 'string', optional: true },
						quantity: { type: 'number', optional: true },
					},
					required: [],
				},
			},
			removeAllItems: { type: 'boolean', optional: true },
			replaceQuantities: { type: 'boolean', optional: true },
			useTax: { type: 'boolean', optional: true },
			taxRate: { type: 'number', optional: true },
			priority: { type: 'string', optional: true },
			status: { type: 'string', optional: true },
			expectedDeliveryDate: { type: 'string', optional: true },
			preferredDeliveryLocation: { type: 'string', optional: true },
		},
		requiredParams: ['orderNumber'],
	});

	// const updateCustomer = createTool(updateCustomerTool, {
	// 	name: 'updateCustomer',
	// 	description:
	// 		'Update customer information when new details are available. Provide customerId and any combination of: phone, address, email, name. Only updates fields that are provided. Automatically updates conversation context with the updated customer information.',
	// 	parameterTypes: {
	// 		customerId: { type: 'string' },
	// 		phone: { type: 'string', optional: true },
	// 		address: { type: 'string', optional: true },
	// 		email: { type: 'string', optional: true },
	// 		name: { type: 'string', optional: true },
	// 	},
	// 	requiredParams: ['customerId'],
	// });

	const toolsArray = [placeOrder, updateOrder];
	
	return toolsArray;
}


