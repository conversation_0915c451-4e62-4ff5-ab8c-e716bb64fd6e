"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./src/styles/globals.css":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./src/styles/globals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n/* App background container for dark agentic theme */\\n.app-bg {\\n  position: relative;\\n  display: flex;\\n  min-height: 100vh;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n@media (min-width: 640px) {\\n\\n  .app-bg {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .app-bg {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n.app-bg {\\n    background-color: var(--bg-primary);\\n  }\\n/* Radial glow background overlay */\\n.glow-overlay {\\n  pointer-events: none;\\n  position: absolute;\\n  inset: 0px;\\n  opacity: 0.4;\\n    background: radial-gradient(1200px 600px at -10% -20%, #2E5BFF 10%, transparent 60%),\\n                radial-gradient(800px 400px at 120% 110%, var(--brand-emerald) 5%, transparent 50%);\\n}\\n/* Subtle grid overlay */\\n.grid-overlay {\\n  pointer-events: none;\\n  position: absolute;\\n  inset: 0px;\\n    opacity: 0.08;\\n    background-image: radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.6) 1px, transparent 1px);\\n    background-size: 50px 50px;\\n}\\n/* Glass card shell */\\n.glass-card {\\n  position: relative;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: rgb(255 255 255 / 0.1);\\n  background-color: rgb(255 255 255 / 0.05);\\n  padding: 1.5rem;\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  --tw-backdrop-blur: blur(24px);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n@media (min-width: 640px) {\\n\\n  .glass-card {\\n    padding: 2rem;\\n  }\\n}\\n/* Optional gradient accent layer for cards (use as an empty absolutely positioned div inside .glass-card) */\\n.card-accent {\\n  pointer-events: none;\\n  position: absolute;\\n  inset: -1px;\\n  border-radius: 0.75rem;\\n    background-image: linear-gradient(90deg, color-mix(in oklab, var(--brand-emerald) 25%, transparent), color-mix(in oklab, var(--brand-cyan) 25%, transparent), color-mix(in oklab, var(--brand-indigo) 25%, transparent));\\n}\\n/* Brand gradient text utility */\\n.brand-gradient-text {\\n    background-image: linear-gradient(90deg, var(--brand-emerald), var(--brand-cyan), var(--brand-indigo));\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    color: transparent;\\n  }\\n/* Primary CTA button for dark theme */\\n.primary-button {\\n  position: relative;\\n  display: flex;\\n  width: 100%;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.75rem;\\n  border-radius: 0.5rem;\\n  background-color: rgb(15 23 42 / 0.6);\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(241 245 249 / var(--tw-text-opacity, 1));\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-color: rgb(255 255 255 / 0.1);\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.primary-button:hover {\\n  background-color: rgb(15 23 42 / 0.8);\\n  --tw-ring-color: rgb(52 211 153 / 0.5);\\n}\\n.primary-button:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-color: rgb(52 211 153 / 0.6);\\n}\\n.primary-button:disabled {\\n  cursor: not-allowed;\\n  opacity: 0.5;\\n}\\n/* Hover glow layer to nest inside buttons if desired */\\n.button-glow {\\n  position: absolute;\\n  inset: 0px;\\n  z-index: -10;\\n  border-radius: 0.5rem;\\n  opacity: 0;\\n  --tw-blur: blur(24px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n    background-image: linear-gradient(90deg, color-mix(in oklab, var(--brand-emerald) 20%, transparent), color-mix(in oklab, var(--brand-cyan) 20%, transparent), color-mix(in oklab, var(--brand-indigo) 20%, transparent));\\n}\\n/* Subtle muted text for descriptions */\\n.subtle-text {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  color: rgb(203 213 225 / 0.8);\\n}\\n/* Small rounded icon badge container */\\n.icon-badge {\\n  margin-left: auto;\\n  margin-right: auto;\\n  display: flex;\\n  height: 3rem;\\n  width: 3rem;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.5rem;\\n  background-color: rgb(255 255 255 / 0.05);\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-color: rgb(255 255 255 / 0.1);\\n  --tw-backdrop-blur: blur(8px);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.-inset-0\\\\.5 {\\n  inset: -0.125rem;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.-right-2 {\\n  right: -0.5rem;\\n}\\n.-top-2 {\\n  top: -0.5rem;\\n}\\n.left-0 {\\n  left: 0px;\\n}\\n.left-14 {\\n  left: 3.5rem;\\n}\\n.left-6 {\\n  left: 1.5rem;\\n}\\n.right-2 {\\n  right: 0.5rem;\\n}\\n.right-3 {\\n  right: 0.75rem;\\n}\\n.right-6 {\\n  right: 1.5rem;\\n}\\n.top-0 {\\n  top: 0px;\\n}\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\n.top-12 {\\n  top: 3rem;\\n}\\n.top-2 {\\n  top: 0.5rem;\\n}\\n.top-20 {\\n  top: 5rem;\\n}\\n.z-10 {\\n  z-index: 10;\\n}\\n.z-40 {\\n  z-index: 40;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.order-1 {\\n  order: 1;\\n}\\n.order-2 {\\n  order: 2;\\n}\\n.col-span-full {\\n  grid-column: 1 / -1;\\n}\\n.mx-4 {\\n  margin-left: 1rem;\\n  margin-right: 1rem;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.my-3 {\\n  margin-top: 0.75rem;\\n  margin-bottom: 0.75rem;\\n}\\n.my-4 {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.my-6 {\\n  margin-top: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\n.-ml-1 {\\n  margin-left: -0.25rem;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-12 {\\n  margin-bottom: 3rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.ml-16 {\\n  margin-left: 4rem;\\n}\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.ml-64 {\\n  margin-left: 16rem;\\n}\\n.ml-auto {\\n  margin-left: auto;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-12 {\\n  margin-top: 3rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.line-clamp-2 {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 2;\\n}\\n.block {\\n  display: block;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.table {\\n  display: table;\\n}\\n.table-row {\\n  display: table-row;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.h-1\\\\.5 {\\n  height: 0.375rem;\\n}\\n.h-10 {\\n  height: 2.5rem;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-16 {\\n  height: 4rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-24 {\\n  height: 6rem;\\n}\\n.h-3 {\\n  height: 0.75rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-40 {\\n  height: 10rem;\\n}\\n.h-48 {\\n  height: 12rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-7 {\\n  height: 1.75rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-auto {\\n  height: auto;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.h-px {\\n  height: 1px;\\n}\\n.h-screen {\\n  height: 100vh;\\n}\\n.max-h-60 {\\n  max-height: 15rem;\\n}\\n.max-h-96 {\\n  max-height: 24rem;\\n}\\n.max-h-\\\\[80vh\\\\] {\\n  max-height: 80vh;\\n}\\n.max-h-\\\\[90vh\\\\] {\\n  max-height: 90vh;\\n}\\n.max-h-\\\\[calc\\\\(90vh-200px\\\\)\\\\] {\\n  max-height: calc(90vh - 200px);\\n}\\n.min-h-\\\\[50vh\\\\] {\\n  min-height: 50vh;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-0\\\\.5 {\\n  width: 0.125rem;\\n}\\n.w-1 {\\n  width: 0.25rem;\\n}\\n.w-1\\\\.5 {\\n  width: 0.375rem;\\n}\\n.w-10 {\\n  width: 2.5rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-16 {\\n  width: 4rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-24 {\\n  width: 6rem;\\n}\\n.w-3 {\\n  width: 0.75rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-7 {\\n  width: 1.75rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.min-w-\\\\[220px\\\\] {\\n  min-width: 220px;\\n}\\n.min-w-\\\\[280px\\\\] {\\n  min-width: 280px;\\n}\\n.min-w-full {\\n  min-width: 100%;\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\n.max-w-7xl {\\n  max-width: 80rem;\\n}\\n.max-w-\\\\[85\\\\%\\\\] {\\n  max-width: 85%;\\n}\\n.max-w-full {\\n  max-width: 100%;\\n}\\n.max-w-lg {\\n  max-width: 32rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.max-w-none {\\n  max-width: none;\\n}\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.grow {\\n  flex-grow: 1;\\n}\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rotate-180 {\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes bounce {\\n\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8,0,1,1);\\n  }\\n\\n  50% {\\n    transform: none;\\n    animation-timing-function: cubic-bezier(0,0,0.2,1);\\n  }\\n}\\n.animate-bounce {\\n  animation: bounce 1s infinite;\\n}\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.select-none {\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\n.select-all {\\n  -webkit-user-select: all;\\n     -moz-user-select: all;\\n          user-select: all;\\n}\\n.resize-none {\\n  resize: none;\\n}\\n.list-inside {\\n  list-style-position: inside;\\n}\\n.list-decimal {\\n  list-style-type: decimal;\\n}\\n.list-disc {\\n  list-style-type: disc;\\n}\\n.list-none {\\n  list-style-type: none;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-start {\\n  justify-content: flex-start;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.gap-8 {\\n  gap: 2rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.divide-y > :not([hidden]) ~ :not([hidden]) {\\n  --tw-divide-y-reverse: 0;\\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\\n}\\n.divide-white\\\\/10 > :not([hidden]) ~ :not([hidden]) {\\n  border-color: rgb(255 255 255 / 0.1);\\n}\\n.overflow-auto {\\n  overflow: auto;\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\n.whitespace-pre {\\n  white-space: pre;\\n}\\n.whitespace-pre-wrap {\\n  white-space: pre-wrap;\\n}\\n.break-all {\\n  word-break: break-all;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-2xl {\\n  border-radius: 1rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.rounded-r-lg {\\n  border-top-right-radius: 0.5rem;\\n  border-bottom-right-radius: 0.5rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-0 {\\n  border-width: 0px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-4 {\\n  border-width: 4px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\n.border-l-4 {\\n  border-left-width: 4px;\\n}\\n.border-r {\\n  border-right-width: 1px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-amber-400\\\\/20 {\\n  border-color: rgb(251 191 36 / 0.2);\\n}\\n.border-amber-400\\\\/30 {\\n  border-color: rgb(251 191 36 / 0.3);\\n}\\n.border-blue-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-400\\\\/30 {\\n  border-color: rgb(96 165 250 / 0.3);\\n}\\n.border-blue-500\\\\/20 {\\n  border-color: rgb(59 130 246 / 0.2);\\n}\\n.border-blue-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(165 243 252 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400\\\\/20 {\\n  border-color: rgb(34 211 238 / 0.2);\\n}\\n.border-cyan-400\\\\/30 {\\n  border-color: rgb(34 211 238 / 0.3);\\n}\\n.border-cyan-500\\\\/20 {\\n  border-color: rgb(6 182 212 / 0.2);\\n}\\n.border-cyan-500\\\\/30 {\\n  border-color: rgb(6 182 212 / 0.3);\\n}\\n.border-emerald-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(167 243 208 / var(--tw-border-opacity, 1));\\n}\\n.border-emerald-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));\\n}\\n.border-emerald-400\\\\/30 {\\n  border-color: rgb(52 211 153 / 0.3);\\n}\\n.border-emerald-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));\\n}\\n.border-emerald-500\\\\/20 {\\n  border-color: rgb(16 185 129 / 0.2);\\n}\\n.border-emerald-500\\\\/30 {\\n  border-color: rgb(16 185 129 / 0.3);\\n}\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.border-indigo-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));\\n}\\n.border-indigo-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));\\n}\\n.border-indigo-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));\\n}\\n.border-indigo-500\\\\/20 {\\n  border-color: rgb(99 102 241 / 0.2);\\n}\\n.border-indigo-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));\\n}\\n.border-orange-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\\n}\\n.border-orange-400\\\\/30 {\\n  border-color: rgb(251 146 60 / 0.3);\\n}\\n.border-orange-500\\\\/20 {\\n  border-color: rgb(249 115 22 / 0.2);\\n}\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\n.border-red-500\\\\/20 {\\n  border-color: rgb(239 68 68 / 0.2);\\n}\\n.border-red-500\\\\/30 {\\n  border-color: rgb(239 68 68 / 0.3);\\n}\\n.border-red-500\\\\/50 {\\n  border-color: rgb(239 68 68 / 0.5);\\n}\\n.border-red-700 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));\\n}\\n.border-slate-400\\\\/30 {\\n  border-color: rgb(148 163 184 / 0.3);\\n}\\n.border-slate-500\\\\/20 {\\n  border-color: rgb(100 116 139 / 0.2);\\n}\\n.border-slate-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n}\\n.border-slate-600\\\\/30 {\\n  border-color: rgb(71 85 105 / 0.3);\\n}\\n.border-slate-600\\\\/40 {\\n  border-color: rgb(71 85 105 / 0.4);\\n}\\n.border-slate-600\\\\/50 {\\n  border-color: rgb(71 85 105 / 0.5);\\n}\\n.border-slate-700 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));\\n}\\n.border-slate-700\\\\/50 {\\n  border-color: rgb(51 65 85 / 0.5);\\n}\\n.border-slate-800 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));\\n}\\n.border-transparent {\\n  border-color: transparent;\\n}\\n.border-white\\\\/10 {\\n  border-color: rgb(255 255 255 / 0.1);\\n}\\n.border-white\\\\/30 {\\n  border-color: rgb(255 255 255 / 0.3);\\n}\\n.border-yellow-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-500\\\\/20 {\\n  border-color: rgb(234 179 8 / 0.2);\\n}\\n.border-yellow-500\\\\/30 {\\n  border-color: rgb(234 179 8 / 0.3);\\n}\\n.border-yellow-700 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-400\\\\/50 {\\n  border-color: rgb(96 165 250 / 0.5);\\n}\\n.border-emerald-400\\\\/50 {\\n  border-color: rgb(52 211 153 / 0.5);\\n}\\n.border-t-cyan-400 {\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(34 211 238 / var(--tw-border-opacity, 1));\\n}\\n.border-t-slate-400 {\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(148 163 184 / var(--tw-border-opacity, 1));\\n}\\n.border-t-transparent {\\n  border-top-color: transparent;\\n}\\n.border-t-white {\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.bg-amber-500\\\\/10 {\\n  background-color: rgb(245 158 11 / 0.1);\\n}\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n}\\n.bg-black\\\\/50 {\\n  background-color: rgb(0 0 0 / 0.5);\\n}\\n.bg-blue-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500\\\\/10 {\\n  background-color: rgb(59 130 246 / 0.1);\\n}\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-500\\\\/10 {\\n  background-color: rgb(6 182 212 / 0.1);\\n}\\n.bg-emerald-300\\\\/40 {\\n  background-color: rgb(110 231 183 / 0.4);\\n}\\n.bg-emerald-300\\\\/60 {\\n  background-color: rgb(110 231 183 / 0.6);\\n}\\n.bg-emerald-300\\\\/80 {\\n  background-color: rgb(110 231 183 / 0.8);\\n}\\n.bg-emerald-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(52 211 153 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-500\\\\/10 {\\n  background-color: rgb(16 185 129 / 0.1);\\n}\\n.bg-emerald-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(5 150 105 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-600\\\\/20 {\\n  background-color: rgb(5 150 105 / 0.2);\\n}\\n.bg-emerald-900\\\\/30 {\\n  background-color: rgb(6 78 59 / 0.3);\\n}\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-800 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-900 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(129 140 248 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-500\\\\/10 {\\n  background-color: rgb(99 102 241 / 0.1);\\n}\\n.bg-indigo-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-500\\\\/10 {\\n  background-color: rgb(249 115 22 / 0.1);\\n}\\n.bg-purple-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500\\\\/10 {\\n  background-color: rgb(239 68 68 / 0.1);\\n}\\n.bg-red-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-900\\\\/50 {\\n  background-color: rgb(127 29 29 / 0.5);\\n}\\n.bg-slate-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-500\\\\/10 {\\n  background-color: rgb(100 116 139 / 0.1);\\n}\\n.bg-slate-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-700 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-700\\\\/50 {\\n  background-color: rgb(51 65 85 / 0.5);\\n}\\n.bg-slate-700\\\\/60 {\\n  background-color: rgb(51 65 85 / 0.6);\\n}\\n.bg-slate-800 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-800\\\\/40 {\\n  background-color: rgb(30 41 59 / 0.4);\\n}\\n.bg-slate-800\\\\/50 {\\n  background-color: rgb(30 41 59 / 0.5);\\n}\\n.bg-slate-800\\\\/60 {\\n  background-color: rgb(30 41 59 / 0.6);\\n}\\n.bg-slate-900 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-900\\\\/30 {\\n  background-color: rgb(15 23 42 / 0.3);\\n}\\n.bg-slate-900\\\\/50 {\\n  background-color: rgb(15 23 42 / 0.5);\\n}\\n.bg-slate-900\\\\/60 {\\n  background-color: rgb(15 23 42 / 0.6);\\n}\\n.bg-slate-950 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 6 23 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white\\\\/5 {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\n.bg-yellow-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500\\\\/10 {\\n  background-color: rgb(234 179 8 / 0.1);\\n}\\n.bg-yellow-900\\\\/30 {\\n  background-color: rgb(113 63 18 / 0.3);\\n}\\n.bg-yellow-900\\\\/50 {\\n  background-color: rgb(113 63 18 / 0.5);\\n}\\n.bg-yellow-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\\n}\\n.bg-opacity-50 {\\n  --tw-bg-opacity: 0.5;\\n}\\n.bg-opacity-75 {\\n  --tw-bg-opacity: 0.75;\\n}\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-amber-500 {\\n  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-amber-500\\\\/20 {\\n  --tw-gradient-from: rgb(245 158 11 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-50 {\\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500 {\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500\\\\/20 {\\n  --tw-gradient-from: rgb(59 130 246 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-900\\\\/20 {\\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400 {\\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-500 {\\n  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-emerald-400 {\\n  --tw-gradient-from: #34d399 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(52 211 153 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-emerald-500 {\\n  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-emerald-500\\\\/20 {\\n  --tw-gradient-from: rgb(16 185 129 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-emerald-600 {\\n  --tw-gradient-from: #059669 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-400 {\\n  --tw-gradient-from: #4ade80 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-orange-500 {\\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-orange-500\\\\/20 {\\n  --tw-gradient-from: rgb(249 115 22 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-400 {\\n  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-500\\\\/20 {\\n  --tw-gradient-from: rgb(239 68 68 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-600 {\\n  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-slate-800\\\\/95 {\\n  --tw-gradient-from: rgb(30 41 59 / 0.95) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-teal-400 {\\n  --tw-gradient-from: #2dd4bf var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(45 212 191 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-violet-500 {\\n  --tw-gradient-from: #8b5cf6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(139 92 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-400 {\\n  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500\\\\/30 {\\n  --tw-gradient-from: rgb(59 130 246 / 0.3) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-emerald-500\\\\/30 {\\n  --tw-gradient-from: rgb(16 185 129 / 0.3) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.via-cyan-500\\\\/20 {\\n  --tw-gradient-to: rgb(6 182 212 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(6 182 212 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.via-orange-500\\\\/20 {\\n  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(249 115 22 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.via-purple-900\\\\/20 {\\n  --tw-gradient-to: rgb(88 28 135 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(88 28 135 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.to-blue-400 {\\n  --tw-gradient-to: #60a5fa var(--tw-gradient-to-position);\\n}\\n.to-blue-500 {\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\n.to-cyan-400 {\\n  --tw-gradient-to: #22d3ee var(--tw-gradient-to-position);\\n}\\n.to-cyan-500 {\\n  --tw-gradient-to: #06b6d4 var(--tw-gradient-to-position);\\n}\\n.to-cyan-500\\\\/20 {\\n  --tw-gradient-to: rgb(6 182 212 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-cyan-900\\\\/20 {\\n  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-emerald-500 {\\n  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);\\n}\\n.to-indigo-100 {\\n  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);\\n}\\n.to-indigo-500\\\\/20 {\\n  --tw-gradient-to: rgb(99 102 241 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-orange-500 {\\n  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);\\n}\\n.to-pink-500 {\\n  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);\\n}\\n.to-purple-500\\\\/20 {\\n  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-600 {\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\n.to-red-500 {\\n  --tw-gradient-to: #ef4444 var(--tw-gradient-to-position);\\n}\\n.to-red-500\\\\/20 {\\n  --tw-gradient-to: rgb(239 68 68 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-slate-900\\\\/95 {\\n  --tw-gradient-to: rgb(15 23 42 / 0.95) var(--tw-gradient-to-position);\\n}\\n.to-yellow-500 {\\n  --tw-gradient-to: #eab308 var(--tw-gradient-to-position);\\n}\\n.to-yellow-500\\\\/20 {\\n  --tw-gradient-to: rgb(234 179 8 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-cyan-500\\\\/30 {\\n  --tw-gradient-to: rgb(6 182 212 / 0.3) var(--tw-gradient-to-position);\\n}\\n.to-purple-500\\\\/30 {\\n  --tw-gradient-to: rgb(168 85 247 / 0.3) var(--tw-gradient-to-position);\\n}\\n.bg-clip-text {\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\n.object-cover {\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\n.p-0 {\\n  padding: 0px;\\n}\\n.p-1 {\\n  padding: 0.25rem;\\n}\\n.p-12 {\\n  padding: 3rem;\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\n.px-1\\\\.5 {\\n  padding-left: 0.375rem;\\n  padding-right: 0.375rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.py-0\\\\.5 {\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-1\\\\.5 {\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.py-10 {\\n  padding-top: 2.5rem;\\n  padding-bottom: 2.5rem;\\n}\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-2\\\\.5 {\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.pb-1 {\\n  padding-bottom: 0.25rem;\\n}\\n.pl-3 {\\n  padding-left: 0.75rem;\\n}\\n.pl-6 {\\n  padding-left: 1.5rem;\\n}\\n.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-right {\\n  text-align: right;\\n}\\n.align-middle {\\n  vertical-align: middle;\\n}\\n.font-mono {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-5xl {\\n  font-size: 3rem;\\n  line-height: 1;\\n}\\n.text-\\\\[0\\\\.85em\\\\] {\\n  font-size: 0.85em;\\n}\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-extrabold {\\n  font-weight: 800;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.uppercase {\\n  text-transform: uppercase;\\n}\\n.capitalize {\\n  text-transform: capitalize;\\n}\\n.italic {\\n  font-style: italic;\\n}\\n.leading-4 {\\n  line-height: 1rem;\\n}\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\n.tracking-wide {\\n  letter-spacing: 0.025em;\\n}\\n.tracking-wider {\\n  letter-spacing: 0.05em;\\n}\\n.text-amber-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(254 243 199 / var(--tw-text-opacity, 1));\\n}\\n.text-amber-300\\\\/80 {\\n  color: rgb(252 211 77 / 0.8);\\n}\\n.text-blue-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(207 250 254 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-300\\\\/80 {\\n  color: rgb(103 232 249 / 0.8);\\n}\\n.text-cyan-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 94 117 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 250 229 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(167 243 208 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(110 231 183 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(4 120 87 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(6 95 70 / var(--tw-text-opacity, 1));\\n}\\n.text-fuchsia-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(240 171 252 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(165 180 252 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 237 213 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(253 186 116 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\\n}\\n.text-red-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\\n}\\n.text-red-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-red-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(241 245 249 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(226 232 240 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\\n}\\n.text-transparent {\\n  color: transparent;\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(254 240 138 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.underline {\\n  text-decoration-line: underline;\\n}\\n.line-through {\\n  text-decoration-line: line-through;\\n}\\n.decoration-slate-400 {\\n  text-decoration-color: #94a3b8;\\n}\\n.underline-offset-2 {\\n  text-underline-offset: 2px;\\n}\\n.placeholder-cyan-400\\\\/50::-moz-placeholder {\\n  color: rgb(34 211 238 / 0.5);\\n}\\n.placeholder-cyan-400\\\\/50::placeholder {\\n  color: rgb(34 211 238 / 0.5);\\n}\\n.placeholder-gray-500::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-gray-500::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-slate-400::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-slate-400::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-gray-400::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-gray-400::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\\n}\\n.opacity-10 {\\n  opacity: 0.1;\\n}\\n.opacity-25 {\\n  opacity: 0.25;\\n}\\n.opacity-30 {\\n  opacity: 0.3;\\n}\\n.opacity-60 {\\n  opacity: 0.6;\\n}\\n.opacity-75 {\\n  opacity: 0.75;\\n}\\n.opacity-80 {\\n  opacity: 0.8;\\n}\\n.opacity-90 {\\n  opacity: 0.9;\\n}\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-emerald-500\\\\/25 {\\n  --tw-shadow-color: rgb(16 185 129 / 0.25);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\n.shadow-red-500\\\\/25 {\\n  --tw-shadow-color: rgb(239 68 68 / 0.25);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\n.outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.ring-1 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.ring-inset {\\n  --tw-ring-inset: inset;\\n}\\n.ring-cyan-500\\\\/30 {\\n  --tw-ring-color: rgb(6 182 212 / 0.3);\\n}\\n.ring-emerald-500\\\\/20 {\\n  --tw-ring-color: rgb(16 185 129 / 0.2);\\n}\\n.ring-emerald-500\\\\/30 {\\n  --tw-ring-color: rgb(16 185 129 / 0.3);\\n}\\n.ring-indigo-500\\\\/30 {\\n  --tw-ring-color: rgb(99 102 241 / 0.3);\\n}\\n.ring-orange-500\\\\/30 {\\n  --tw-ring-color: rgb(249 115 22 / 0.3);\\n}\\n.ring-red-500\\\\/20 {\\n  --tw-ring-color: rgb(239 68 68 / 0.2);\\n}\\n.ring-red-500\\\\/30 {\\n  --tw-ring-color: rgb(239 68 68 / 0.3);\\n}\\n.ring-slate-500\\\\/30 {\\n  --tw-ring-color: rgb(100 116 139 / 0.3);\\n}\\n.ring-yellow-500\\\\/30 {\\n  --tw-ring-color: rgb(234 179 8 / 0.3);\\n}\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-shadow {\\n  transition-property: box-shadow;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.\\\\[animation-delay\\\\:120ms\\\\] {\\n  animation-delay: 120ms;\\n}\\n.\\\\[animation-delay\\\\:240ms\\\\] {\\n  animation-delay: 240ms;\\n}\\n\\n:root {\\n  --foreground-rgb: 0, 0, 0;\\n  --background-start-rgb: 214, 219, 220;\\n  --background-end-rgb: 255, 255, 255;\\n  --bg-primary: #0B1020;\\n  --brand-emerald: #10B981;\\n  --brand-cyan: #22D3EE;\\n  --brand-indigo: #6366F1;\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  :root {\\n    --foreground-rgb: 255, 255, 255;\\n    --background-start-rgb: 0, 0, 0;\\n    --background-end-rgb: 0, 0, 0;\\n  }\\n}\\n\\nbody {\\n  color: rgb(var(--foreground-rgb));\\n  background: linear-gradient(\\n      to bottom,\\n      transparent,\\n      rgb(var(--background-end-rgb))\\n    )\\n    rgb(var(--background-start-rgb));\\n}\\n\\n.last\\\\:mb-0:last-child {\\n  margin-bottom: 0px;\\n}\\n\\n.last\\\\:border-b-0:last-child {\\n  border-bottom-width: 0px;\\n}\\n\\n.hover\\\\:scale-105:hover {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.hover\\\\:border-slate-500:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));\\n}\\n\\n.hover\\\\:border-slate-500\\\\/50:hover {\\n  border-color: rgb(100 116 139 / 0.5);\\n}\\n\\n.hover\\\\:bg-blue-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-emerald-500:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-emerald-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(4 120 87 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-200:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-green-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-indigo-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-red-500\\\\/10:hover {\\n  background-color: rgb(239 68 68 / 0.1);\\n}\\n\\n.hover\\\\:bg-red-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-red-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-slate-500:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-slate-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-slate-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-slate-700\\\\/50:hover {\\n  background-color: rgb(51 65 85 / 0.5);\\n}\\n\\n.hover\\\\:bg-slate-700\\\\/60:hover {\\n  background-color: rgb(51 65 85 / 0.6);\\n}\\n\\n.hover\\\\:bg-slate-800:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-slate-800\\\\/60:hover {\\n  background-color: rgb(30 41 59 / 0.6);\\n}\\n\\n.hover\\\\:bg-white\\\\/5:hover {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\n\\n.hover\\\\:bg-yellow-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:from-cyan-400:hover {\\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n\\n.hover\\\\:from-emerald-500:hover {\\n  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n\\n.hover\\\\:from-red-500:hover {\\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n\\n.hover\\\\:to-blue-400:hover {\\n  --tw-gradient-to: #60a5fa var(--tw-gradient-to-position);\\n}\\n\\n.hover\\\\:to-emerald-400:hover {\\n  --tw-gradient-to: #34d399 var(--tw-gradient-to-position);\\n}\\n\\n.hover\\\\:to-red-400:hover {\\n  --tw-gradient-to: #f87171 var(--tw-gradient-to-position);\\n}\\n\\n.hover\\\\:text-blue-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-emerald-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(110 231 183 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-emerald-400:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-gray-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-gray-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-gray-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-indigo-500:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(99 102 241 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-indigo-800:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-red-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-red-400:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-slate-100:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(241 245 249 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-slate-200:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(226 232 240 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\n\\n.hover\\\\:opacity-100:hover {\\n  opacity: 1;\\n}\\n\\n.hover\\\\:shadow-lg:hover {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-md:hover {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-cyan-500\\\\/25:hover {\\n  --tw-shadow-color: rgb(6 182 212 / 0.25);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\n\\n.focus\\\\:border-cyan-400:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));\\n}\\n\\n.focus\\\\:border-emerald-400\\\\/50:focus {\\n  border-color: rgb(52 211 153 / 0.5);\\n}\\n\\n.focus\\\\:border-indigo-500:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));\\n}\\n\\n.focus\\\\:border-red-400\\\\/50:focus {\\n  border-color: rgb(248 113 113 / 0.5);\\n}\\n\\n.focus\\\\:border-red-500\\\\/40:focus {\\n  border-color: rgb(239 68 68 / 0.4);\\n}\\n\\n.focus\\\\:bg-white\\\\/5:focus {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\n\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus\\\\:ring-blue-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-cyan-400\\\\/50:focus {\\n  --tw-ring-color: rgb(34 211 238 / 0.5);\\n}\\n\\n.focus\\\\:ring-emerald-400\\\\/50:focus {\\n  --tw-ring-color: rgb(52 211 153 / 0.5);\\n}\\n\\n.focus\\\\:ring-emerald-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(16 185 129 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-emerald-500\\\\/60:focus {\\n  --tw-ring-color: rgb(16 185 129 / 0.6);\\n}\\n\\n.focus\\\\:ring-indigo-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-red-400\\\\/50:focus {\\n  --tw-ring-color: rgb(248 113 113 / 0.5);\\n}\\n\\n.focus\\\\:ring-red-500\\\\/40:focus {\\n  --tw-ring-color: rgb(239 68 68 / 0.4);\\n}\\n\\n.focus\\\\:ring-slate-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.focus\\\\:ring-offset-slate-700:focus {\\n  --tw-ring-offset-color: #334155;\\n}\\n\\n.focus\\\\:ring-offset-slate-800:focus {\\n  --tw-ring-offset-color: #1e293b;\\n}\\n\\n.focus\\\\:ring-offset-slate-900:focus {\\n  --tw-ring-offset-color: #0f172a;\\n}\\n\\n.active\\\\:scale-95:active {\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\n\\n.disabled\\\\:border-slate-700\\\\/50:disabled {\\n  border-color: rgb(51 65 85 / 0.5);\\n}\\n\\n.disabled\\\\:bg-slate-800:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n}\\n\\n.disabled\\\\:opacity-40:disabled {\\n  opacity: 0.4;\\n}\\n\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\n\\n.disabled\\\\:opacity-60:disabled {\\n  opacity: 0.6;\\n}\\n\\n.group:hover .group-hover\\\\:text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\n\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .sm\\\\:justify-between {\\n    justify-content: space-between;\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .sm\\\\:text-sm {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\n\\n@media (min-width: 768px) {\\n\\n  .md\\\\:mb-2 {\\n    margin-bottom: 0.5rem;\\n  }\\n\\n  .md\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .md\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .md\\\\:h-10 {\\n    height: 2.5rem;\\n  }\\n\\n  .md\\\\:h-5 {\\n    height: 1.25rem;\\n  }\\n\\n  .md\\\\:h-4 {\\n    height: 1rem;\\n  }\\n\\n  .md\\\\:w-10 {\\n    width: 2.5rem;\\n  }\\n\\n  .md\\\\:w-5 {\\n    width: 1.25rem;\\n  }\\n\\n  .md\\\\:w-4 {\\n    width: 1rem;\\n  }\\n\\n  .md\\\\:max-w-\\\\[70\\\\%\\\\] {\\n    max-width: 70%;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .md\\\\:gap-3 {\\n    gap: 0.75rem;\\n  }\\n\\n  .md\\\\:gap-4 {\\n    gap: 1rem;\\n  }\\n\\n  .md\\\\:space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n  }\\n\\n  .md\\\\:p-4 {\\n    padding: 1rem;\\n  }\\n\\n  .md\\\\:px-4 {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .md\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .md\\\\:py-3 {\\n    padding-top: 0.75rem;\\n    padding-bottom: 0.75rem;\\n  }\\n\\n  .md\\\\:py-6 {\\n    padding-top: 1.5rem;\\n    padding-bottom: 1.5rem;\\n  }\\n\\n  .md\\\\:text-3xl {\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n\\n  .md\\\\:text-base {\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n\\n  .md\\\\:text-sm {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\n\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:col-span-1 {\\n    grid-column: span 1 / span 1;\\n  }\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n\\n@media (min-width: 1280px) {\\n\\n  .xl\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;AAiCZ,oDAAoD;AAElD;EAAA,kBAAwG;EAAxG,aAAwG;EAAxG,iBAAwG;EAAxG,mBAAwG;EAAxG,uBAAwG;EAAxG,gBAAwG;EAAxG,iBAAwG;EAAxG,oBAAwG;EAAxG,kBAAwG;EAAxG;AAAwG;AAAxG;;EAAA;IAAA,oBAAwG;IAAxG;EAAwG;AAAA;AAAxG;;EAAA;IAAA,kBAAwG;IAAxG;EAAwG;AAAA;AAD1G;IAEE,mCAAmC;EACrC;AAEA,mCAAmC;AAEjC;EAAA,oBAAsD;EAAtD,kBAAsD;EAAtD,UAAsD;EAAtD,YAAsD;IACtD;;AADsD;AAKxD,wBAAwB;AAEtB;EAAA,oBAA2C;EAA3C,kBAA2C;EAA3C,UAA2C;IAC3C,aAAa;IACb,qGAAqG;IACrG;AAH2C;AAM7C,qBAAqB;AAEnB;EAAA,kBAAmG;EAAnG,sBAAmG;EAAnG,iBAAmG;EAAnG,oCAAmG;EAAnG,yCAAmG;EAAnG,eAAmG;EAAnG,gDAAmG;EAAnG,6DAAmG;EAAnG,uGAAmG;EAAnG,8BAAmG;EAAnG;AAAmG;AAAnG;;EAAA;IAAA;EAAmG;AAAA;AAGrG,4GAA4G;AAE1G;EAAA,oBAAwD;EAAxD,kBAAwD;EAAxD,WAAwD;EAAxD,sBAAwD;IACxD;AADwD;AAI1D,gCAAgC;AAChC;IACE,sGAAsG;IACtG,6BAA6B;IAC7B,qBAAqB;IACrB,kBAAkB;EACpB;AAEA,sCAAsC;AAEpC;EAAA,kBAAsU;EAAtU,aAAsU;EAAtU,WAAsU;EAAtU,mBAAsU;EAAtU,uBAAsU;EAAtU,YAAsU;EAAtU,qBAAsU;EAAtU,qCAAsU;EAAtU,oBAAsU;EAAtU,uBAAsU;EAAtU,kBAAsU;EAAtU,mBAAsU;EAAtU,mBAAsU;EAAtU,oBAAsU;EAAtU,gBAAsU;EAAtU,oBAAsU;EAAtU,mDAAsU;EAAtU,2GAAsU;EAAtU,yGAAsU;EAAtU,4FAAsU;EAAtU,uCAAsU;EAAtU,+FAAsU;EAAtU,wDAAsU;EAAtU;AAAsU;AAAtU;EAAA,qCAAsU;EAAtU;AAAsU;AAAtU;EAAA,8BAAsU;EAAtU,mBAAsU;EAAtU,2GAAsU;EAAtU,yGAAsU;EAAtU,4FAAsU;EAAtU;AAAsU;AAAtU;EAAA,mBAAsU;EAAtU;AAAsU;AAGxU,uDAAuD;AAErD;EAAA,kBAA0F;EAA1F,UAA0F;EAA1F,YAA0F;EAA1F,qBAA0F;EAA1F,UAA0F;EAA1F,qBAA0F;EAA1F,iLAA0F;EAA1F,4BAA0F;EAA1F,wDAA0F;EAA1F,0BAA0F;IAC1F;AAD0F;AAI5F,uCAAuC;AAErC;EAAA,mBAAgC;EAAhC,oBAAgC;EAAhC;AAAgC;AAGlC,uCAAuC;AAErC;EAAA,iBAA4H;EAA5H,kBAA4H;EAA5H,aAA4H;EAA5H,YAA4H;EAA5H,WAA4H;EAA5H,mBAA4H;EAA5H,uBAA4H;EAA5H,qBAA4H;EAA5H,yCAA4H;EAA5H,6EAA4H;EAA5H,iGAA4H;EAA5H,uGAA4H;EAA5H,2GAA4H;EAA5H,yGAA4H;EAA5H,4FAA4H;EAA5H,uCAA4H;EAA5H,6BAA4H;EAA5H;AAA4H;AAzFhI;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,oBAAmB;EAAnB,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,2BAAmB;IAAnB;EAAmB;;EAAnB;IAAA,eAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA,wBAAmB;KAAnB,qBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;UAAnB;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,yCAAmB;EAAnB;AAAmB;AAAnB;EAAA,wCAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB;EACE,yBAAyB;EACzB,qCAAqC;EACrC,mCAAmC;EACnC,qBAAqB;EACrB,wBAAwB;EACxB,qBAAqB;EACrB,uBAAuB;AACzB;;AAEA;EACE;IACE,+BAA+B;IAC/B,+BAA+B;IAC/B,6BAA6B;EAC/B;AACF;;AAEA;EACE,iCAAiC;EACjC;;;;;oCAKkC;AACpC;;AA9BA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,sBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,4DA+FA;EA/FA,oEA+FA;EA/FA;AA+FA;;AA/FA;EAAA,4DA+FA;EA/FA,oEA+FA;EA/FA;AA+FA;;AA/FA;EAAA,4DA+FA;EA/FA,mEA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,+EA+FA;EA/FA,mGA+FA;EA/FA;AA+FA;;AA/FA;EAAA,6EA+FA;EA/FA,iGA+FA;EA/FA;AA+FA;;AA/FA;EAAA,wCA+FA;EA/FA;AA+FA;;AA/FA;EAAA,sBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,sBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,8BA+FA;EA/FA;AA+FA;;AA/FA;EAAA,2GA+FA;EA/FA,yGA+FA;EA/FA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,iBA+FA;EA/FA,iBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,kBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;EAAA,oBA+FA;EA/FA;AA+FA;;AA/FA;EAAA;AA+FA;;AA/FA;;EAAA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA,oBA+FA;IA/FA;EA+FA;;EA/FA;IAAA,mBA+FA;IA/FA;EA+FA;AAAA;;AA/FA;;EAAA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA,uBA+FA;IA/FA,4DA+FA;IA/FA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA,kBA+FA;IA/FA;EA+FA;;EA/FA;IAAA,oBA+FA;IA/FA;EA+FA;;EA/FA;IAAA,oBA+FA;IA/FA;EA+FA;;EA/FA;IAAA,mBA+FA;IA/FA;EA+FA;;EA/FA;IAAA,mBA+FA;IA/FA;EA+FA;;EA/FA;IAAA,eA+FA;IA/FA;EA+FA;;EA/FA;IAAA,mBA+FA;IA/FA;EA+FA;AAAA;;AA/FA;;EAAA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA;EA+FA;;EA/FA;IAAA,kBA+FA;IA/FA;EA+FA;AAAA;;AA/FA;;EAAA;IAAA;EA+FA;AAAA\",\"sourcesContent\":[\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n:root {\\n  --foreground-rgb: 0, 0, 0;\\n  --background-start-rgb: 214, 219, 220;\\n  --background-end-rgb: 255, 255, 255;\\n  --bg-primary: #0B1020;\\n  --brand-emerald: #10B981;\\n  --brand-cyan: #22D3EE;\\n  --brand-indigo: #6366F1;\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  :root {\\n    --foreground-rgb: 255, 255, 255;\\n    --background-start-rgb: 0, 0, 0;\\n    --background-end-rgb: 0, 0, 0;\\n  }\\n}\\n\\nbody {\\n  color: rgb(var(--foreground-rgb));\\n  background: linear-gradient(\\n      to bottom,\\n      transparent,\\n      rgb(var(--background-end-rgb))\\n    )\\n    rgb(var(--background-start-rgb));\\n}\\n\\n@layer components {\\n  /* App background container for dark agentic theme */\\n  .app-bg {\\n    @apply relative min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 overflow-hidden;\\n    background-color: var(--bg-primary);\\n  }\\n\\n  /* Radial glow background overlay */\\n  .glow-overlay {\\n    @apply pointer-events-none absolute inset-0 opacity-40;\\n    background: radial-gradient(1200px 600px at -10% -20%, #2E5BFF 10%, transparent 60%),\\n                radial-gradient(800px 400px at 120% 110%, var(--brand-emerald) 5%, transparent 50%);\\n  }\\n\\n  /* Subtle grid overlay */\\n  .grid-overlay {\\n    @apply pointer-events-none absolute inset-0;\\n    opacity: 0.08;\\n    background-image: radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.6) 1px, transparent 1px);\\n    background-size: 50px 50px;\\n  }\\n\\n  /* Glass card shell */\\n  .glass-card {\\n    @apply relative rounded-xl border border-white/10 bg-white/5 backdrop-blur-xl shadow-2xl p-6 sm:p-8;\\n  }\\n\\n  /* Optional gradient accent layer for cards (use as an empty absolutely positioned div inside .glass-card) */\\n  .card-accent {\\n    @apply pointer-events-none absolute -inset-px rounded-xl;\\n    background-image: linear-gradient(90deg, color-mix(in oklab, var(--brand-emerald) 25%, transparent), color-mix(in oklab, var(--brand-cyan) 25%, transparent), color-mix(in oklab, var(--brand-indigo) 25%, transparent));\\n  }\\n\\n  /* Brand gradient text utility */\\n  .brand-gradient-text {\\n    background-image: linear-gradient(90deg, var(--brand-emerald), var(--brand-cyan), var(--brand-indigo));\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    color: transparent;\\n  }\\n\\n  /* Primary CTA button for dark theme */\\n  .primary-button {\\n    @apply relative w-full flex items-center justify-center gap-3 py-3 px-4 rounded-lg text-sm font-medium text-slate-100 bg-slate-900/60 ring-1 ring-white/10 hover:bg-slate-900/80 hover:ring-emerald-400/50 focus:outline-none focus:ring-2 focus:ring-emerald-400/60 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;\\n  }\\n\\n  /* Hover glow layer to nest inside buttons if desired */\\n  .button-glow {\\n    @apply absolute inset-0 -z-10 rounded-lg opacity-0 blur-xl transition-opacity duration-300;\\n    background-image: linear-gradient(90deg, color-mix(in oklab, var(--brand-emerald) 20%, transparent), color-mix(in oklab, var(--brand-cyan) 20%, transparent), color-mix(in oklab, var(--brand-indigo) 20%, transparent));\\n  }\\n\\n  /* Subtle muted text for descriptions */\\n  .subtle-text {\\n    @apply text-sm text-slate-300/80;\\n  }\\n\\n  /* Small rounded icon badge container */\\n  .icon-badge {\\n    @apply mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-white/5 ring-1 ring-white/10 shadow-md backdrop-blur;\\n  }\\n}\\n\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./src/styles/globals.css\n"));

/***/ }),

/***/ "./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const storageKey = \"teno:auth:user\";\n    const lastUserIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const current = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(current);\n            try {\n                if (current) {\n                    localStorage.setItem(storageKey, JSON.stringify(current));\n                } else {\n                    localStorage.removeItem(storageKey);\n                }\n            } catch (e) {}\n            try {\n                const prevId = lastUserIdRef.current;\n                var _current_id;\n                const nextId = (_current_id = current === null || current === void 0 ? void 0 : current.id) !== null && _current_id !== void 0 ? _current_id : null;\n                const changed = prevId !== nextId;\n                lastUserIdRef.current = nextId;\n                if (changed && \"object\" !== \"undefined\") {\n                    const evtName = current ? \"teno:auth:login\" : \"teno:auth:logout\";\n                    window.dispatchEvent(new CustomEvent(\"teno:auth:change\", {\n                        detail: {\n                            user: current\n                        }\n                    }));\n                    window.dispatchEvent(new CustomEvent(evtName, {\n                        detail: {\n                            user: current\n                        }\n                    }));\n                }\n            } catch (e) {}\n        } catch (err) {\n            setError(\"Failed to load user\");\n            setUser(null);\n            try {\n                localStorage.removeItem(storageKey);\n            } catch (e) {}\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const loginWithGoogle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((nextPath)=>{\n        (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.redirectToGoogleAuth)(nextPath);\n    }, []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        // Optimistically clear local state for snappier UX\n        setUser(null);\n        try {\n            localStorage.removeItem(storageKey);\n        } catch (e) {}\n        try {\n            await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.performLogout)();\n        } finally{\n            // Always refresh after logout to ensure backend/session is in sync\n            await refresh();\n        }\n    }, [\n        refresh\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Optimistically hydrate from localStorage for fast first paint\n        try {\n            const raw = localStorage.getItem(storageKey);\n            if (raw) {\n                const cached = JSON.parse(raw);\n                setUser(cached);\n                var _cached_id;\n                lastUserIdRef.current = (_cached_id = cached === null || cached === void 0 ? void 0 : cached.id) !== null && _cached_id !== void 0 ? _cached_id : null;\n            }\n        } catch (e) {}\n        // On initial mount, try to load the current user from backend\n        refresh();\n        // Listen for global unauthorized signals to immediately drop user state\n        const onUnauthorized = ()=>{\n            (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.clearClientAuthArtifacts)();\n            setUser(null);\n            lastUserIdRef.current = null;\n        };\n        if (true) {\n            window.addEventListener(\"teno:auth:unauthorized\", onUnauthorized);\n        }\n        return ()=>{\n            if (true) {\n                window.removeEventListener(\"teno:auth:unauthorized\", onUnauthorized);\n            }\n        };\n    }, [\n        refresh\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            isLoading,\n            error,\n            refresh,\n            loginWithGoogle,\n            logout\n        }), [\n        user,\n        isLoading,\n        error,\n        refresh,\n        loginWithGoogle,\n        logout\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"i1Hafzw8L1e75c2rxUDcFO0bvko=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!ctx) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return ctx;\n}\n_s1(useAuth, \"/dMy7t63NXD4eYACoT93CePwGrg=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29udGV4dC9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXFHO0FBQ21CO0FBV3hILE1BQU1XLDRCQUFjWCxvREFBYUEsQ0FBK0JZO0FBRXpELFNBQVNDLGFBQWEsS0FBMkM7UUFBM0MsRUFBRUMsUUFBUSxFQUFpQyxHQUEzQzs7SUFDM0IsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdWLCtDQUFRQSxDQUFrQjtJQUNsRCxNQUFNLENBQUNXLFdBQVdDLGFBQWEsR0FBR1osK0NBQVFBLENBQVU7SUFDcEQsTUFBTSxDQUFDYSxPQUFPQyxTQUFTLEdBQUdkLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNZSxhQUFhO0lBQ25CLE1BQU1DLGdCQUFnQmpCLDZDQUFNQSxDQUFnQjtJQUU1QyxNQUFNa0IsVUFBVXRCLGtEQUFXQSxDQUFDO1FBQzFCaUIsYUFBYTtRQUNiRSxTQUFTO1FBQ1QsSUFBSTtZQUNGLE1BQU1JLFVBQVUsTUFBTWpCLDJEQUFjQTtZQUNwQ1MsUUFBUVE7WUFDUixJQUFJO2dCQUNGLElBQUlBLFNBQVM7b0JBQ1hDLGFBQWFDLE9BQU8sQ0FBQ0wsWUFBWU0sS0FBS0MsU0FBUyxDQUFDSjtnQkFDbEQsT0FBTztvQkFDTEMsYUFBYUksVUFBVSxDQUFDUjtnQkFDMUI7WUFDRixFQUFFLFVBQU0sQ0FBQztZQUNULElBQUk7Z0JBQ0YsTUFBTVMsU0FBU1IsY0FBY0UsT0FBTztvQkFDckJBO2dCQUFmLE1BQU1PLFNBQVNQLENBQUFBLGNBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU1EsRUFBRSxjQUFYUix5QkFBQUEsY0FBZTtnQkFDOUIsTUFBTVMsVUFBVUgsV0FBV0M7Z0JBQzNCVCxjQUFjRSxPQUFPLEdBQUdPO2dCQUN4QixJQUFJRSxXQUFXLGFBQWtCLGFBQWE7b0JBQzVDLE1BQU1DLFVBQVVWLFVBQVUsb0JBQW9CO29CQUM5Q1csT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVksb0JBQW9CO3dCQUFFQyxRQUFROzRCQUFFdkIsTUFBTVM7d0JBQVE7b0JBQUU7b0JBQ3JGVyxPQUFPQyxhQUFhLENBQUMsSUFBSUMsWUFBWUgsU0FBUzt3QkFBRUksUUFBUTs0QkFBRXZCLE1BQU1TO3dCQUFRO29CQUFFO2dCQUM1RTtZQUNGLEVBQUUsVUFBTSxDQUFDO1FBQ1gsRUFBRSxPQUFPZSxLQUFLO1lBQ1puQixTQUFTO1lBQ1RKLFFBQVE7WUFDUixJQUFJO2dCQUFFUyxhQUFhSSxVQUFVLENBQUNSO1lBQWEsRUFBRSxVQUFNLENBQUM7UUFDdEQsU0FBVTtZQUNSSCxhQUFhO1FBQ2Y7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNc0Isa0JBQWtCdkMsa0RBQVdBLENBQUMsQ0FBQ3dDO1FBQ25DaEMsaUVBQW9CQSxDQUFDZ0M7SUFDdkIsR0FBRyxFQUFFO0lBRUwsTUFBTUMsU0FBU3pDLGtEQUFXQSxDQUFDO1FBQ3pCLG1EQUFtRDtRQUNuRGUsUUFBUTtRQUNSLElBQUk7WUFBRVMsYUFBYUksVUFBVSxDQUFDUjtRQUFhLEVBQUUsVUFBTSxDQUFDO1FBQ3BELElBQUk7WUFDRixNQUFNYiwwREFBYUE7UUFDckIsU0FBVTtZQUNSLG1FQUFtRTtZQUNuRSxNQUFNZTtRQUNSO0lBQ0YsR0FBRztRQUFDQTtLQUFRO0lBRVpwQixnREFBU0EsQ0FBQztRQUNSLGdFQUFnRTtRQUNoRSxJQUFJO1lBQ0YsTUFBTXdDLE1BQU1sQixhQUFhbUIsT0FBTyxDQUFDdkI7WUFDakMsSUFBSXNCLEtBQUs7Z0JBQ1AsTUFBTUUsU0FBU2xCLEtBQUttQixLQUFLLENBQUNIO2dCQUMxQjNCLFFBQVE2QjtvQkFDZ0JBO2dCQUF4QnZCLGNBQWNFLE9BQU8sR0FBR3FCLENBQUFBLGFBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUWIsRUFBRSxjQUFWYSx3QkFBQUEsYUFBYztZQUN4QztRQUNGLEVBQUUsVUFBTSxDQUFDO1FBQ1QsOERBQThEO1FBQzlEdEI7UUFDQSx3RUFBd0U7UUFDeEUsTUFBTXdCLGlCQUFpQjtZQUNyQnJDLHFFQUF3QkE7WUFDeEJNLFFBQVE7WUFDUk0sY0FBY0UsT0FBTyxHQUFHO1FBQzFCO1FBQ0EsSUFBSSxJQUFrQixFQUFhO1lBQ2pDVyxPQUFPYSxnQkFBZ0IsQ0FBQywwQkFBMEJEO1FBQ3BEO1FBQ0EsT0FBTztZQUNMLElBQUksSUFBa0IsRUFBYTtnQkFDakNaLE9BQU9jLG1CQUFtQixDQUFDLDBCQUEwQkY7WUFDdkQ7UUFDRjtJQUNGLEdBQUc7UUFBQ3hCO0tBQVE7SUFFWixNQUFNMkIsUUFBUTlDLDhDQUFPQSxDQUFtQixJQUFPO1lBQzdDVztZQUNBRTtZQUNBRTtZQUNBSTtZQUNBaUI7WUFDQUU7UUFDRixJQUFJO1FBQUMzQjtRQUFNRTtRQUFXRTtRQUFPSTtRQUFTaUI7UUFBaUJFO0tBQU87SUFFOUQscUJBQ0UsOERBQUMvQixZQUFZd0MsUUFBUTtRQUFDRCxPQUFPQTtrQkFDMUJwQzs7Ozs7O0FBR1A7R0FsR2dCRDtLQUFBQTtBQW9HVCxTQUFTdUM7O0lBQ2QsTUFBTUMsTUFBTW5ELGlEQUFVQSxDQUFDUztJQUN2QixJQUFJLENBQUMwQyxLQUFLO1FBQ1IsTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtJQU5nQkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbnRleHQvQXV0aENvbnRleHQudHN4PzZlZTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ2FsbGJhY2ssIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlTWVtbywgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgQXV0aFVzZXIsIGdldEN1cnJlbnRVc2VyLCBwZXJmb3JtTG9nb3V0LCByZWRpcmVjdFRvR29vZ2xlQXV0aCwgY2xlYXJDbGllbnRBdXRoQXJ0aWZhY3RzIH0gZnJvbSAnLi4vdXRpbHMvYXV0aCc7XHJcblxyXG5pbnRlcmZhY2UgQXV0aENvbnRleHRWYWx1ZSB7XHJcbiAgdXNlcjogQXV0aFVzZXIgfCBudWxsO1xyXG4gIGlzTG9hZGluZzogYm9vbGVhbjtcclxuICBlcnJvcjogc3RyaW5nIHwgbnVsbDtcclxuICByZWZyZXNoOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xyXG4gIGxvZ2luV2l0aEdvb2dsZTogKG5leHRQYXRoPzogc3RyaW5nKSA9PiB2b2lkO1xyXG4gIGxvZ291dDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcclxufVxyXG5cclxuY29uc3QgQXV0aENvbnRleHQgPSBjcmVhdGVDb250ZXh0PEF1dGhDb250ZXh0VmFsdWUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gQXV0aFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxBdXRoVXNlciB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZTxib29sZWFuPih0cnVlKTtcclxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IHN0b3JhZ2VLZXkgPSAndGVubzphdXRoOnVzZXInO1xyXG4gIGNvbnN0IGxhc3RVc2VySWRSZWYgPSB1c2VSZWY8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIGNvbnN0IHJlZnJlc2ggPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XHJcbiAgICBzZXRFcnJvcihudWxsKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGN1cnJlbnQgPSBhd2FpdCBnZXRDdXJyZW50VXNlcigpO1xyXG4gICAgICBzZXRVc2VyKGN1cnJlbnQpO1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGlmIChjdXJyZW50KSB7XHJcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShzdG9yYWdlS2V5LCBKU09OLnN0cmluZ2lmeShjdXJyZW50KSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKHN0b3JhZ2VLZXkpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCB7fVxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHByZXZJZCA9IGxhc3RVc2VySWRSZWYuY3VycmVudDtcclxuICAgICAgICBjb25zdCBuZXh0SWQgPSBjdXJyZW50Py5pZCA/PyBudWxsO1xyXG4gICAgICAgIGNvbnN0IGNoYW5nZWQgPSBwcmV2SWQgIT09IG5leHRJZDtcclxuICAgICAgICBsYXN0VXNlcklkUmVmLmN1cnJlbnQgPSBuZXh0SWQ7XHJcbiAgICAgICAgaWYgKGNoYW5nZWQgJiYgdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAgIGNvbnN0IGV2dE5hbWUgPSBjdXJyZW50ID8gJ3Rlbm86YXV0aDpsb2dpbicgOiAndGVubzphdXRoOmxvZ291dCc7XHJcbiAgICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ3Rlbm86YXV0aDpjaGFuZ2UnLCB7IGRldGFpbDogeyB1c2VyOiBjdXJyZW50IH0gfSkpO1xyXG4gICAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KGV2dE5hbWUsIHsgZGV0YWlsOiB7IHVzZXI6IGN1cnJlbnQgfSB9KSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIHt9XHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHVzZXInKTtcclxuICAgICAgc2V0VXNlcihudWxsKTtcclxuICAgICAgdHJ5IHsgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oc3RvcmFnZUtleSk7IH0gY2F0Y2gge31cclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBsb2dpbldpdGhHb29nbGUgPSB1c2VDYWxsYmFjaygobmV4dFBhdGg/OiBzdHJpbmcpID0+IHtcclxuICAgIHJlZGlyZWN0VG9Hb29nbGVBdXRoKG5leHRQYXRoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIGNvbnN0IGxvZ291dCA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIC8vIE9wdGltaXN0aWNhbGx5IGNsZWFyIGxvY2FsIHN0YXRlIGZvciBzbmFwcGllciBVWFxyXG4gICAgc2V0VXNlcihudWxsKTtcclxuICAgIHRyeSB7IGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKHN0b3JhZ2VLZXkpOyB9IGNhdGNoIHt9XHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCBwZXJmb3JtTG9nb3V0KCk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICAvLyBBbHdheXMgcmVmcmVzaCBhZnRlciBsb2dvdXQgdG8gZW5zdXJlIGJhY2tlbmQvc2Vzc2lvbiBpcyBpbiBzeW5jXHJcbiAgICAgIGF3YWl0IHJlZnJlc2goKTtcclxuICAgIH1cclxuICB9LCBbcmVmcmVzaF0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gT3B0aW1pc3RpY2FsbHkgaHlkcmF0ZSBmcm9tIGxvY2FsU3RvcmFnZSBmb3IgZmFzdCBmaXJzdCBwYWludFxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmF3ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oc3RvcmFnZUtleSk7XHJcbiAgICAgIGlmIChyYXcpIHtcclxuICAgICAgICBjb25zdCBjYWNoZWQgPSBKU09OLnBhcnNlKHJhdykgYXMgQXV0aFVzZXI7XHJcbiAgICAgICAgc2V0VXNlcihjYWNoZWQpO1xyXG4gICAgICAgIGxhc3RVc2VySWRSZWYuY3VycmVudCA9IGNhY2hlZD8uaWQgPz8gbnVsbDtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCB7fVxyXG4gICAgLy8gT24gaW5pdGlhbCBtb3VudCwgdHJ5IHRvIGxvYWQgdGhlIGN1cnJlbnQgdXNlciBmcm9tIGJhY2tlbmRcclxuICAgIHJlZnJlc2goKTtcclxuICAgIC8vIExpc3RlbiBmb3IgZ2xvYmFsIHVuYXV0aG9yaXplZCBzaWduYWxzIHRvIGltbWVkaWF0ZWx5IGRyb3AgdXNlciBzdGF0ZVxyXG4gICAgY29uc3Qgb25VbmF1dGhvcml6ZWQgPSAoKSA9PiB7XHJcbiAgICAgIGNsZWFyQ2xpZW50QXV0aEFydGlmYWN0cygpO1xyXG4gICAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgICBsYXN0VXNlcklkUmVmLmN1cnJlbnQgPSBudWxsO1xyXG4gICAgfTtcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigndGVubzphdXRoOnVuYXV0aG9yaXplZCcsIG9uVW5hdXRob3JpemVkIGFzIEV2ZW50TGlzdGVuZXIpO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Rlbm86YXV0aDp1bmF1dGhvcml6ZWQnLCBvblVuYXV0aG9yaXplZCBhcyBFdmVudExpc3RlbmVyKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICB9LCBbcmVmcmVzaF0pO1xyXG5cclxuICBjb25zdCB2YWx1ZSA9IHVzZU1lbW88QXV0aENvbnRleHRWYWx1ZT4oKCkgPT4gKHtcclxuICAgIHVzZXIsXHJcbiAgICBpc0xvYWRpbmcsXHJcbiAgICBlcnJvcixcclxuICAgIHJlZnJlc2gsXHJcbiAgICBsb2dpbldpdGhHb29nbGUsXHJcbiAgICBsb2dvdXQsXHJcbiAgfSksIFt1c2VyLCBpc0xvYWRpbmcsIGVycm9yLCByZWZyZXNoLCBsb2dpbldpdGhHb29nbGUsIGxvZ291dF0pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvQXV0aENvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHVzZUF1dGgoKTogQXV0aENvbnRleHRWYWx1ZSB7XHJcbiAgY29uc3QgY3R4ID0gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7XHJcbiAgaWYgKCFjdHgpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xyXG4gIH1cclxuICByZXR1cm4gY3R4O1xyXG59XHJcblxyXG5cclxuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDYWxsYmFjayIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VNZW1vIiwidXNlUmVmIiwidXNlU3RhdGUiLCJnZXRDdXJyZW50VXNlciIsInBlcmZvcm1Mb2dvdXQiLCJyZWRpcmVjdFRvR29vZ2xlQXV0aCIsImNsZWFyQ2xpZW50QXV0aEFydGlmYWN0cyIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ1c2VyIiwic2V0VXNlciIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJzdG9yYWdlS2V5IiwibGFzdFVzZXJJZFJlZiIsInJlZnJlc2giLCJjdXJyZW50IiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsIkpTT04iLCJzdHJpbmdpZnkiLCJyZW1vdmVJdGVtIiwicHJldklkIiwibmV4dElkIiwiaWQiLCJjaGFuZ2VkIiwiZXZ0TmFtZSIsIndpbmRvdyIsImRpc3BhdGNoRXZlbnQiLCJDdXN0b21FdmVudCIsImRldGFpbCIsImVyciIsImxvZ2luV2l0aEdvb2dsZSIsIm5leHRQYXRoIiwibG9nb3V0IiwicmF3IiwiZ2V0SXRlbSIsImNhY2hlZCIsInBhcnNlIiwib25VbmF1dGhvcml6ZWQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VBdXRoIiwiY3R4IiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/context/AuthContext.tsx\n"));

/***/ }),

/***/ "./src/context/PreferencesContext.tsx":
/*!********************************************!*\
  !*** ./src/context/PreferencesContext.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreferencesProvider: function() { return /* binding */ PreferencesProvider; },\n/* harmony export */   usePreferences: function() { return /* binding */ usePreferences; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _utils_preferences__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/preferences */ \"./src/utils/preferences.ts\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst PreferencesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction getBrowserDefaults() {\n    const { language, country, currency } = (0,_utils_preferences__WEBPACK_IMPORTED_MODULE_3__.getBrowserPreferenceDefaults)();\n    return {\n        language,\n        country,\n        currency\n    };\n}\nfunction PreferencesProvider(param) {\n    let { children } = param;\n    _s();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const defaults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getBrowserDefaults(), []);\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.language);\n    const [currency, setCurrencyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.currency);\n    const [country, setCountryState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.country);\n    const storageKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const userId = (user === null || user === void 0 ? void 0 : user.id) ? String(user.id) : \"anon\";\n        const key = \"teno:prefs:\".concat(userId);\n        console.debug(\"[Prefs] storageKey computed\", {\n            userId,\n            key\n        });\n        return key;\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Hydrate from localStorage on mount or when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const raw = localStorage.getItem(storageKey);\n            console.debug(\"[Prefs] hydrate start\", {\n                storageKey,\n                raw\n            });\n            if (raw) {\n                const parsed = JSON.parse(raw);\n                const nextLanguage = parsed.language || defaults.language;\n                const nextCurrency = parsed.currency || defaults.currency;\n                const nextCountry = parsed.country || defaults.country;\n                console.debug(\"[Prefs] hydrate parsed\", {\n                    parsed,\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n            } else {\n                console.debug(\"[Prefs] hydrate no existing, using defaults\", defaults);\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        } catch (e) {\n            console.debug(\"[Prefs] hydrate error, falling back to defaults\", defaults);\n            setLanguageState(defaults.language);\n            setCurrencyState(defaults.currency);\n            setCountryState(defaults.country);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        storageKey\n    ]);\n    // Re-hydrate on auth changes (login/logout) since key scope changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const onAuthChange = ()=>{\n            try {\n                const raw = localStorage.getItem(storageKey);\n                if (raw) {\n                    const parsed = JSON.parse(raw);\n                    setLanguageState(parsed.language || defaults.language);\n                    setCurrencyState(parsed.currency || defaults.currency);\n                    setCountryState(parsed.country || defaults.country);\n                } else {\n                    setLanguageState(defaults.language);\n                    setCurrencyState(defaults.currency);\n                    setCountryState(defaults.country);\n                }\n            } catch (e) {\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        };\n        if (true) {\n            window.addEventListener(\"teno:auth:change\", onAuthChange);\n        }\n        return ()=>{\n            if (true) {\n                window.removeEventListener(\"teno:auth:change\", onAuthChange);\n            }\n        };\n    }, [\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    // If a user is present, fetch server-side preferences and apply\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userId = (user === null || user === void 0 ? void 0 : user.id) ? String(user.id) : null;\n        if (!userId) return;\n        let aborted = false;\n        (async ()=>{\n            try {\n                const url = \"\".concat((0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.getBackendUrl)(), \"/api/users/\").concat(encodeURIComponent(userId));\n                console.debug(\"[Prefs] fetching server preferences\", {\n                    userId,\n                    url\n                });\n                const resp = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.fetchWithCredentials)(url);\n                if (!resp.ok) return;\n                const payload = await resp.json();\n                if (aborted || !payload) return;\n                const nextLanguage = payload.preferredLanguage || defaults.language;\n                const nextCurrency = payload.preferredCurrency || defaults.currency;\n                const nextCountry = payload.countryCode || defaults.country;\n                console.debug(\"[Prefs] server preferences received\", {\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n                try {\n                    localStorage.setItem(storageKey, JSON.stringify({\n                        language: nextLanguage,\n                        currency: nextCurrency,\n                        country: nextCountry\n                    }));\n                } catch (e) {}\n            } catch (e) {}\n        })();\n        return ()=>{\n            aborted = true;\n        };\n    }, [\n        user === null || user === void 0 ? void 0 : user.id,\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    const persist = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((next)=>{\n        try {\n            const current = {\n                language,\n                currency,\n                country\n            };\n            const merged = {\n                ...current,\n                ...next\n            };\n            console.debug(\"[Prefs] persist\", {\n                storageKey,\n                current,\n                next,\n                merged\n            });\n            localStorage.setItem(storageKey, JSON.stringify(merged));\n        } catch (e) {}\n    }, [\n        language,\n        currency,\n        country,\n        storageKey\n    ]);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lng)=>{\n        setLanguageState(lng);\n        persist({\n            language: lng\n        });\n    }, [\n        persist\n    ]);\n    const setCurrency = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cur)=>{\n        console.debug(\"[Prefs] setCurrency\", {\n            from: currency,\n            to: cur\n        });\n        setCurrencyState(cur);\n        persist({\n            currency: cur\n        });\n    }, [\n        persist,\n        currency\n    ]);\n    const setCountry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cc)=>{\n        setCountryState(cc);\n        persist({\n            country: cc\n        });\n    }, [\n        persist\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            language,\n            currency,\n            country,\n            setLanguage,\n            setCurrency,\n            setCountry\n        }), [\n        language,\n        currency,\n        country,\n        setLanguage,\n        setCurrency,\n        setCountry\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreferencesContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\PreferencesContext.tsx\",\n        lineNumber: 169,\n        columnNumber: 3\n    }, this);\n}\n_s(PreferencesProvider, \"FxcX8wsLUqDDd1Kr0oVnren+WS4=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = PreferencesProvider;\nfunction usePreferences() {\n    _s1();\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PreferencesContext);\n    if (!ctx) {\n        throw new Error(\"usePreferences must be used within a PreferencesProvider\");\n    }\n    return ctx;\n}\n_s1(usePreferences, \"/dMy7t63NXD4eYACoT93CePwGrg=\");\nvar _c;\n$RefreshReg$(_c, \"PreferencesProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29udGV4dC9QcmVmZXJlbmNlc0NvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQTZGO0FBQ3JEO0FBQytFO0FBQ25EO0FBV3BFLE1BQU1VLG1DQUFxQlYsb0RBQWFBLENBQXNDVztBQVE5RSxTQUFTQztJQUNSLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxPQUFPLEVBQUVDLFFBQVEsRUFBRSxHQUFHUixnRkFBNEJBO0lBQ3BFLE9BQU87UUFBRU07UUFBVUM7UUFBU0M7SUFBUztBQUN0QztBQUVPLFNBQVNDLG9CQUFvQixLQUEyQztRQUEzQyxFQUFFQyxRQUFRLEVBQWlDLEdBQTNDOztJQUNuQyxNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHWixxREFBT0E7SUFDeEIsTUFBTWEsV0FBV2YsOENBQU9BLENBQUMsSUFBTVEsc0JBQXNCLEVBQUU7SUFDdkQsTUFBTSxDQUFDQyxVQUFVTyxpQkFBaUIsR0FBR2YsK0NBQVFBLENBQW9CYyxTQUFTTixRQUFRO0lBQ2xGLE1BQU0sQ0FBQ0UsVUFBVU0saUJBQWlCLEdBQUdoQiwrQ0FBUUEsQ0FBb0JjLFNBQVNKLFFBQVE7SUFDbEYsTUFBTSxDQUFDRCxTQUFTUSxnQkFBZ0IsR0FBR2pCLCtDQUFRQSxDQUFjYyxTQUFTTCxPQUFPO0lBRXpFLE1BQU1TLGFBQWFuQiw4Q0FBT0EsQ0FBQztRQUMxQixNQUFNb0IsU0FBU04sQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNTyxFQUFFLElBQUdDLE9BQU9SLEtBQUtPLEVBQUUsSUFBSTtRQUM1QyxNQUFNRSxNQUFNLGNBQXFCLE9BQVBIO1FBQzFCSSxRQUFRQyxLQUFLLENBQUMsK0JBQStCO1lBQUVMO1lBQVFHO1FBQUk7UUFDM0QsT0FBT0E7SUFDUixHQUFHO1FBQUNULGlCQUFBQSwyQkFBQUEsS0FBTU8sRUFBRTtLQUFDO0lBRWIsMERBQTBEO0lBQzFEdEIsZ0RBQVNBLENBQUM7UUFDVCxJQUFJO1lBQ0gsTUFBTTJCLE1BQU1DLGFBQWFDLE9BQU8sQ0FBQ1Q7WUFDakNLLFFBQVFDLEtBQUssQ0FBQyx5QkFBeUI7Z0JBQUVOO2dCQUFZTztZQUFJO1lBQ3pELElBQUlBLEtBQUs7Z0JBQ1IsTUFBTUcsU0FBU0MsS0FBS0MsS0FBSyxDQUFDTDtnQkFDMUIsTUFBTU0sZUFBZUgsT0FBT3BCLFFBQVEsSUFBSU0sU0FBU04sUUFBUTtnQkFDekQsTUFBTXdCLGVBQWVKLE9BQU9sQixRQUFRLElBQUlJLFNBQVNKLFFBQVE7Z0JBQ3pELE1BQU11QixjQUFjTCxPQUFPbkIsT0FBTyxJQUFJSyxTQUFTTCxPQUFPO2dCQUN0RGMsUUFBUUMsS0FBSyxDQUFDLDBCQUEwQjtvQkFBRUk7b0JBQVFHO29CQUFjQztvQkFBY0M7Z0JBQVk7Z0JBQzFGbEIsaUJBQWlCZ0I7Z0JBQ2pCZixpQkFBaUJnQjtnQkFDakJmLGdCQUFnQmdCO1lBQ2pCLE9BQU87Z0JBQ05WLFFBQVFDLEtBQUssQ0FBQywrQ0FBK0NWO2dCQUM3REMsaUJBQWlCRCxTQUFTTixRQUFRO2dCQUNsQ1EsaUJBQWlCRixTQUFTSixRQUFRO2dCQUNsQ08sZ0JBQWdCSCxTQUFTTCxPQUFPO1lBQ2pDO1FBQ0QsRUFBRSxVQUFNO1lBQ1BjLFFBQVFDLEtBQUssQ0FBQyxtREFBbURWO1lBQ2pFQyxpQkFBaUJELFNBQVNOLFFBQVE7WUFDbENRLGlCQUFpQkYsU0FBU0osUUFBUTtZQUNsQ08sZ0JBQWdCSCxTQUFTTCxPQUFPO1FBQ2pDO0lBQ0EsdURBQXVEO0lBQ3hELEdBQUc7UUFBQ1M7S0FBVztJQUVmLG9FQUFvRTtJQUNwRXBCLGdEQUFTQSxDQUFDO1FBQ1QsTUFBTW9DLGVBQWU7WUFDcEIsSUFBSTtnQkFDSCxNQUFNVCxNQUFNQyxhQUFhQyxPQUFPLENBQUNUO2dCQUNqQyxJQUFJTyxLQUFLO29CQUNSLE1BQU1HLFNBQVNDLEtBQUtDLEtBQUssQ0FBQ0w7b0JBQzFCVixpQkFBaUJhLE9BQU9wQixRQUFRLElBQUlNLFNBQVNOLFFBQVE7b0JBQ3JEUSxpQkFBaUJZLE9BQU9sQixRQUFRLElBQUlJLFNBQVNKLFFBQVE7b0JBQ3JETyxnQkFBZ0JXLE9BQU9uQixPQUFPLElBQUlLLFNBQVNMLE9BQU87Z0JBQ25ELE9BQU87b0JBQ05NLGlCQUFpQkQsU0FBU04sUUFBUTtvQkFDbENRLGlCQUFpQkYsU0FBU0osUUFBUTtvQkFDbENPLGdCQUFnQkgsU0FBU0wsT0FBTztnQkFDakM7WUFDRCxFQUFFLFVBQU07Z0JBQ1BNLGlCQUFpQkQsU0FBU04sUUFBUTtnQkFDbENRLGlCQUFpQkYsU0FBU0osUUFBUTtnQkFDbENPLGdCQUFnQkgsU0FBU0wsT0FBTztZQUNqQztRQUNEO1FBQ0EsSUFBSSxJQUFrQixFQUFhO1lBQ2xDMEIsT0FBT0MsZ0JBQWdCLENBQUMsb0JBQW9CRjtRQUM3QztRQUNBLE9BQU87WUFDTixJQUFJLElBQWtCLEVBQWE7Z0JBQ2xDQyxPQUFPRSxtQkFBbUIsQ0FBQyxvQkFBb0JIO1lBQ2hEO1FBQ0Q7SUFDRCxHQUFHO1FBQUNoQjtRQUFZSixTQUFTTixRQUFRO1FBQUVNLFNBQVNKLFFBQVE7UUFBRUksU0FBU0wsT0FBTztLQUFDO0lBRXZFLGdFQUFnRTtJQUNoRVgsZ0RBQVNBLENBQUM7UUFDVCxNQUFNcUIsU0FBU04sQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNTyxFQUFFLElBQUdDLE9BQU9SLEtBQUtPLEVBQUUsSUFBSTtRQUM1QyxJQUFJLENBQUNELFFBQVE7UUFDYixJQUFJbUIsVUFBVTtRQUNiO1lBQ0EsSUFBSTtnQkFDSCxNQUFNQyxNQUFNLEdBQWdDQyxPQUE3QnBDLDBEQUFhQSxJQUFHLGVBQXdDLE9BQTNCb0MsbUJBQW1CckI7Z0JBQy9ESSxRQUFRQyxLQUFLLENBQUMsdUNBQXVDO29CQUFFTDtvQkFBUW9CO2dCQUFJO2dCQUNuRSxNQUFNRSxPQUFPLE1BQU10QyxpRUFBb0JBLENBQUNvQztnQkFDeEMsSUFBSSxDQUFDRSxLQUFLQyxFQUFFLEVBQUU7Z0JBQ2QsTUFBTUMsVUFBVSxNQUFNRixLQUFLRyxJQUFJO2dCQUMvQixJQUFJTixXQUFXLENBQUNLLFNBQVM7Z0JBQ3pCLE1BQU1aLGVBQWUsUUFBU2MsaUJBQWlCLElBQTBCL0IsU0FBU04sUUFBUTtnQkFDMUYsTUFBTXdCLGVBQWUsUUFBU2MsaUJBQWlCLElBQTBCaEMsU0FBU0osUUFBUTtnQkFDMUYsTUFBTXVCLGNBQWMsUUFBU2MsV0FBVyxJQUFvQmpDLFNBQVNMLE9BQU87Z0JBQzVFYyxRQUFRQyxLQUFLLENBQUMsdUNBQXVDO29CQUFFTztvQkFBY0M7b0JBQWNDO2dCQUFZO2dCQUMvRmxCLGlCQUFpQmdCO2dCQUNqQmYsaUJBQWlCZ0I7Z0JBQ2pCZixnQkFBZ0JnQjtnQkFDaEIsSUFBSTtvQkFDSFAsYUFBYXNCLE9BQU8sQ0FBQzlCLFlBQVlXLEtBQUtvQixTQUFTLENBQUM7d0JBQUV6QyxVQUFVdUI7d0JBQWNyQixVQUFVc0I7d0JBQWN2QixTQUFTd0I7b0JBQVk7Z0JBQ3hILEVBQUUsVUFBTSxDQUFDO1lBQ1YsRUFBRSxVQUFNLENBQUM7UUFDVjtRQUNBLE9BQU87WUFBUUssVUFBVTtRQUFNO0lBQ2hDLEdBQUc7UUFBQ3pCLGlCQUFBQSwyQkFBQUEsS0FBTU8sRUFBRTtRQUFFRjtRQUFZSixTQUFTTixRQUFRO1FBQUVNLFNBQVNKLFFBQVE7UUFBRUksU0FBU0wsT0FBTztLQUFDO0lBRWpGLE1BQU15QyxVQUFVdEQsa0RBQVdBLENBQUMsQ0FBQ3VEO1FBQzVCLElBQUk7WUFDSCxNQUFNQyxVQUEwQjtnQkFDL0I1QztnQkFDQUU7Z0JBQ0FEO1lBQ0Q7WUFDQSxNQUFNNEMsU0FBUztnQkFBRSxHQUFHRCxPQUFPO2dCQUFFLEdBQUdELElBQUk7WUFBQztZQUNyQzVCLFFBQVFDLEtBQUssQ0FBQyxtQkFBbUI7Z0JBQUVOO2dCQUFZa0M7Z0JBQVNEO2dCQUFNRTtZQUFPO1lBQ3JFM0IsYUFBYXNCLE9BQU8sQ0FBQzlCLFlBQVlXLEtBQUtvQixTQUFTLENBQUNJO1FBQ2pELEVBQUUsVUFBTSxDQUFDO0lBQ1YsR0FBRztRQUFDN0M7UUFBVUU7UUFBVUQ7UUFBU1M7S0FBVztJQUU1QyxNQUFNb0MsY0FBYzFELGtEQUFXQSxDQUFDLENBQUMyRDtRQUNoQ3hDLGlCQUFpQndDO1FBQ2pCTCxRQUFRO1lBQUUxQyxVQUFVK0M7UUFBSTtJQUN6QixHQUFHO1FBQUNMO0tBQVE7SUFFWixNQUFNTSxjQUFjNUQsa0RBQVdBLENBQUMsQ0FBQzZEO1FBQ2hDbEMsUUFBUUMsS0FBSyxDQUFDLHVCQUF1QjtZQUFFa0MsTUFBTWhEO1lBQVVpRCxJQUFJRjtRQUFJO1FBQy9EekMsaUJBQWlCeUM7UUFDakJQLFFBQVE7WUFBRXhDLFVBQVUrQztRQUFJO0lBQ3pCLEdBQUc7UUFBQ1A7UUFBU3hDO0tBQVM7SUFFdEIsTUFBTWtELGFBQWFoRSxrREFBV0EsQ0FBQyxDQUFDaUU7UUFDL0I1QyxnQkFBZ0I0QztRQUNoQlgsUUFBUTtZQUFFekMsU0FBU29EO1FBQUc7SUFDdkIsR0FBRztRQUFDWDtLQUFRO0lBRVosTUFBTVksUUFBUS9ELDhDQUFPQSxDQUEwQixJQUFPO1lBQ3JEUztZQUNBRTtZQUNBRDtZQUNBNkM7WUFDQUU7WUFDQUk7UUFDRCxJQUFJO1FBQUNwRDtRQUFVRTtRQUFVRDtRQUFTNkM7UUFBYUU7UUFBYUk7S0FBVztJQUV2RSxxQkFDQyw4REFBQ3ZELG1CQUFtQjBELFFBQVE7UUFBQ0QsT0FBT0E7a0JBQ2xDbEQ7Ozs7OztBQUdKO0dBakpnQkQ7O1FBQ0VWLGlEQUFPQTs7O0tBRFRVO0FBbUpULFNBQVNxRDs7SUFDZixNQUFNQyxNQUFNcEUsaURBQVVBLENBQUNRO0lBQ3ZCLElBQUksQ0FBQzRELEtBQUs7UUFDVCxNQUFNLElBQUlDLE1BQU07SUFDakI7SUFDQSxPQUFPRDtBQUNSO0lBTmdCRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29udGV4dC9QcmVmZXJlbmNlc0NvbnRleHQudHN4P2VlZTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ2FsbGJhY2ssIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlTWVtbywgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICcuL0F1dGhDb250ZXh0JztcclxuaW1wb3J0IHsgQ291bnRyeUNvZGUsIFN1cHBvcnRlZEN1cnJlbmN5LCBTdXBwb3J0ZWRMYW5ndWFnZSwgZ2V0QnJvd3NlclByZWZlcmVuY2VEZWZhdWx0cyB9IGZyb20gJy4uL3V0aWxzL3ByZWZlcmVuY2VzJztcclxuaW1wb3J0IHsgZmV0Y2hXaXRoQ3JlZGVudGlhbHMsIGdldEJhY2tlbmRVcmwgfSBmcm9tICcuLi91dGlscy9hdXRoJztcclxuXHJcbmludGVyZmFjZSBQcmVmZXJlbmNlc0NvbnRleHRWYWx1ZSB7XHJcblx0bGFuZ3VhZ2U6IFN1cHBvcnRlZExhbmd1YWdlO1xyXG5cdGN1cnJlbmN5OiBTdXBwb3J0ZWRDdXJyZW5jeTtcclxuXHRjb3VudHJ5OiBDb3VudHJ5Q29kZTtcclxuXHRzZXRMYW5ndWFnZTogKGxhbmd1YWdlOiBTdXBwb3J0ZWRMYW5ndWFnZSkgPT4gdm9pZDtcclxuXHRzZXRDdXJyZW5jeTogKGN1cnJlbmN5OiBTdXBwb3J0ZWRDdXJyZW5jeSkgPT4gdm9pZDtcclxuXHRzZXRDb3VudHJ5OiAoY291bnRyeTogQ291bnRyeUNvZGUpID0+IHZvaWQ7XHJcbn1cclxuXHJcbmNvbnN0IFByZWZlcmVuY2VzQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8UHJlZmVyZW5jZXNDb250ZXh0VmFsdWUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XHJcblxyXG50eXBlIFBlcnNpc3RlZFByZWZzID0ge1xyXG5cdGxhbmd1YWdlPzogU3VwcG9ydGVkTGFuZ3VhZ2U7XHJcblx0Y3VycmVuY3k/OiBTdXBwb3J0ZWRDdXJyZW5jeTtcclxuXHRjb3VudHJ5PzogQ291bnRyeUNvZGU7XHJcbn07XHJcblxyXG5mdW5jdGlvbiBnZXRCcm93c2VyRGVmYXVsdHMoKTogUmVxdWlyZWQ8UGVyc2lzdGVkUHJlZnM+IHtcclxuXHRjb25zdCB7IGxhbmd1YWdlLCBjb3VudHJ5LCBjdXJyZW5jeSB9ID0gZ2V0QnJvd3NlclByZWZlcmVuY2VEZWZhdWx0cygpO1xyXG5cdHJldHVybiB7IGxhbmd1YWdlLCBjb3VudHJ5LCBjdXJyZW5jeSB9O1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUHJlZmVyZW5jZXNQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XHJcblx0Y29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XHJcblx0Y29uc3QgZGVmYXVsdHMgPSB1c2VNZW1vKCgpID0+IGdldEJyb3dzZXJEZWZhdWx0cygpLCBbXSk7XHJcblx0Y29uc3QgW2xhbmd1YWdlLCBzZXRMYW5ndWFnZVN0YXRlXSA9IHVzZVN0YXRlPFN1cHBvcnRlZExhbmd1YWdlPihkZWZhdWx0cy5sYW5ndWFnZSk7XHJcblx0Y29uc3QgW2N1cnJlbmN5LCBzZXRDdXJyZW5jeVN0YXRlXSA9IHVzZVN0YXRlPFN1cHBvcnRlZEN1cnJlbmN5PihkZWZhdWx0cy5jdXJyZW5jeSk7XHJcblx0Y29uc3QgW2NvdW50cnksIHNldENvdW50cnlTdGF0ZV0gPSB1c2VTdGF0ZTxDb3VudHJ5Q29kZT4oZGVmYXVsdHMuY291bnRyeSk7XHJcblxyXG5cdGNvbnN0IHN0b3JhZ2VLZXkgPSB1c2VNZW1vKCgpID0+IHtcclxuXHRcdGNvbnN0IHVzZXJJZCA9IHVzZXI/LmlkID8gU3RyaW5nKHVzZXIuaWQpIDogJ2Fub24nO1xyXG5cdFx0Y29uc3Qga2V5ID0gYHRlbm86cHJlZnM6JHt1c2VySWR9YDtcclxuXHRcdGNvbnNvbGUuZGVidWcoJ1tQcmVmc10gc3RvcmFnZUtleSBjb21wdXRlZCcsIHsgdXNlcklkLCBrZXkgfSk7XHJcblx0XHRyZXR1cm4ga2V5O1xyXG5cdH0sIFt1c2VyPy5pZF0pO1xyXG5cclxuXHQvLyBIeWRyYXRlIGZyb20gbG9jYWxTdG9yYWdlIG9uIG1vdW50IG9yIHdoZW4gdXNlciBjaGFuZ2VzXHJcblx0dXNlRWZmZWN0KCgpID0+IHtcclxuXHRcdHRyeSB7XHJcblx0XHRcdGNvbnN0IHJhdyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHN0b3JhZ2VLZXkpO1xyXG5cdFx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIGh5ZHJhdGUgc3RhcnQnLCB7IHN0b3JhZ2VLZXksIHJhdyB9KTtcclxuXHRcdFx0aWYgKHJhdykge1xyXG5cdFx0XHRcdGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UocmF3KSBhcyBQZXJzaXN0ZWRQcmVmcztcclxuXHRcdFx0XHRjb25zdCBuZXh0TGFuZ3VhZ2UgPSBwYXJzZWQubGFuZ3VhZ2UgfHwgZGVmYXVsdHMubGFuZ3VhZ2U7XHJcblx0XHRcdFx0Y29uc3QgbmV4dEN1cnJlbmN5ID0gcGFyc2VkLmN1cnJlbmN5IHx8IGRlZmF1bHRzLmN1cnJlbmN5O1xyXG5cdFx0XHRcdGNvbnN0IG5leHRDb3VudHJ5ID0gcGFyc2VkLmNvdW50cnkgfHwgZGVmYXVsdHMuY291bnRyeTtcclxuXHRcdFx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIGh5ZHJhdGUgcGFyc2VkJywgeyBwYXJzZWQsIG5leHRMYW5ndWFnZSwgbmV4dEN1cnJlbmN5LCBuZXh0Q291bnRyeSB9KTtcclxuXHRcdFx0XHRzZXRMYW5ndWFnZVN0YXRlKG5leHRMYW5ndWFnZSk7XHJcblx0XHRcdFx0c2V0Q3VycmVuY3lTdGF0ZShuZXh0Q3VycmVuY3kpO1xyXG5cdFx0XHRcdHNldENvdW50cnlTdGF0ZShuZXh0Q291bnRyeSk7XHJcblx0XHRcdH0gZWxzZSB7XHJcblx0XHRcdFx0Y29uc29sZS5kZWJ1ZygnW1ByZWZzXSBoeWRyYXRlIG5vIGV4aXN0aW5nLCB1c2luZyBkZWZhdWx0cycsIGRlZmF1bHRzKTtcclxuXHRcdFx0XHRzZXRMYW5ndWFnZVN0YXRlKGRlZmF1bHRzLmxhbmd1YWdlKTtcclxuXHRcdFx0XHRzZXRDdXJyZW5jeVN0YXRlKGRlZmF1bHRzLmN1cnJlbmN5KTtcclxuXHRcdFx0XHRzZXRDb3VudHJ5U3RhdGUoZGVmYXVsdHMuY291bnRyeSk7XHJcblx0XHRcdH1cclxuXHRcdH0gY2F0Y2gge1xyXG5cdFx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIGh5ZHJhdGUgZXJyb3IsIGZhbGxpbmcgYmFjayB0byBkZWZhdWx0cycsIGRlZmF1bHRzKTtcclxuXHRcdFx0c2V0TGFuZ3VhZ2VTdGF0ZShkZWZhdWx0cy5sYW5ndWFnZSk7XHJcblx0XHRcdHNldEN1cnJlbmN5U3RhdGUoZGVmYXVsdHMuY3VycmVuY3kpO1xyXG5cdFx0XHRzZXRDb3VudHJ5U3RhdGUoZGVmYXVsdHMuY291bnRyeSk7XHJcblx0XHR9XHJcblx0XHQvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXHJcblx0fSwgW3N0b3JhZ2VLZXldKTtcclxuXHJcblx0Ly8gUmUtaHlkcmF0ZSBvbiBhdXRoIGNoYW5nZXMgKGxvZ2luL2xvZ291dCkgc2luY2Uga2V5IHNjb3BlIGNoYW5nZXNcclxuXHR1c2VFZmZlY3QoKCkgPT4ge1xyXG5cdFx0Y29uc3Qgb25BdXRoQ2hhbmdlID0gKCkgPT4ge1xyXG5cdFx0XHR0cnkge1xyXG5cdFx0XHRcdGNvbnN0IHJhdyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHN0b3JhZ2VLZXkpO1xyXG5cdFx0XHRcdGlmIChyYXcpIHtcclxuXHRcdFx0XHRcdGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UocmF3KSBhcyBQZXJzaXN0ZWRQcmVmcztcclxuXHRcdFx0XHRcdHNldExhbmd1YWdlU3RhdGUocGFyc2VkLmxhbmd1YWdlIHx8IGRlZmF1bHRzLmxhbmd1YWdlKTtcclxuXHRcdFx0XHRcdHNldEN1cnJlbmN5U3RhdGUocGFyc2VkLmN1cnJlbmN5IHx8IGRlZmF1bHRzLmN1cnJlbmN5KTtcclxuXHRcdFx0XHRcdHNldENvdW50cnlTdGF0ZShwYXJzZWQuY291bnRyeSB8fCBkZWZhdWx0cy5jb3VudHJ5KTtcclxuXHRcdFx0XHR9IGVsc2Uge1xyXG5cdFx0XHRcdFx0c2V0TGFuZ3VhZ2VTdGF0ZShkZWZhdWx0cy5sYW5ndWFnZSk7XHJcblx0XHRcdFx0XHRzZXRDdXJyZW5jeVN0YXRlKGRlZmF1bHRzLmN1cnJlbmN5KTtcclxuXHRcdFx0XHRcdHNldENvdW50cnlTdGF0ZShkZWZhdWx0cy5jb3VudHJ5KTtcclxuXHRcdFx0XHR9XHJcblx0XHRcdH0gY2F0Y2gge1xyXG5cdFx0XHRcdHNldExhbmd1YWdlU3RhdGUoZGVmYXVsdHMubGFuZ3VhZ2UpO1xyXG5cdFx0XHRcdHNldEN1cnJlbmN5U3RhdGUoZGVmYXVsdHMuY3VycmVuY3kpO1xyXG5cdFx0XHRcdHNldENvdW50cnlTdGF0ZShkZWZhdWx0cy5jb3VudHJ5KTtcclxuXHRcdFx0fVxyXG5cdFx0fTtcclxuXHRcdGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG5cdFx0XHR3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigndGVubzphdXRoOmNoYW5nZScsIG9uQXV0aENoYW5nZSBhcyBFdmVudExpc3RlbmVyKTtcclxuXHRcdH1cclxuXHRcdHJldHVybiAoKSA9PiB7XHJcblx0XHRcdGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG5cdFx0XHRcdHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCd0ZW5vOmF1dGg6Y2hhbmdlJywgb25BdXRoQ2hhbmdlIGFzIEV2ZW50TGlzdGVuZXIpO1xyXG5cdFx0XHR9XHJcblx0XHR9O1xyXG5cdH0sIFtzdG9yYWdlS2V5LCBkZWZhdWx0cy5sYW5ndWFnZSwgZGVmYXVsdHMuY3VycmVuY3ksIGRlZmF1bHRzLmNvdW50cnldKTtcclxuXHJcblx0Ly8gSWYgYSB1c2VyIGlzIHByZXNlbnQsIGZldGNoIHNlcnZlci1zaWRlIHByZWZlcmVuY2VzIGFuZCBhcHBseVxyXG5cdHVzZUVmZmVjdCgoKSA9PiB7XHJcblx0XHRjb25zdCB1c2VySWQgPSB1c2VyPy5pZCA/IFN0cmluZyh1c2VyLmlkKSA6IG51bGw7XHJcblx0XHRpZiAoIXVzZXJJZCkgcmV0dXJuO1xyXG5cdFx0bGV0IGFib3J0ZWQgPSBmYWxzZTtcclxuXHRcdChhc3luYyAoKSA9PiB7XHJcblx0XHRcdHRyeSB7XHJcblx0XHRcdFx0Y29uc3QgdXJsID0gYCR7Z2V0QmFja2VuZFVybCgpfS9hcGkvdXNlcnMvJHtlbmNvZGVVUklDb21wb25lbnQodXNlcklkKX1gO1xyXG5cdFx0XHRcdGNvbnNvbGUuZGVidWcoJ1tQcmVmc10gZmV0Y2hpbmcgc2VydmVyIHByZWZlcmVuY2VzJywgeyB1c2VySWQsIHVybCB9KTtcclxuXHRcdFx0XHRjb25zdCByZXNwID0gYXdhaXQgZmV0Y2hXaXRoQ3JlZGVudGlhbHModXJsKTtcclxuXHRcdFx0XHRpZiAoIXJlc3Aub2spIHJldHVybjtcclxuXHRcdFx0XHRjb25zdCBwYXlsb2FkID0gYXdhaXQgcmVzcC5qc29uKCk7XHJcblx0XHRcdFx0aWYgKGFib3J0ZWQgfHwgIXBheWxvYWQpIHJldHVybjtcclxuXHRcdFx0XHRjb25zdCBuZXh0TGFuZ3VhZ2UgPSAocGF5bG9hZC5wcmVmZXJyZWRMYW5ndWFnZSBhcyBTdXBwb3J0ZWRMYW5ndWFnZSkgfHwgZGVmYXVsdHMubGFuZ3VhZ2U7XHJcblx0XHRcdFx0Y29uc3QgbmV4dEN1cnJlbmN5ID0gKHBheWxvYWQucHJlZmVycmVkQ3VycmVuY3kgYXMgU3VwcG9ydGVkQ3VycmVuY3kpIHx8IGRlZmF1bHRzLmN1cnJlbmN5O1xyXG5cdFx0XHRcdGNvbnN0IG5leHRDb3VudHJ5ID0gKHBheWxvYWQuY291bnRyeUNvZGUgYXMgQ291bnRyeUNvZGUpIHx8IGRlZmF1bHRzLmNvdW50cnk7XHJcblx0XHRcdFx0Y29uc29sZS5kZWJ1ZygnW1ByZWZzXSBzZXJ2ZXIgcHJlZmVyZW5jZXMgcmVjZWl2ZWQnLCB7IG5leHRMYW5ndWFnZSwgbmV4dEN1cnJlbmN5LCBuZXh0Q291bnRyeSB9KTtcclxuXHRcdFx0XHRzZXRMYW5ndWFnZVN0YXRlKG5leHRMYW5ndWFnZSk7XHJcblx0XHRcdFx0c2V0Q3VycmVuY3lTdGF0ZShuZXh0Q3VycmVuY3kpO1xyXG5cdFx0XHRcdHNldENvdW50cnlTdGF0ZShuZXh0Q291bnRyeSk7XHJcblx0XHRcdFx0dHJ5IHtcclxuXHRcdFx0XHRcdGxvY2FsU3RvcmFnZS5zZXRJdGVtKHN0b3JhZ2VLZXksIEpTT04uc3RyaW5naWZ5KHsgbGFuZ3VhZ2U6IG5leHRMYW5ndWFnZSwgY3VycmVuY3k6IG5leHRDdXJyZW5jeSwgY291bnRyeTogbmV4dENvdW50cnkgfSkpO1xyXG5cdFx0XHRcdH0gY2F0Y2gge31cclxuXHRcdFx0fSBjYXRjaCB7fVxyXG5cdFx0fSkoKTtcclxuXHRcdHJldHVybiAoKSA9PiB7IGFib3J0ZWQgPSB0cnVlOyB9O1xyXG5cdH0sIFt1c2VyPy5pZCwgc3RvcmFnZUtleSwgZGVmYXVsdHMubGFuZ3VhZ2UsIGRlZmF1bHRzLmN1cnJlbmN5LCBkZWZhdWx0cy5jb3VudHJ5XSk7XHJcblxyXG5cdGNvbnN0IHBlcnNpc3QgPSB1c2VDYWxsYmFjaygobmV4dDogUGVyc2lzdGVkUHJlZnMpID0+IHtcclxuXHRcdHRyeSB7XHJcblx0XHRcdGNvbnN0IGN1cnJlbnQ6IFBlcnNpc3RlZFByZWZzID0ge1xyXG5cdFx0XHRcdGxhbmd1YWdlLFxyXG5cdFx0XHRcdGN1cnJlbmN5LFxyXG5cdFx0XHRcdGNvdW50cnksXHJcblx0XHRcdH07XHJcblx0XHRcdGNvbnN0IG1lcmdlZCA9IHsgLi4uY3VycmVudCwgLi4ubmV4dCB9O1xyXG5cdFx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIHBlcnNpc3QnLCB7IHN0b3JhZ2VLZXksIGN1cnJlbnQsIG5leHQsIG1lcmdlZCB9KTtcclxuXHRcdFx0bG9jYWxTdG9yYWdlLnNldEl0ZW0oc3RvcmFnZUtleSwgSlNPTi5zdHJpbmdpZnkobWVyZ2VkKSk7XHJcblx0XHR9IGNhdGNoIHt9XHJcblx0fSwgW2xhbmd1YWdlLCBjdXJyZW5jeSwgY291bnRyeSwgc3RvcmFnZUtleV0pO1xyXG5cclxuXHRjb25zdCBzZXRMYW5ndWFnZSA9IHVzZUNhbGxiYWNrKChsbmc6IFN1cHBvcnRlZExhbmd1YWdlKSA9PiB7XHJcblx0XHRzZXRMYW5ndWFnZVN0YXRlKGxuZyk7XHJcblx0XHRwZXJzaXN0KHsgbGFuZ3VhZ2U6IGxuZyB9KTtcclxuXHR9LCBbcGVyc2lzdF0pO1xyXG5cclxuXHRjb25zdCBzZXRDdXJyZW5jeSA9IHVzZUNhbGxiYWNrKChjdXI6IFN1cHBvcnRlZEN1cnJlbmN5KSA9PiB7XHJcblx0XHRjb25zb2xlLmRlYnVnKCdbUHJlZnNdIHNldEN1cnJlbmN5JywgeyBmcm9tOiBjdXJyZW5jeSwgdG86IGN1ciB9KTtcclxuXHRcdHNldEN1cnJlbmN5U3RhdGUoY3VyKTtcclxuXHRcdHBlcnNpc3QoeyBjdXJyZW5jeTogY3VyIH0pO1xyXG5cdH0sIFtwZXJzaXN0LCBjdXJyZW5jeV0pO1xyXG5cclxuXHRjb25zdCBzZXRDb3VudHJ5ID0gdXNlQ2FsbGJhY2soKGNjOiBDb3VudHJ5Q29kZSkgPT4ge1xyXG5cdFx0c2V0Q291bnRyeVN0YXRlKGNjKTtcclxuXHRcdHBlcnNpc3QoeyBjb3VudHJ5OiBjYyB9KTtcclxuXHR9LCBbcGVyc2lzdF0pO1xyXG5cclxuXHRjb25zdCB2YWx1ZSA9IHVzZU1lbW88UHJlZmVyZW5jZXNDb250ZXh0VmFsdWU+KCgpID0+ICh7XHJcblx0XHRsYW5ndWFnZSxcclxuXHRcdGN1cnJlbmN5LFxyXG5cdFx0Y291bnRyeSxcclxuXHRcdHNldExhbmd1YWdlLFxyXG5cdFx0c2V0Q3VycmVuY3ksXHJcblx0XHRzZXRDb3VudHJ5LFxyXG5cdH0pLCBbbGFuZ3VhZ2UsIGN1cnJlbmN5LCBjb3VudHJ5LCBzZXRMYW5ndWFnZSwgc2V0Q3VycmVuY3ksIHNldENvdW50cnldKTtcclxuXHJcblx0cmV0dXJuIChcclxuXHRcdDxQcmVmZXJlbmNlc0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cclxuXHRcdFx0e2NoaWxkcmVufVxyXG5cdFx0PC9QcmVmZXJlbmNlc0NvbnRleHQuUHJvdmlkZXI+XHJcblx0KTtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHVzZVByZWZlcmVuY2VzKCk6IFByZWZlcmVuY2VzQ29udGV4dFZhbHVlIHtcclxuXHRjb25zdCBjdHggPSB1c2VDb250ZXh0KFByZWZlcmVuY2VzQ29udGV4dCk7XHJcblx0aWYgKCFjdHgpIHtcclxuXHRcdHRocm93IG5ldyBFcnJvcigndXNlUHJlZmVyZW5jZXMgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFByZWZlcmVuY2VzUHJvdmlkZXInKTtcclxuXHR9XHJcblx0cmV0dXJuIGN0eDtcclxufVxyXG5cclxuXHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ2FsbGJhY2siLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlTWVtbyIsInVzZVN0YXRlIiwidXNlQXV0aCIsImdldEJyb3dzZXJQcmVmZXJlbmNlRGVmYXVsdHMiLCJmZXRjaFdpdGhDcmVkZW50aWFscyIsImdldEJhY2tlbmRVcmwiLCJQcmVmZXJlbmNlc0NvbnRleHQiLCJ1bmRlZmluZWQiLCJnZXRCcm93c2VyRGVmYXVsdHMiLCJsYW5ndWFnZSIsImNvdW50cnkiLCJjdXJyZW5jeSIsIlByZWZlcmVuY2VzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJkZWZhdWx0cyIsInNldExhbmd1YWdlU3RhdGUiLCJzZXRDdXJyZW5jeVN0YXRlIiwic2V0Q291bnRyeVN0YXRlIiwic3RvcmFnZUtleSIsInVzZXJJZCIsImlkIiwiU3RyaW5nIiwia2V5IiwiY29uc29sZSIsImRlYnVnIiwicmF3IiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInBhcnNlZCIsIkpTT04iLCJwYXJzZSIsIm5leHRMYW5ndWFnZSIsIm5leHRDdXJyZW5jeSIsIm5leHRDb3VudHJ5Iiwib25BdXRoQ2hhbmdlIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJhYm9ydGVkIiwidXJsIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwicmVzcCIsIm9rIiwicGF5bG9hZCIsImpzb24iLCJwcmVmZXJyZWRMYW5ndWFnZSIsInByZWZlcnJlZEN1cnJlbmN5IiwiY291bnRyeUNvZGUiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwicGVyc2lzdCIsIm5leHQiLCJjdXJyZW50IiwibWVyZ2VkIiwic2V0TGFuZ3VhZ2UiLCJsbmciLCJzZXRDdXJyZW5jeSIsImN1ciIsImZyb20iLCJ0byIsInNldENvdW50cnkiLCJjYyIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VQcmVmZXJlbmNlcyIsImN0eCIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/context/PreferencesContext.tsx\n"));

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/EntityTable.fadein.css */ \"./src/components/EntityTable.fadein.css\");\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../context/PreferencesContext */ \"./src/context/PreferencesContext.tsx\");\n/* harmony import */ var _utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/useAuthGuard */ \"./src/utils/useAuthGuard.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction MyApp(param) {\n    let { Component, pageProps } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_StoreContext__WEBPACK_IMPORTED_MODULE_4__.StoreProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__.PreferencesProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GuardedApp, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                        ...pageProps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = MyApp;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MyApp);\nfunction GuardedApp(param) {\n    let { children } = param;\n    _s();\n    // Run global auth guard on every route navigation\n    (0,_utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__.useAuthGuard)({\n        publicPaths: [\n            \"/\",\n            \"/login\",\n            \"/_error\",\n            \"/setup/store\",\n            \"/store/[storeUuid]\",\n            \"/storefront\"\n        ]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(GuardedApp, \"TitGtyjW32Onfz5lscu0IFsEfl4=\", false, function() {\n    return [\n        _utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__.useAuthGuard\n    ];\n});\n_c1 = GuardedApp;\nvar _c, _c1;\n$RefreshReg$(_c, \"MyApp\");\n$RefreshReg$(_c1, \"GuardedApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n"));

/***/ }),

/***/ "./src/utils/api.ts":
/*!**************************!*\
  !*** ./src/utils/api.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: function() { return /* binding */ ApiClient; },\n/* harmony export */   agentApi: function() { return /* binding */ agentApi; },\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; },\n/* harmony export */   conversationApi: function() { return /* binding */ conversationApi; },\n/* harmony export */   customerApi: function() { return /* binding */ customerApi; },\n/* harmony export */   imageApi: function() { return /* binding */ imageApi; },\n/* harmony export */   orderApi: function() { return /* binding */ orderApi; },\n/* harmony export */   productApi: function() { return /* binding */ productApi; },\n/* harmony export */   storeApi: function() { return /* binding */ storeApi; },\n/* harmony export */   userApi: function() { return /* binding */ userApi; }\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\n// Helper function to handle API responses\nasync function handleResponse(response) {\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.error || \"HTTP error! status: \".concat(response.status));\n    }\n    return response.json();\n}\n// Helper function to get auth headers\nfunction getAuthHeaders() {\n    return {\n        \"Content-Type\": \"application/json\"\n    };\n}\n// Generic API client class\nclass ApiClient {\n    // Generic GET request\n    async get(endpoint, params) {\n        const url = new URL(\"\".concat(this.baseUrl).concat(endpoint));\n        if (params) {\n            Object.entries(params).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        console.log(\"API GET request to:\", url.toString());\n        const response = await fetch(url.toString(), {\n            method: \"GET\",\n            headers: getAuthHeaders(),\n            credentials: \"include\"\n        });\n        return handleResponse(response);\n    }\n    // Generic POST request\n    async post(endpoint, data) {\n        const response = await fetch(\"\".concat(this.baseUrl).concat(endpoint), {\n            method: \"POST\",\n            headers: getAuthHeaders(),\n            credentials: \"include\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n        return handleResponse(response);\n    }\n    // Generic PUT request\n    async put(endpoint, data) {\n        const response = await fetch(\"\".concat(this.baseUrl).concat(endpoint), {\n            method: \"PUT\",\n            headers: getAuthHeaders(),\n            credentials: \"include\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n        return handleResponse(response);\n    }\n    // Generic DELETE request\n    async delete(endpoint) {\n        const response = await fetch(\"\".concat(this.baseUrl).concat(endpoint), {\n            method: \"DELETE\",\n            headers: getAuthHeaders(),\n            credentials: \"include\"\n        });\n        return handleResponse(response);\n    }\n    constructor(baseUrl = API_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n}\n// Create a default API client instance\nconst apiClient = new ApiClient();\n// Store API methods\nconst storeApi = {\n    getAll: (params)=>apiClient.get(\"/api/stores\", params),\n    getById: (id)=>apiClient.get(\"/api/stores/\".concat(id)),\n    getByUserId: (userId, params)=>apiClient.get(\"/api/stores/user/\".concat(userId), params),\n    getByUuid: (uuid)=>apiClient.get(\"/api/stores/uuid/\".concat(uuid)),\n    create: (data)=>apiClient.post(\"/api/stores\", data),\n    update: (id, data)=>apiClient.put(\"/api/stores/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/api/stores/\".concat(id))\n};\n// Product API methods\nconst productApi = {\n    getAll: (params)=>apiClient.get(\"/api/products\", params),\n    getById: (id)=>apiClient.get(\"/api/products/\".concat(id)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/api/products/store/\".concat(storeId), params),\n    getByStoreUuid: (storeUuid, params)=>apiClient.get(\"/api/products/store/uuid/\".concat(storeUuid), params),\n    create: (data)=>apiClient.post(\"/api/products\", data),\n    update: (id, data)=>apiClient.put(\"/api/products/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/api/products/\".concat(id))\n};\n// Customer API methods\nconst customerApi = {\n    getAll: (params)=>apiClient.get(\"/api/customers\", params),\n    getById: (id)=>apiClient.get(\"/api/customers/\".concat(id)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/api/customers/store/\".concat(storeId), params),\n    create: (data)=>apiClient.post(\"/api/customers\", data),\n    update: (id, data)=>apiClient.put(\"/api/customers/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/api/customers/\".concat(id))\n};\n// User API methods\nconst userApi = {\n    getAll: (params)=>apiClient.get(\"/api/users\", params),\n    getById: (id)=>apiClient.get(\"/api/users/\".concat(id)),\n    create: (data)=>apiClient.post(\"/api/users\", data),\n    update: (id, data)=>apiClient.put(\"/api/users/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/api/users/\".concat(id))\n};\n// Order API methods\nconst orderApi = {\n    getAll: (params)=>apiClient.get(\"/api/orders\", params),\n    getById: (id)=>apiClient.get(\"/api/orders/\".concat(id)),\n    getByOrderNumber: (orderNumber)=>apiClient.get(\"/api/orders/order-number/\".concat(orderNumber)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/api/orders/store/\".concat(storeId), params),\n    filter: (data)=>apiClient.post(\"/api/orders/filter\", data),\n    create: (data)=>apiClient.post(\"/api/orders\", data),\n    update: (id, data)=>apiClient.put(\"/api/orders/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/api/orders/\".concat(id))\n};\n// Conversation API methods (using existing endpoints)\nconst conversationApi = {\n    getAll: (params)=>apiClient.get(\"/api/conversations\", params),\n    getById: (id)=>apiClient.get(\"/api/conversations/\".concat(id)),\n    getByUuid: (uuid)=>apiClient.get(\"/api/conversations/uuid/\".concat(uuid)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/api/conversations/store/\".concat(storeId), params),\n    getByUserId: (userId, params)=>apiClient.get(\"/api/conversations/user/\".concat(userId), params),\n    create: (data)=>apiClient.post(\"/api/conversations\", data),\n    getTimeline: (id, params)=>apiClient.get(\"/api/conversations/\".concat(id, \"/timeline\"), params),\n    getUnifiedTimeline: (id, params)=>apiClient.get(\"/api/conversations/\".concat(id, \"/unified-timeline\"), params),\n    getUnifiedTimelineByUuid: (uuid, params)=>apiClient.get(\"/api/conversations/uuid/\".concat(uuid, \"/unified-timeline\"), params),\n    appendMessage: (id, data)=>apiClient.post(\"/api/conversations/\".concat(id, \"/messages\"), data),\n    appendMessageByUuid: (uuid, data)=>apiClient.post(\"/api/conversations/uuid/\".concat(uuid, \"/messages\"), data)\n};\n// Image API methods\nconst imageApi = {\n    upload: (file)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return fetch(\"/api/images\", {\n            method: \"POST\",\n            body: formData\n        }).then((response)=>{\n            if (!response.ok) {\n                return response.json().then((errorData)=>{\n                    throw new Error(errorData.error || \"Failed to upload image\");\n                });\n            }\n            return response.json();\n        });\n    }\n};\n// Agent API methods (using existing endpoints)\nconst agentApi = {\n    getAll: (params)=>apiClient.get(\"/api/agents\", params),\n    getById: (id)=>apiClient.get(\"/api/agents/\".concat(id)),\n    create: (data)=>apiClient.post(\"/api/agents\", data),\n    update: (id, data)=>apiClient.put(\"/api/agents/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/api/agents/\".concat(id)),\n    generateMessage: (data)=>apiClient.post(\"/api/agents/runtime/generate-message\", data)\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/api.ts\n"));

/***/ }),

/***/ "./src/utils/auth.ts":
/*!***************************!*\
  !*** ./src/utils/auth.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearClientAuthArtifacts: function() { return /* binding */ clearClientAuthArtifacts; },\n/* harmony export */   debugLogin: function() { return /* binding */ debugLogin; },\n/* harmony export */   fetchWithCredentials: function() { return /* binding */ fetchWithCredentials; },\n/* harmony export */   getBackendUrl: function() { return /* binding */ getBackendUrl; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   handleUnauthorized: function() { return /* binding */ handleUnauthorized; },\n/* harmony export */   performLogout: function() { return /* binding */ performLogout; },\n/* harmony export */   redirectToGoogleAuth: function() { return /* binding */ redirectToGoogleAuth; }\n/* harmony export */ });\nconst getBackendUrl = ()=>{\n    return \"http://localhost:8000\" || 0;\n};\nconst clearClientAuthArtifacts = ()=>{\n    try {\n        // Clear local cache of user\n        if (typeof localStorage !== \"undefined\") {\n            localStorage.removeItem(\"teno:auth:user\");\n        }\n    } catch (e) {}\n    try {\n        // Proactively drop any readable client token if it exists\n        if (typeof document !== \"undefined\") {\n            // Expire both potential names just in case\n            document.cookie = \"access_token_client=; Path=/; Max-Age=0; SameSite=Lax\";\n            document.cookie = \"access_token=; Path=/; Max-Age=0; SameSite=Lax\";\n        }\n    } catch (e) {}\n};\nconst handleUnauthorized = ()=>{\n    // Ensure client artifacts are cleared immediately\n    clearClientAuthArtifacts();\n    try {\n        // Notify any listeners (e.g., UI) that auth state became unauthorized\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"teno:auth:unauthorized\"));\n        }\n    } catch (e) {}\n    // Best-effort redirect to login preserving next path\n    try {\n        if (true) {\n            const next = encodeURIComponent(window.location.pathname + window.location.search);\n            const target = \"/login?loggedOut=1&next=\".concat(next);\n            // Avoid infinite loops if we are already on login\n            if (!window.location.pathname.startsWith(\"/login\")) {\n                window.location.replace(target);\n            }\n        }\n    } catch (e) {}\n};\nconst fetchWithCredentials = async (input, init)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...init && init.headers ? init.headers : {}\n    };\n    // In development, the backend sets a readable access_token_client cookie.\n    // Attach it as Authorization so we don't rely on cross-site cookies.\n    try {\n        if (typeof document !== \"undefined\" && !headers[\"Authorization\"]) {\n            const match = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);\n            const token = match ? decodeURIComponent(match[1]) : undefined;\n            if (token) {\n                headers[\"Authorization\"] = \"Bearer \".concat(token);\n            }\n        }\n    } catch (e) {}\n    const response = await fetch(input, {\n        ...init,\n        credentials: \"include\",\n        headers\n    });\n    // If backend says unauthorized/forbidden, clear local auth and nudge UI\n    if (response.status === 401 || response.status === 403) {\n        handleUnauthorized();\n    }\n    return response;\n};\nconst getCurrentUser = async ()=>{\n    const url = \"\".concat(getBackendUrl(), \"/api/auth/me\");\n    const response = await fetchWithCredentials(url);\n    if (!response.ok) return null;\n    const data = await response.json();\n    var _data_user;\n    return (_data_user = data.user) !== null && _data_user !== void 0 ? _data_user : null;\n};\nconst performLogout = async ()=>{\n    const url = \"\".concat(getBackendUrl(), \"/api/auth/logout\");\n    try {\n        await fetchWithCredentials(url, {\n            method: \"POST\"\n        });\n    } finally{\n        // Always clear client artifacts regardless of server response\n        clearClientAuthArtifacts();\n    }\n};\nconst redirectToGoogleAuth = (nextPath)=>{\n    let url = \"\".concat(getBackendUrl(), \"/api/auth/google\");\n    try {\n        // Prefer explicit nextPath, otherwise pick it up from current URL (?next=...)\n        let nextParam = nextPath;\n        if (!nextParam && \"object\" !== \"undefined\") {\n            const sp = new URLSearchParams(window.location.search);\n            const qp = sp.get(\"next\") || undefined;\n            nextParam = qp || undefined;\n        }\n        if (nextParam) {\n            // Only allow app-internal paths starting with '/'\n            const safeNext = decodeURIComponent(nextParam);\n            if (safeNext.startsWith(\"/\")) {\n                url += \"?next=\".concat(encodeURIComponent(safeNext));\n            }\n        }\n    } catch (e) {}\n    if (true) {\n        window.location.href = url;\n    }\n};\nconst debugLogin = async (email, name)=>{\n    const url = \"\".concat(getBackendUrl(), \"/api/auth/dev-login\");\n    const response = await fetchWithCredentials(url, {\n        method: \"POST\",\n        body: JSON.stringify({\n            email,\n            name\n        })\n    });\n    if (!response.ok) return null;\n    const data = await response.json();\n    var _data_user;\n    return (_data_user = data.user) !== null && _data_user !== void 0 ? _data_user : null;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/auth.ts\n"));

/***/ }),

/***/ "./src/utils/useAuthGuard.ts":
/*!***********************************!*\
  !*** ./src/utils/useAuthGuard.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthGuard: function() { return /* binding */ useAuthGuard; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./api */ \"./src/utils/api.ts\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n\n\n\n\n\n/**\r\n * Global auth guard that runs on page mount and route changes.\r\n * - Redirects unauthenticated users away from protected routes\r\n * - Adds verbose console logging for debugging\r\n * - Prevents redirect loops using an in-flight ref and public-path checks\r\n */ function useAuthGuard(options) {\n    var _globalThis;\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, isLoading, error, refresh } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const isRedirectingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const [storesData, setStoresData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isStoresLoading, setIsStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Module-scoped cache to persist across StrictMode re-mounts in dev\n    // and across component re-renders. Keyed by pathname only.\n    const handledCache = (_globalThis = globalThis).__authGuardHandledCache__ || (_globalThis.__authGuardHandledCache__ = {});\n    var _options_publicPaths;\n    // Configure public paths. Everything else is considered protected.\n    const publicPaths = (_options_publicPaths = options === null || options === void 0 ? void 0 : options.publicPaths) !== null && _options_publicPaths !== void 0 ? _options_publicPaths : [\n        \"/\",\n        \"/login\",\n        \"/_error\",\n        \"/setup/store\",\n        \"/track\",\n        \"/track/[orderNumber]\"\n    ];\n    const isPublic = (path)=>{\n        if (!path) return false;\n        // Exact path match for static routes\n        if (publicPaths.includes(path)) return true;\n        // Check for dynamic track routes (e.g., /track/ORD-20250115-001)\n        if (path.startsWith(\"/track/\") && path !== \"/track\") return true;\n        return false;\n    };\n    // Reset cache entry for the NEXT route so the guard runs once there\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const onRouteChangeStart = (url)=>{\n            try {\n                const nextPath = new URL(url, window.location.origin).pathname;\n                delete handledCache[nextPath];\n            } catch (e) {\n                // Fallback: if URL parsing fails, clear all\n                for (const key of Object.keys(handledCache))delete handledCache[key];\n            }\n        };\n        router.events.on(\"routeChangeStart\", onRouteChangeStart);\n        return ()=>{\n            router.events.off(\"routeChangeStart\", onRouteChangeStart);\n        };\n    }, [\n        router.events\n    ]);\n    // Determine store availability for the authenticated user\n    const userIdBigInt = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>(user === null || user === void 0 ? void 0 : user.id) ? BigInt(user.id) : null, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    const { currentStoreId, setCurrentStoreId } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    // Fetch stores data\n    const fetchStores = async ()=>{\n        if (!userIdBigInt) {\n            console.log(\"[AuthGuard] fetchStores: No userIdBigInt, skipping\");\n            return;\n        }\n        console.log(\"[AuthGuard] fetchStores: Starting fetch for user:\", userIdBigInt.toString());\n        setIsStoresLoading(true);\n        try {\n            const result = await _api__WEBPACK_IMPORTED_MODULE_3__.storeApi.getByUserId(userIdBigInt.toString(), {\n                page: 1,\n                limit: 1\n            });\n            console.log(\"[AuthGuard] fetchStores: API response:\", result);\n            if (result && typeof result === \"object\" && \"data\" in result && \"meta\" in result) {\n                setStoresData(result);\n                console.log(\"[AuthGuard] fetchStores: Set stores data with meta:\", result.meta);\n            } else {\n                const fallbackData = {\n                    data: Array.isArray(result) ? result : [],\n                    meta: {\n                        total: 0\n                    }\n                };\n                setStoresData(fallbackData);\n                console.log(\"[AuthGuard] fetchStores: Set fallback data:\", fallbackData);\n            }\n        } catch (err) {\n            console.error(\"[AuthGuard] fetchStores: Error fetching stores:\", err);\n            const errorData = {\n                data: [],\n                meta: {\n                    total: 0\n                }\n            };\n            setStoresData(errorData);\n            console.log(\"[AuthGuard] fetchStores: Set error data:\", errorData);\n        } finally{\n            setIsStoresLoading(false);\n            console.log(\"[AuthGuard] fetchStores: Set loading to false\");\n        }\n    };\n    // Fetch stores when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (userIdBigInt) {\n            fetchStores();\n        }\n    }, [\n        userIdBigInt\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        const asPath = router.asPath;\n        // Use asPath so dynamic route params are preserved (e.g., /live/123 not /live/[uuid])\n        const fullPath = asPath || currentPath;\n        const searchParams = new URLSearchParams( true ? window.location.search : 0);\n        const loggedOutFlag = searchParams.get(\"loggedOut\");\n        // Avoid redirecting while we are still determining auth state\n        if (isLoading) return;\n        // Ensure we only handle/log once per path after loading has completed\n        if (handledCache[currentPath]) {\n            return;\n        }\n        console.log(\"[AuthGuard] route/useEffect fired\", {\n            pathname: currentPath,\n            asPath: router.asPath,\n            isLoading,\n            hasUser: !!user,\n            error\n        });\n        if (!user && !isPublic(currentPath)) {\n            if (isRedirectingRef.current) {\n                console.log(\"[AuthGuard] Redirect already in progress; skipping\");\n                return;\n            }\n            isRedirectingRef.current = true;\n            const nextParam = encodeURIComponent(fullPath || \"/\");\n            const loginUrl = \"/login?next=\".concat(nextParam);\n            console.log(\"[AuthGuard] Not authenticated; redirecting to\", loginUrl);\n            router.replace(loginUrl).finally(()=>{\n                // Reset after navigation completes to allow future redirects\n                setTimeout(()=>{\n                    isRedirectingRef.current = false;\n                }, 0);\n            });\n            // Mark as handled to prevent duplicate processing for this path\n            handledCache[currentPath] = true;\n            return;\n        }\n        if (isPublic(currentPath) && currentPath === \"/login\" && loggedOutFlag === \"1\") {\n            // If explicitly coming from logout, do not auto-redirect to setup or dashboard\n            handledCache[currentPath] = true;\n            return;\n        }\n        if (user && isPublic(currentPath) && currentPath === \"/login\") {\n            // If user hits /login while already authenticated, choose destination\n            // Wait for store lookup to resolve to avoid prematurely sending users to dashboard\n            if (isStoresLoading) {\n                return;\n            }\n            // Only proceed with redirect logic if we have actually fetched stores data\n            if (storesData === null) {\n                return;\n            }\n            if (!isRedirectingRef.current) {\n                isRedirectingRef.current = true;\n                let target = \"/dashboard\";\n                try {\n                    var _storesData_meta, _storesData_data;\n                    const nextParam = searchParams.get(\"next\") || undefined;\n                    var _storesData_meta_total, _ref;\n                    const totalActive = (_ref = (_storesData_meta_total = storesData === null || storesData === void 0 ? void 0 : (_storesData_meta = storesData.meta) === null || _storesData_meta === void 0 ? void 0 : _storesData_meta.total) !== null && _storesData_meta_total !== void 0 ? _storesData_meta_total : storesData === null || storesData === void 0 ? void 0 : (_storesData_data = storesData.data) === null || _storesData_data === void 0 ? void 0 : _storesData_data.length) !== null && _ref !== void 0 ? _ref : 0;\n                    if (nextParam && nextParam.startsWith(\"/\")) {\n                        target = nextParam;\n                    } else {\n                        // Only redirect to setup if user has no active stores\n                        if (totalActive === 0) {\n                            target = \"/setup/store\";\n                        }\n                    }\n                } catch (e) {}\n                console.log(\"[AuthGuard] Already authenticated; redirecting away from public auth page to\", target);\n                router.replace(target).finally(()=>{\n                    setTimeout(()=>{\n                        isRedirectingRef.current = false;\n                    }, 0);\n                });\n            }\n            handledCache[currentPath] = true;\n            return;\n        }\n        // If authenticated on a protected route and user has no store yet, redirect to setup\n        if (user && !isPublic(currentPath)) {\n            var _storesData_meta1, _storesData_data1;\n            // Only proceed with redirect logic if we have actually fetched stores data\n            if (storesData === null) {\n                console.log(\"[AuthGuard] Stores data not yet fetched, waiting...\");\n                return;\n            }\n            var _storesData_meta_total1, _ref1;\n            const totalActive = (_ref1 = (_storesData_meta_total1 = storesData === null || storesData === void 0 ? void 0 : (_storesData_meta1 = storesData.meta) === null || _storesData_meta1 === void 0 ? void 0 : _storesData_meta1.total) !== null && _storesData_meta_total1 !== void 0 ? _storesData_meta_total1 : storesData === null || storesData === void 0 ? void 0 : (_storesData_data1 = storesData.data) === null || _storesData_data1 === void 0 ? void 0 : _storesData_data1.length) !== null && _ref1 !== void 0 ? _ref1 : 0;\n            console.log(\"[AuthGuard] Store check:\", {\n                currentPath,\n                totalActive,\n                storesData,\n                isStoresLoading,\n                loggedOutFlag\n            });\n            // Never redirect to setup from login page or when explicitly logged out\n            // Only redirect if user has no active stores\n            if (!isStoresLoading && totalActive === 0 && currentPath !== \"/setup/store\" && loggedOutFlag !== \"1\") {\n                if (!isRedirectingRef.current) {\n                    isRedirectingRef.current = true;\n                    console.log(\"[AuthGuard] Authenticated user without active stores; redirecting to /setup/store\");\n                    router.replace(\"/setup/store\").finally(()=>{\n                        setTimeout(()=>{\n                            isRedirectingRef.current = false;\n                        }, 0);\n                    });\n                    handledCache[currentPath] = true;\n                    return;\n                }\n            } else if (totalActive > 0 && !currentStoreId) {\n                var _storesData_data_, _storesData_data2;\n                const firstId = storesData === null || storesData === void 0 ? void 0 : (_storesData_data2 = storesData.data) === null || _storesData_data2 === void 0 ? void 0 : (_storesData_data_ = _storesData_data2[0]) === null || _storesData_data_ === void 0 ? void 0 : _storesData_data_.id;\n                if (firstId) setCurrentStoreId(String(firstId));\n            }\n        }\n        // For authenticated access: do not mark handled while store query is still loading,\n        // otherwise we won't get a chance to redirect once it resolves\n        if (!(user && !isPublic(currentPath) && isStoresLoading)) {\n            handledCache[currentPath] = true;\n        }\n    }, [\n        router.pathname,\n        isLoading,\n        user,\n        error,\n        isStoresLoading,\n        storesData,\n        currentStoreId\n    ]);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/useAuthGuard.ts\n"));

/***/ })

});