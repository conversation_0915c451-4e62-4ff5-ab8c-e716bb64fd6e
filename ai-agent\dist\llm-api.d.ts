import { Agent } from './agent';
import { Message, LlmResponseResult } from './types';
/**
 * Executes agents with the OpenAI API.
 *
 * This class handles the execution flow for agents, including:
 * - Setting up the OpenAI client
 * - Managing the conversation
 * - Handling tool calls and responses
 */
export declare class LlmApi {
    /**
     * Generate an LLM response given an agent and a list of chat messages.
     * Messages may be provided as `{ role, message }` or `{ role, content }`.
     * Returns a LlmResponseResult containing both the message response and tool calls.
     */
    static generateLlmResponse(agent: Agent, chatMessages: Message[], model?: string): Promise<LlmResponseResult>;
}
//# sourceMappingURL=llm-api.d.ts.map