import { useState, useEffect } from 'react';
import { getCurrentUser } from '../utils/auth';

export default function DebugAuth() {
  const [cookieToken, setCookieToken] = useState<string | null>(null);
  const [localStorageToken, setLocalStorageToken] = useState<string | null>(null);
  const [apiResult, setApiResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testToken, setTestToken] = useState('');

  useEffect(() => {
    // Check for tokens on mount
    checkTokens();
  }, []);

  const checkTokens = () => {
    // Check cookie
    if (typeof document !== 'undefined') {
      const match = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);
      if (match) {
        setCookieToken(decodeURIComponent(match[1]));
      }
    }

    // Check localStorage
    try {
      if (typeof localStorage !== 'undefined') {
        const token = localStorage.getItem('teno:auth:token');
        setLocalStorageToken(token);
      }
    } catch (e) {
      console.error('Error accessing localStorage:', e);
    }
  };

  const testApiCall = async () => {
    setIsLoading(true);
    try {
      const user = await getCurrentUser();
      setApiResult({ success: true, user });
    } catch (error) {
      setApiResult({ success: false, error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const clearTokens = () => {
    // Clear cookie
    if (typeof document !== 'undefined') {
      document.cookie = 'access_token_client=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    }
    
    // Clear localStorage
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem('teno:auth:token');
      }
    } catch (e) {
      console.error('Error clearing localStorage:', e);
    }
    
    checkTokens();
  };

  const setTestTokenManually = () => {
    if (!testToken.trim()) {
      alert('Please enter a test token');
      return;
    }

    // Set in cookie
    document.cookie = `access_token_client=${encodeURIComponent(testToken)}; path=/; max-age=86400; samesite=lax`;
    
    // Set in localStorage
    try {
      localStorage.setItem('teno:auth:token', testToken);
    } catch (e) {
      console.error('Error setting localStorage token:', e);
    }
    
    checkTokens();
    setTestToken('');
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Authentication Debug</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Token Storage</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-300">Cookie Token:</h3>
                <div className="bg-gray-700 p-3 rounded mt-2 font-mono text-sm break-all">
                  {cookieToken ? (
                    <>
                      <div className="text-green-400">✓ Found</div>
                      <div className="text-gray-400 mt-1">
                        {cookieToken.substring(0, 50)}...
                      </div>
                    </>
                  ) : (
                    <span className="text-red-400">✗ Not found</span>
                  )}
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-300">LocalStorage Token:</h3>
                <div className="bg-gray-700 p-3 rounded mt-2 font-mono text-sm break-all">
                  {localStorageToken ? (
                    <>
                      <div className="text-green-400">✓ Found</div>
                      <div className="text-gray-400 mt-1">
                        {localStorageToken.substring(0, 50)}...
                      </div>
                    </>
                  ) : (
                    <span className="text-red-400">✗ Not found</span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="mt-6 space-y-3">
              <button
                onClick={checkTokens}
                className="w-full bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded"
              >
                Refresh Token Check
              </button>
              
              <button
                onClick={clearTokens}
                className="w-full bg-red-600 hover:bg-red-700 px-4 py-2 rounded"
              >
                Clear All Tokens
              </button>
            </div>
          </div>
          
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">API Test</h2>
            
            <button
              onClick={testApiCall}
              disabled={isLoading}
              className="w-full bg-green-600 hover:bg-green-700 px-4 py-2 rounded disabled:opacity-50"
            >
              {isLoading ? 'Testing...' : 'Test /auth/me'}
            </button>
            
            {apiResult && (
              <div className="mt-4">
                <h3 className="font-medium text-gray-300 mb-2">Result:</h3>
                <div className="bg-gray-700 p-3 rounded">
                  <pre className="text-sm overflow-auto">
                    {JSON.stringify(apiResult, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="mt-8 bg-gray-800 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Manual Token Test</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Test JWT Token:
              </label>
              <input
                type="text"
                value={testToken}
                onChange={(e) => setTestToken(e.target.value)}
                placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white placeholder-gray-400"
              />
            </div>
            <button
              onClick={setTestTokenManually}
              className="w-full bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded"
            >
              Set Test Token
            </button>
          </div>
        </div>
        
        <div className="mt-8 bg-gray-800 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-300">
            <li>Go to <a href="/login" className="text-blue-400 hover:underline">/login</a> and click "Continue with Google"</li>
            <li>Complete Google OAuth</li>
            <li>You should be redirected to <a href="/auth/callback" className="text-blue-400 hover:underline">/auth/callback</a></li>
            <li>Return here and check if tokens are stored</li>
            <li>Test the API call to see if authentication works</li>
            <li>Or manually set a test token above to debug</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
