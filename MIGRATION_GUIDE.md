# Migration from tRPC to REST API

This document outlines the migration from tRPC to a standard REST API implementation.

## Overview

The project has been migrated from tRPC to REST API for the following reasons:
- **Simplified architecture**: REST APIs are more standard and easier to understand
- **Better tooling support**: REST APIs work with standard HTTP tools and libraries
- **Easier debugging**: Standard HTTP requests are easier to debug and test
- **Reduced bundle size**: No need for tRPC client libraries in the frontend

## What Changed

### Backend Changes

1. **Removed tRPC dependencies** from `package.json`
2. **Created REST API endpoints** for all previously tRPC-exposed functionality
3. **Updated CORS handling** to work with direct REST calls
4. **Added new endpoints**:
   - `/api/users` - User management
   - `/api/orders/order-number/{orderNumber}` - Order lookup by order number

### Frontend Changes

1. **Removed tRPC client dependencies** from `package.json`
2. **Created comprehensive REST API utilities** in `src/utils/api.ts`
3. **Added mock tRPC implementation** for backward compatibility during transition
4. **Updated Next.js config** to remove tRPC proxy

## API Endpoints

### Users
- `GET /api/users` - List users with pagination
- `POST /api/users` - Create user
- `GET /api/users/{id}` - Get user by ID
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user

### Stores
- `GET /api/stores` - List stores with pagination
- `POST /api/stores` - Create store
- `GET /api/stores/{id}` - Get store by ID
- `PUT /api/stores/{id}` - Update store
- `DELETE /api/stores/{id}` - Delete store
- `GET /api/stores/user/{userId}` - Get stores by user ID
- `GET /api/stores/uuid/{uuid}` - Get store by UUID

### Products
- `GET /api/products` - List products with pagination
- `POST /api/products` - Create product
- `GET /api/products/{id}` - Get product by ID
- `PUT /api/products/{id}` - Update product
- `DELETE /api/products/{id}` - Delete product
- `GET /api/products/store/{storeId}` - Get products by store ID
- `GET /api/products/store/uuid/{storeUuid}` - Get products by store UUID

### Customers
- `GET /api/customers` - List customers with pagination
- `POST /api/customers` - Create customer
- `GET /api/customers/{id}` - Get customer by ID
- `PUT /api/customers/{id}` - Update customer
- `DELETE /api/customers/{id}` - Delete customer
- `GET /api/customers/store/{storeId}` - Get customers by store ID

### Orders
- `GET /api/orders` - List orders with pagination
- `POST /api/orders` - Create order
- `GET /api/orders/{id}` - Get order by ID
- `PUT /api/orders/{id}` - Update order
- `DELETE /api/orders/{id}` - Delete order
- `GET /api/orders/order-number/{orderNumber}` - Get order by order number
- `POST /api/orders/filter` - Filter orders with criteria
- `GET /api/orders/store/{storeId}` - Get orders by store ID

### Conversations
- `GET /api/conversations` - List conversations with pagination
- `POST /api/conversations` - Create conversation
- `GET /api/conversations/{id}` - Get conversation by ID
- `PUT /api/conversations/{id}` - Update conversation
- `DELETE /api/conversations/{id}` - Delete conversation
- `GET /api/conversations/{id}/messages` - Get conversation messages
- `POST /api/conversations/{id}/messages` - Add message to conversation
- `GET /api/conversations/{id}/timeline` - Get conversation timeline
- `GET /api/conversations/{id}/unified-timeline` - Get conversation unified timeline
- `GET /api/conversations/uuid/{uuid}` - Get conversation by UUID
- `GET /api/conversations/uuid/{uuid}/unified-timeline` - Get conversation unified timeline by UUID
- `POST /api/conversations/uuid/{uuid}/messages` - Add message to conversation by UUID

### Agents
- `GET /api/agents` - List agents with pagination
- `POST /api/agents` - Create agent
- `GET /api/agents/{id}` - Get agent by ID
- `PUT /api/agents/{id}` - Update agent
- `DELETE /api/agents/{id}` - Delete agent
- `POST /api/agents/runtime/generate-message` - Generate agent message

## Frontend Usage

### Before (tRPC)
```typescript
import { trpc } from '../utils/trpc';

const userQuery = trpc.user.getById.useQuery({ id: userId });
const updateMutation = trpc.user.update.useMutation();
```

### After (REST API)
```typescript
import { userApi } from '../utils/api';

// For queries, use the useApiQuery hook
const { data: user, isLoading, error, refetch } = useApiQuery(
  () => userApi.getById(userId),
  [userId],
  !!userId
);

// For mutations, use the useApiMutation hook
const { mutate: updateUser, isLoading: isUpdating } = useApiMutation(
  (data) => userApi.update(userId, data)
);
```

## Migration Steps

### 1. Update Frontend Components

Replace tRPC usage with REST API calls:

```typescript
// Old tRPC pattern
const { data, isLoading, error } = trpc.conversation.getAll.useQuery({ page, limit });

// New REST API pattern
const { data, isLoading, error, refetch } = useApiQuery(
  () => conversationApi.getAll({ page, limit }),
  [page, limit]
);
```

### 2. Update Mutations

```typescript
// Old tRPC pattern
const createMutation = trpc.conversation.create.useMutation();
await createMutation.mutateAsync(data);

// New REST API pattern
const { mutate: createConversation, isLoading } = useApiMutation(
  (data) => conversationApi.create(data)
);
createConversation(data);
```

### 3. Remove tRPC Imports

Replace all `import { trpc } from '../utils/trpc';` with appropriate API imports:

```typescript
import { userApi, conversationApi, orderApi } from '../utils/api';
```

## Benefits of the Migration

1. **Standard HTTP**: Uses standard HTTP methods and status codes
2. **Better debugging**: Easier to debug with browser dev tools and Postman
3. **Reduced complexity**: No need to understand tRPC-specific patterns
4. **Better caching**: Standard HTTP caching mechanisms work out of the box
5. **Easier testing**: REST APIs are easier to mock and test

## Backward Compatibility

During the transition period, a mock tRPC implementation is provided in `src/utils/trpc.ts` that redirects calls to REST API endpoints. This allows for gradual migration of components.

## Testing the Migration

1. **Start the backend**: `npm run dev` in the backend directory
2. **Start the frontend**: `npm run dev` in the frontend directory
3. **Test API endpoints**: Visit `http://localhost:8000/api/rest` to see all available endpoints
4. **Test frontend functionality**: Navigate through the application to ensure all features work

## Troubleshooting

### Common Issues

1. **CORS errors**: Ensure the backend CORS configuration matches your frontend URL
2. **404 errors**: Check that the API endpoint paths match exactly
3. **Authentication issues**: Verify that auth headers are being sent correctly

### Debug Tips

1. **Check browser network tab** for failed requests
2. **Verify API endpoint paths** in the backend
3. **Check backend logs** for server-side errors
4. **Use the REST API documentation** at `/api/rest` to verify endpoint availability

## Next Steps

1. **Complete component migration**: Replace all remaining tRPC usage with REST API calls
2. **Remove mock tRPC file**: Once all components are migrated, remove `src/utils/trpc.ts`
3. **Add comprehensive error handling**: Implement proper error handling for all API calls
4. **Add loading states**: Ensure all async operations show appropriate loading indicators
5. **Add retry logic**: Implement retry mechanisms for failed API calls
6. **Add offline support**: Consider implementing offline-first patterns if needed
