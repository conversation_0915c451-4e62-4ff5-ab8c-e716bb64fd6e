import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Store } from './store.entity';
import { StoreStatus } from '../shared/enums';

interface CreateStoreDto {
  name: string;
  description?: string;
  currency?: string;
  preferredLanguage?: string;
  userId: string | number;
}

@Injectable()
export class StoresService {
  constructor(
    @InjectRepository(Store)
    private storesRepository: Repository<Store>,
  ) {}

  async findAll(): Promise<Store[]> {
    return this.storesRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Store> {
    const store = await this.storesRepository.findOne({
      where: { id, isDeleted: false },
    });
    
    if (!store) {
      throw new NotFoundException(`Store with ID ${id} not found`);
    }
    
    return store;
  }

  async findByUuid(uuid: string): Promise<Store> {
    // UUID functionality not implemented in current Store entity
    // This method should be updated to use a proper UUID field or removed
    throw new NotFoundException(`Store with UUID ${uuid} not found`);
  }

  async findByUserId(userId: string): Promise<Store[]> {
    return this.storesRepository.find({
      where: [
        { ownerId: userId, isDeleted: false },
        { managerId: userId, isDeleted: false }
      ],
      order: { createdAt: 'DESC' },
    });
  }

  async create(createStoreDto: CreateStoreDto): Promise<Store> {
    // Extract userId and map it to the correct fields
    const { userId, ...storeData } = createStoreDto;
    
    // Create the store with proper field mapping
    const store = new Store();
    Object.assign(store, {
      ...storeData,
      createdBy: userId,
      ownerId: userId,
      status: StoreStatus.ACTIVE,
      isDeleted: false
    });
    
    // Save and return the single store
    return await this.storesRepository.save(store);
  }

  async update(id: string, updateStoreDto: Partial<Store>): Promise<Store> {
    const store = await this.findOne(id);
    Object.assign(store, updateStoreDto);
    return this.storesRepository.save(store);
  }

  async remove(id: string): Promise<void> {
    const store = await this.findOne(id);
    store.isDeleted = true;
    await this.storesRepository.save(store);
  }
}
