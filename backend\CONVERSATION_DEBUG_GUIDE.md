# Conversation Controller Debug Guide

## Overview
I've added comprehensive debug logging to the conversation controller to help identify where the validation error `{ details: { uuid: undefined }, name: 'ValidationError' }` is occurring.

## What Was Added

### 1. Enhanced Logging in Conversation Controller
- **Request-level logging**: Every method now logs when it's called with request details
- **Validation logging**: All validation steps are logged with input data and results
- **Error logging**: Detailed error information including context and stack traces
- **Database operation logging**: Query parameters and results are logged

### 2. Enhanced Logging in Base Controller
- **Request context logging**: Full request details (method, URL, query, body, headers)
- **Validation logging**: Query and body validation with detailed error information
- **Error handling logging**: Enhanced error logging for validation errors

### 3. Test Script
- **`test-conversation-debug.js`**: Comprehensive test script to trigger all endpoints
- **Package script**: `npm run test:conversation-debug` to run the tests

## How to Use

### 1. Start Your Backend Server
```bash
cd backend
npm run dev
```

### 2. Run the Test Script
```bash
npm run test:conversation-debug
```

### 3. Check the Console Output
The enhanced logging will show:
- Which endpoint is being called
- What query parameters are received
- What validation is happening
- Where errors occur
- Full error context

## Expected Debug Output

When you run the tests, you should see logs like:

```
[DEBUG] conversation-controller.ts:67 - ConversationController.handleGet called { method: 'GET', url: '/api/conversations', query: { messages: 'true' }, headers: {...} }
[DEBUG] conversation-controller.ts:70 - Handling messages request
[DEBUG] conversation-controller.ts:204 - handleGetMessages called { id: undefined, uuid: undefined, query: { messages: 'true' } }
[ERROR] conversation-controller.ts:207 - Missing conversation identifier { id: undefined, uuid: undefined, query: { messages: 'true' } }
[ERROR] base-controller.ts:125 - Conversation controller error: ValidationError: Either conversation ID or UUID must be provided
[ERROR] base-controller.ts:127 - Validation error details: { message: 'Either conversation ID or UUID must be provided', details: { id: undefined, uuid: undefined }, stack: '...', name: 'ValidationError' }
```

## Key Areas to Monitor

### 1. Query Parameter Validation
Look for logs showing:
- What query parameters are received
- Which validation schema is being used
- What the validation result is

### 2. Request Routing
Look for logs showing:
- Which handler method is being called
- What the request context is

### 3. Error Context
Look for logs showing:
- Where the error occurred
- What the input data was
- What validation failed

## Common Issues to Look For

### 1. Missing Required Parameters
- `id` or `uuid` being undefined
- Required query parameters missing

### 2. Schema Validation Failures
- Query parameters not matching expected types
- Body data not matching expected schema

### 3. Route Handler Issues
- Wrong handler being called
- Missing query parameters for specific endpoints

## Next Steps

1. **Run the test script** to see which endpoints are failing
2. **Check the console logs** for detailed error information
3. **Identify the specific endpoint** that's causing the validation error
4. **Look at the request context** to see what data is missing
5. **Check the validation schemas** to ensure they match the expected data

## Files Modified

- `backend/src/controllers/conversation-controller.ts` - Added comprehensive debug logging
- `backend/src/controllers/base-controller.ts` - Enhanced error handling and validation logging
- `backend/test-conversation-debug.js` - Created test script
- `backend/package.json` - Added test script and dependency

## Dependencies Added

- `node-fetch@^2.7.0` - For the test script

## Running the Tests

```bash
# Install dependencies first
npm install

# Run the debug tests
npm run test:conversation-debug
```

This will help you identify exactly where and why the validation error is occurring in your conversation controller.
