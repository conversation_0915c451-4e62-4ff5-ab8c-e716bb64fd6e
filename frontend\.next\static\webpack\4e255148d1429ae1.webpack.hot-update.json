{"c": ["webpack"], "r": ["pages/products", "/_error"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cproducts%5Cindex.tsx&page=%2Fproducts!", "./src/components/EntityTable.tsx", "./src/components/ImageUpload.tsx", "./src/components/ProductModals.tsx", "./src/pages/products/index.tsx", "./src/utils/hooks.ts", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"]}