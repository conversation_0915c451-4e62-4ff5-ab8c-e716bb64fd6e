{"version": 3, "file": "generate-migration.js", "sourceRoot": "", "sources": ["../../src/scripts/generate-migration.ts"], "names": [], "mappings": ";;;AAEA,qCAAqC;AACrC,mCAAgC;AAChC,+BAA4B;AAC5B,2BAA0D;AAG1D,IAAA,eAAM,GAAE,CAAC;AAET,KAAK,UAAU,iBAAiB;IAC9B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC;IAEhG,MAAM,UAAU,GAAG,IAAI,oBAAU,CAAC;QAChC,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;QACxC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,CAAC;QAC7C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,UAAU;QAC3C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;QACvC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,eAAe;QAChD,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,yBAAyB,CAAC,CAAC;QACtD,UAAU,EAAE,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,0BAA0B,CAAC,CAAC;QACzD,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;KACnF,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAGjD,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,GAAG,EAAE,CAAC;QAExE,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC9C,OAAO;QACT,CAAC;QAGD,MAAM,aAAa,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QACvD,IAAI,CAAC,IAAA,eAAU,EAAC,aAAa,CAAC,EAAE,CAAC;YAC/B,IAAA,cAAS,EAAC,aAAa,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAChD,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,GAAG,SAAS,IAAI,aAAa,KAAK,CAAC;QACpD,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAG/C,MAAM,gBAAgB,GAAG;;eAEd,aAAa,GAAG,SAAS;cAC1B,aAAa,GAAG,SAAS;;;EAGrC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,qCAAqC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;EAIrG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,qCAAqC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;CAGxG,CAAC;QAGE,IAAA,kBAAa,EAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,gBAAgB,QAAQ,EAAE,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,kBAAkB,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,qBAAqB,WAAW,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;QAEnE,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC9C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,iBAAiB,EAAE,CAAC"}