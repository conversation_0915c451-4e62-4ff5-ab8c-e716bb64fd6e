# Teno Store

**Your complete e-commerce solution with AI-powered order management**

Teno Store is a comprehensive e-commerce platform that combines traditional inventory management with cutting-edge AI agents to streamline your business operations. Whether you're running a small boutique or scaling to multiple locations, Teno Store provides the tools you need to succeed in the digital marketplace.

## 🌟 Key Features

### 🛍️ **Storefront (Like Shopify)**
- **Public Store Directory**: Browse and discover stores without creating an account
- **Multi-language Support**: Built-in support for 15+ languages including English, French, Spanish, Arabic, and more
- **Responsive Design**: Beautiful, mobile-first storefronts that work on all devices
- **Product Catalogs**: Rich product displays with images, descriptions, and pricing
- **Order Tracking**: Public order tracking system for customers

### 🤖 **AI-Powered Order Management**
- **Intelligent Agents**: AI agents that can handle customer conversations and process orders
- **Automated Order Processing**: Agents can place orders, update customer information, and check order status
- **Natural Language Processing**: Customers can place orders using natural language
- **Smart Customer Updates**: AI agents automatically update customer records during conversations
- **Order Status Management**: Real-time order tracking and status updates

### 📦 **Inventory Management**
- **Product Catalog**: Comprehensive product management with SKUs, descriptions, and pricing
- **Stock Tracking**: Monitor inventory levels and product availability
- **Image Management**: Upload and manage product images with automatic optimization
- **Category Organization**: Organize products by categories and tags
- **Bulk Operations**: Import/export products and manage inventory at scale

### 👥 **Customer Relationship Management**
- **Customer Database**: Centralized customer information management
- **Contact Management**: Store customer details, addresses, and communication preferences
- **Order History**: Complete customer order tracking and history
- **Customer Segmentation**: Organize customers for targeted marketing
- **Communication Tools**: Integrated messaging and notification system

### 📊 **Sales & Analytics**
- **Order Management**: Complete order lifecycle from creation to fulfillment
- **Sales Tracking**: Monitor revenue, trends, and performance metrics
- **Reporting Tools**: Generate comprehensive business reports
- **Performance Analytics**: Track key business metrics and KPIs
- **Export Capabilities**: Export data for external analysis

### 🔧 **Store Management**
- **Multi-store Support**: Manage multiple store locations from one dashboard
- **Store Configuration**: Customize store settings, currencies, and languages
- **User Management**: Role-based access control and permissions
- **Store Analytics**: Individual store performance tracking
- **Branding Tools**: Customize store appearance and branding

### 🚀 **Advanced Features**
- **Real-time Notifications**: Instant updates on orders, inventory, and customer activity
- **API Integration**: RESTful API for third-party integrations
- **Webhook Support**: Real-time data synchronization with external systems
- **Multi-currency Support**: Support for 40+ global currencies
- **Responsive Dashboard**: Modern, intuitive admin interface

## 🛠️ Technology Stack

### Frontend
- **Next.js 14**: React framework with server-side rendering
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **React Query**: Data fetching and state management
- **NextAuth.js**: Authentication and authorization

### Backend
- **Next.js API Routes**: Server-side API endpoints
- **TypeORM**: Database ORM with PostgreSQL
- **JWT Authentication**: Secure user authentication
- **Google OAuth**: Social login integration
- **Sharp**: Image processing and optimization

### AI & Machine Learning
- **Custom Agent SDK**: TypeScript-based AI agent framework
- **OpenAI Integration**: GPT-powered conversation agents
- **Tool Calling**: AI agents with custom business logic
- **Conversation Management**: Structured AI-human interactions
- **Performance Monitoring**: AI agent metrics and optimization

### Database & Infrastructure
- **PostgreSQL**: Reliable relational database
- **TypeORM Migrations**: Database schema management
- **UUID v7**: Modern identifier system
- **Soft Deletes**: Data integrity and recovery
- **Audit Trails**: Complete change tracking

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- PostgreSQL 12+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/teno-store.git
   cd teno-store
   ```

2. **Set up the backend**
   ```bash
   cd backend
   npm install
   cp env.example .env.local
   # Configure your environment variables
   npm run dev
   ```

3. **Set up the frontend**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

4. **Set up the AI agent SDK**
   ```bash
   cd ai-agent
   npm install
   npm run build
   ```

5. **Configure your database**
   ```bash
   cd backend
   npm run db:migrate
   npm run db:seed
   ```

### Environment Configuration

Create `.env.local` files in both `backend/` and `frontend/` directories:

```bash
# Backend (.env.local)
DATABASE_URL=postgresql://username:password@localhost:5432/teno_store
JWT_SECRET=your-jwt-secret
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
OPENAI_API_KEY=your-openai-api-key

# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000
```

## 📱 Usage

### For Store Owners

1. **Create Your Store**
   - Sign up and create your first store
   - Configure store settings, currency, and language
   - Set up your product catalog

2. **Manage Inventory**
   - Add products with images and descriptions
   - Set pricing and manage stock levels
   - Organize products into categories

3. **Handle Orders**
   - AI agents automatically process customer orders
   - Monitor order status and fulfillment
   - Manage customer relationships

4. **Track Performance**
   - View sales analytics and reports
   - Monitor inventory levels
   - Track customer engagement

### For Customers

1. **Browse Stores**
   - Visit the public store directory
   - Choose your preferred language
   - Discover products from various stores

2. **Shop & Order**
   - Browse product catalogs
   - Place orders through natural conversation
   - Track order status in real-time

3. **Get Support**
   - Chat with AI agents for assistance
   - Get order updates and notifications
   - Access customer support

## 🔌 API Reference

Teno Store provides a comprehensive REST API for integration:

- **Authentication**: JWT-based authentication
- **Stores**: Store management endpoints
- **Products**: Product catalog management
- **Orders**: Order processing and management
- **Customers**: Customer relationship management
- **Conversations**: AI agent conversation management

Full API documentation is available at `/api/openapi.json` when running the backend.

## 🤖 AI Agent Capabilities

### Available Tools
- **Place Orders**: Process customer orders automatically
- **Update Customers**: Maintain accurate customer records
- **Check Order Status**: Provide real-time order updates
- **Search Customers**: Find customer information quickly
- **Update Orders**: Modify existing orders as needed

### Agent Types
- **Sales Agents**: Handle product inquiries and order processing
- **Support Agents**: Provide customer service and assistance
- **Information Agents**: Answer questions about products and policies

## 🌍 Internationalization

Teno Store supports multiple languages and regions:

- **Languages**: English, French, Spanish, German, Italian, Portuguese, Russian, Chinese, Japanese, Korean, Arabic, Hindi, Bengali, Punjabi, Urdu, Persian
- **Currencies**: 40+ global currencies including USD, EUR, GBP, CAD, AUD, JPY, CNY, and more
- **Localization**: Region-specific formatting and conventions

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Google OAuth**: Social login integration
- **Role-based Access Control**: Granular permission management
- **Data Encryption**: Secure data transmission and storage
- **Audit Logging**: Complete activity tracking
- **Input Validation**: Comprehensive input sanitization

## 📊 Performance & Monitoring

- **Real-time Metrics**: Live performance monitoring
- **AI Agent Analytics**: Track agent efficiency and costs
- **Database Optimization**: Optimized queries and indexing
- **Image Optimization**: Automatic image compression and optimization
- **Caching**: Intelligent caching for improved performance

## 🚧 Development Status

### ✅ **Production Ready**
- Core e-commerce functionality
- AI agent integration
- Multi-store management
- Customer management
- Order processing
- Product catalog
- Storefront system

### 🚧 **In Development**
- Advanced inventory tracking
- Sales analytics dashboard
- Purchase order management
- Supplier management
- Advanced reporting tools

### 📋 **Planned Features**
- Multi-channel selling
- Advanced shipping options
- Payment gateway integration
- Marketing automation
- Customer loyalty programs

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.tenostore.com](https://docs.tenostore.com)
- **Issues**: [GitHub Issues](https://github.com/your-org/teno-store/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/teno-store/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- AI powered by [OpenAI](https://openai.com/)
- Database powered by [PostgreSQL](https://www.postgresql.org/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)

---

**Teno Store** - Transforming e-commerce with AI-powered intelligence 🚀
