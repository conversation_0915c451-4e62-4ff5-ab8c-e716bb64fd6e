"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./src/pages/products/index.tsx":
/*!**************************************!*\
  !*** ./src/pages/products/index.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _components_EntityTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/EntityTable */ \"./src/components/EntityTable.tsx\");\n/* harmony import */ var _components_TopTaskBar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/TopTaskBar */ \"./src/components/TopTaskBar.tsx\");\n/* harmony import */ var _components_SideTaskBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/SideTaskBar */ \"./src/components/SideTaskBar.tsx\");\n/* harmony import */ var _components_ProductModals__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/ProductModals */ \"./src/components/ProductModals.tsx\");\n/* harmony import */ var _utils_hooks__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/hooks */ \"./src/utils/hooks.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const { currentStoreId } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const itemsPerPage = 20;\n    // Debug logging\n    console.log(\"ProductsPage render - currentStoreId:\", currentStoreId, \"user:\", user === null || user === void 0 ? void 0 : user.id);\n    // Keyboard shortcut for F1 to open add modal\n    (0,_utils_hooks__WEBPACK_IMPORTED_MODULE_10__.useAddEntityShortcut)(Boolean(currentStoreId) && !showAddModal, ()=>setShowAddModal(true));\n    // Fetch products\n    const fetchProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        if (!currentStoreId) return;\n        try {\n            setIsLoading(true);\n            setError(null);\n            console.log(\"Fetching products for store:\", currentStoreId, \"page:\", page);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_3__.productApi.getByStoreId(currentStoreId, {\n                page,\n                limit: itemsPerPage\n            });\n            console.log(\"Products response:\", response);\n            setProducts(response.data);\n            setCurrentPage(response.meta.page);\n            setTotalPages(response.meta.totalPages);\n            setTotalItems(response.meta.total);\n        } catch (err) {\n            console.error(\"Error fetching products:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch products\");\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        currentStoreId,\n        itemsPerPage\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentStoreId) {\n            fetchProducts(currentPage);\n        }\n    }, [\n        currentStoreId,\n        currentPage,\n        fetchProducts\n    ]);\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    const handleAddProduct = ()=>{\n        setShowAddModal(true);\n    };\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowEditModal(true);\n    };\n    const handleDeleteProduct = async (product)=>{\n        if (!confirm('Are you sure you want to delete \"'.concat(product.name, '\"?'))) return;\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_3__.productApi.delete(product.id);\n            // Refresh the current page\n            fetchProducts(currentPage);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to delete product\");\n        }\n    };\n    const handleCreateProduct = async (productData)=>{\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_3__.productApi.create({\n                ...productData,\n                storeId: currentStoreId,\n                userId: user.id\n            });\n            setShowAddModal(false);\n            // Refresh the current page\n            fetchProducts(currentPage);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to create product\");\n        }\n    };\n    const handleUpdateProduct = async (productData)=>{\n        if (!editingProduct) return;\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_3__.productApi.update(editingProduct.id, productData);\n            setShowEditModal(false);\n            setEditingProduct(null);\n            // Refresh the current page\n            fetchProducts(currentPage);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to update product\");\n        }\n    };\n    const formatPrice = (price, currency)=>{\n        const numericPrice = typeof price === \"string\" ? parseFloat(price) : price;\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: currency || \"USD\"\n        }).format(numericPrice);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const columns = [\n        {\n            key: \"name\",\n            header: \"Product Name\",\n            render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        row.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: row.imageUrl,\n                            alt: row.name,\n                            className: \"w-10 h-10 rounded-lg object-cover bg-slate-700\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium text-slate-100\",\n                                    children: row.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                row.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-slate-400 truncate max-w-xs\",\n                                    children: row.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"price\",\n            header: \"Price\",\n            render: (value, row)=>{\n                var _row_store;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"font-semibold text-emerald-400\",\n                    children: formatPrice(row.price, ((_row_store = row.store) === null || _row_store === void 0 ? void 0 : _row_store.currency) || \"USD\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"sku\",\n            header: \"SKU\",\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-slate-300 font-mono text-sm\",\n                    children: value || \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"store\",\n            header: \"Store\",\n            render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-slate-300\",\n                    children: row.store.name\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"createdAt\",\n            header: \"Created\",\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-slate-400 text-sm\",\n                    children: formatDate(value)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            header: \"Actions\",\n            render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-slate-300 hover:text-emerald-400 rounded-md p-2 border border-white/10 hover:bg-white/5\",\n                            title: \"Edit\",\n                            \"aria-label\": \"Edit\",\n                            onClick: ()=>handleEditProduct(row),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Edit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-slate-300 hover:text-red-400 rounded-md p-2 border border-white/10 hover:bg-white/5\",\n                            title: \"Delete\",\n                            \"aria-label\": \"Delete\",\n                            onClick: ()=>handleDeleteProduct(row),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Early returns for loading/error states\n    if (!currentStoreId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-slate-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TopTaskBar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SideTaskBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800 rounded-lg shadow-lg p-8 border border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-slate-100 mb-4\",\n                                            children: \"No Store Selected\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-400 mb-6\",\n                                            children: \"Please select a store to view products.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.href = \"/setup/store\",\n                                            className: \"bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors\",\n                                            children: \"Create Store\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Products - Teno Store\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Manage your store products\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-slate-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TopTaskBar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SideTaskBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-3xl font-extrabold tracking-tight text-slate-100\",\n                                                            children: \"Products\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-slate-400\",\n                                                            children: \"Manage your store inventory and product catalog\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleAddProduct,\n                                                    className: \"px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-xl font-medium shadow-lg shadow-emerald-500/25 transition-all duration-200\",\n                                                    children: \"+ Add Product\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 bg-red-500/10 border border-red-500/20 text-red-300 px-4 py-3 rounded-md\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EntityTable__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        columns: columns,\n                                        data: products,\n                                        isLoading: isLoading,\n                                        loadingText: \"Loading products...\",\n                                        noDataText: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-slate-400 mb-2\",\n                                                    children: \"No products found\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleAddProduct,\n                                                    className: \"text-emerald-400 hover:text-emerald-300 underline\",\n                                                    children: \"Add your first product\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        pagination: {\n                                            currentPage,\n                                            totalPages,\n                                            onPageChange: handlePageChange,\n                                            totalItems,\n                                            itemsPerPage\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductModals__WEBPACK_IMPORTED_MODULE_9__.ProductCreateModal, {\n                        isOpen: showAddModal,\n                        onClose: ()=>setShowAddModal(false),\n                        onSubmit: handleCreateProduct,\n                        storeId: currentStoreId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductModals__WEBPACK_IMPORTED_MODULE_9__.ProductUpdateModal, {\n                        isOpen: showEditModal,\n                        onClose: ()=>{\n                            setShowEditModal(false);\n                            setEditingProduct(null);\n                        },\n                        onSubmit: handleUpdateProduct,\n                        product: editingProduct\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\products\\\\index.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProductsPage, \"RJKH7X32hFWaLJ+sDP2qSMvH2tA=\", false, function() {\n    return [\n        _context_StoreContext__WEBPACK_IMPORTED_MODULE_4__.useStore,\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _utils_hooks__WEBPACK_IMPORTED_MODULE_10__.useAddEntityShortcut\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/products/index.tsx\n"));

/***/ })

});