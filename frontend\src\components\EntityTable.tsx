import React, { useEffect, useState } from 'react';

export interface EntityTableColumn<T> {
  key: keyof T | string;
  header: React.ReactNode;
  render?: (value: any, row: T, rowIndex: number) => React.ReactNode;
  cellClassName?: string;
  headerClassName?: string;
  cellStyle?: React.CSSProperties;
  headerStyle?: React.CSSProperties;
}

export interface EntityTableProps<T extends object = Record<string, any>> {
  columns: EntityTableColumn<T>[];
  data: T[];
  noDataText?: React.ReactNode;
  className?: string;
  containerClassName?: string;
  isLoading?: boolean;
  loadingText?: string;
  tableBodyHeight?: string;
  pagination?: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    totalItems: number;
    itemsPerPage: number;
  };
  getRowClassName?: (row: T, rowIndex: number) => string;
}

const CircularSpinner: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => {
  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      <div className="relative">
        <div className="w-12 h-12 border-4 border-slate-600/40 rounded-full"></div>
        <div className="absolute top-0 left-0 w-12 h-12 border-4 border-emerald-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
      <div className="text-slate-400 text-sm font-medium">{text}</div>
    </div>
  );
};

const Pagination: React.FC<{
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems: number;
  itemsPerPage: number;
  isLoading?: boolean;
}> = ({ currentPage, totalPages, onPageChange, totalItems, itemsPerPage, isLoading = false }) => {
  const hasPrev = currentPage > 1;
  const hasNext = currentPage < totalPages;

  return (
    <div className="mt-8 flex items-center justify-between bg-slate-800 rounded-lg shadow-lg px-6 py-4 border border-white/10">
      <button
        className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={!hasPrev || isLoading}
        onClick={() => onPageChange(Math.max(1, currentPage - 1))}
      >
        ← Previous
      </button>
      <div className="text-sm text-slate-300 text-center">
        <div className="font-medium">Page {currentPage} of {totalPages}</div>
        <div className="text-xs text-slate-400">{totalItems} total items</div>
      </div>
      <button
        className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={!hasNext || isLoading}
        onClick={() => onPageChange(currentPage + 1)}
      >
        Next →
      </button>
    </div>
  );
};

function EntityTable<T extends object = Record<string, any>>({
  columns,
  data,
  noDataText = 'No items found.',
  className = '',
  containerClassName = '',
  isLoading = false,
  loadingText = 'Loading...',
  tableBodyHeight,
  pagination,
  getRowClassName,
}: EntityTableProps<T>) {
  const [showTable, setShowTable] = useState(false);
  const [dataKey, setDataKey] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    setIsAnimating(true);
    setShowTable(true);
    const timer = setTimeout(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setIsAnimating(false);
        setIsTransitioning(false);
      }, 200);
    }, 800);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!isLoading && data.length > 0) {
      setIsAnimating(true);
      setDataKey((prev) => prev + 1);
      const timer = setTimeout(() => {
        setIsTransitioning(true);
        setTimeout(() => {
          setIsAnimating(false);
          setIsTransitioning(false);
        }, 200);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [data.length, isLoading]);

  useEffect(() => {
    if (isLoading) {
      setIsAnimating(true);
    } else {
      const timer = setTimeout(() => {
        setIsTransitioning(true);
        setTimeout(() => {
          setIsAnimating(false);
          setIsTransitioning(false);
        }, 200);
      }, 600);
      return () => clearTimeout(timer);
    }
  }, [isLoading]);

  const containerClasses = `bg-slate-800 rounded-xl shadow-lg border border-white/10 w-full table-container ${containerClassName} ${
    isLoading ? 'loading' : 'fade-in'
  } ${showTable ? 'table-entrance' : ''}`;

  return (
    <div
      className={`overflow-x-auto py-2 max-w-7xl mx-auto w-full ${isAnimating || isTransitioning ? 'animating' : ''}`}
      style={{
        overflowX: isAnimating || isTransitioning ? 'hidden' : undefined,
        overflowY: isAnimating || isTransitioning ? 'hidden' : undefined,
      }}
    >
      <div className={containerClasses} style={{ overflow: 'hidden' }}>
        <div className={tableBodyHeight ? 'overflow-y-auto' : ''} style={tableBodyHeight ? { maxHeight: tableBodyHeight } : undefined}>
          <table className={`min-w-full divide-y divide-white/10 ${className}`}>
            <thead className="table-header bg-white/5">
              <tr>
                {columns.map((col) => (
                  <th
                    key={col.key as string}
                    className={`text-left align-middle px-6 py-3 text-xs font-medium text-slate-300 uppercase tracking-wider ${col.headerClassName || ''}`}
                    style={col.headerStyle}
                  >
                    {col.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {isLoading ? (
                <tr>
                  <td colSpan={columns.length} className="p-0">
                    <div
                      className="flex items-center justify-center"
                      style={tableBodyHeight ? { height: tableBodyHeight, minHeight: tableBodyHeight } : { height: '400px' }}
                    >
                      <CircularSpinner text={loadingText} />
                    </div>
                  </td>
                </tr>
              ) : data.length > 0 ? (
                data.map((row, rowIndex) => (
                  <tr key={`${dataKey}-${rowIndex}`} className={`hover:bg-white/5 table-row ${getRowClassName ? getRowClassName(row, rowIndex) : ''}`}>
                    {columns.map((col) => (
                      <td
                        key={col.key as string}
                        className={`px-6 py-4 whitespace-nowrap text-sm text-slate-100 ${col.cellClassName || ''}`}
                        style={col.cellStyle}
                      >
                        {col.render ? col.render((row as any)[col.key as keyof T], row, rowIndex) : String((row as any)[col.key as keyof T] ?? '')}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={columns.length} className="p-0">
                    <div
                      className="flex items-center justify-center text-slate-400"
                      style={tableBodyHeight ? { height: tableBodyHeight, minHeight: tableBodyHeight } : { height: '400px' }}
                    >
                      {noDataText}
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {pagination && (
        <Pagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.onPageChange}
          totalItems={pagination.totalItems}
          itemsPerPage={pagination.itemsPerPage}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}

export default EntityTable;


