#!/usr/bin/env tsx
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const dotenv_1 = require("dotenv");
const path_1 = require("path");
(0, dotenv_1.config)();
async function runMigrations() {
    const dataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        username: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'teno_store_db',
        synchronize: false,
        logging: true,
        entities: [(0, path_1.join)(__dirname, '../**/*.entity{.ts,.js}')],
        migrations: [(0, path_1.join)(__dirname, '../migrations/*{.ts,.js}')],
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    });
    try {
        await dataSource.initialize();
        console.log('✅ Database connection established');
        const pendingMigrations = await dataSource.showMigrations();
        if (pendingMigrations) {
            console.log('🔄 Running pending migrations...');
            await dataSource.runMigrations();
            console.log('✅ All migrations completed successfully');
        }
        else {
            console.log('ℹ️  No pending migrations found');
        }
        await dataSource.destroy();
        console.log('✅ Database connection closed');
    }
    catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
}
runMigrations();
//# sourceMappingURL=migrate.js.map