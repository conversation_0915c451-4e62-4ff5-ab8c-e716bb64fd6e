/**
 * Agent SDK for building LLM-powered agents with tools.
 * 
 * This package provides a simple framework for creating and running LLM-powered agents
 * with tool-calling capabilities.
 */

export { Agent } from './agent';
export { LLMProvider, setLlmProvider, setLlmProviderFromEnvironmentVariables } from './llm-provider';
export { LlmApi } from './llm-api';
export { functionTool, createTool, getToolSpec, isToolFunction } from './tools';
export { addToolMetadata, buildParametersSchema, typeToJsonSchema } from './schema';

// Export pre-built agents
// export * from './agents'; // Removed: agents module no longer present

// Export types
export type {
  ToolSpec,
  JsonSchema,
  Message,
  ToolCall,
  ToolOutput,
  ToolFunction
} from './types';

export type { ToolConfig } from './tools';
export type { LLMConfig } from './llm-provider';
