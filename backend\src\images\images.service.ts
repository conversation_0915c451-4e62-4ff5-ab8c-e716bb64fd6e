import { Injectable } from '@nestjs/common';
import * as sharp from 'sharp';
import * as path from 'path';
import * as fs from 'fs/promises';

@Injectable()
export class ImagesService {
  async processImage(file: any): Promise<string> {
    const filename = `${Date.now()}-${file.originalname}`;
    const uploadPath = path.join(process.cwd(), 'uploads', filename);
    
    // Ensure uploads directory exists
    await fs.mkdir(path.dirname(uploadPath), { recursive: true });
    
    // Process image with sharp
    await sharp(file.buffer)
      .resize(800, 800, { fit: 'inside', withoutEnlargement: true })
      .jpeg({ quality: 80 })
      .toFile(uploadPath);
    
    return filename;
  }

  async getImage(filename: string): Promise<Buffer> {
    const imagePath = path.join(process.cwd(), 'uploads', filename);
    
    try {
      return await fs.readFile(imagePath);
    } catch (error) {
      throw new Error('Image not found');
    }
  }

  async deleteImage(filename: string): Promise<void> {
    const imagePath = path.join(process.cwd(), 'uploads', filename);
    
    try {
      await fs.unlink(imagePath);
    } catch (error) {
      // Ignore error if file doesn't exist
    }
  }
}
