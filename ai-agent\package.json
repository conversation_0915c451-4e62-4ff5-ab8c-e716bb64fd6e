{"name": "dido-agent-sdk", "version": "1.0.0", "description": "Agent SDK for building LLM-powered agents with tools", "main": "dist/index.js", "types": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "examples": "ts-node examples/basic-usage.ts", "chat": "ts-node examples/chat-loop.ts", "clean": "rm -rf dist", "prepublishOnly": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:llm": "ts-node scripts/test_llm_api.ts"}, "keywords": ["llm", "agents", "openai", "tools", "ai"], "author": "Dido Distribution", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cheerio": "^1.0.0", "dotenv": "^16.4.0", "openai": "^4.68.0", "zod": "^3.22.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.11.0", "jest": "^29.7.0", "ts-jest": "^29.4.1", "ts-node": "^10.9.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}