import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    // Password-based authentication is not supported in this OAuth-based system
    // Users authenticate through Google OAuth instead
    return null;
  }

  async login(user: any) {
    const payload = { email: user.email, sub: user.id };
    console.log('[AuthService] Creating JWT payload:', payload);
    console.log('[AuthService] User object in login:', user);
    
    const token = this.jwtService.sign(payload);
    console.log('[AuthService] JWT token created, length:', token.length);
    
    return {
      access_token: token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.image,
      },
    };
  }

  async googleLogin(profile: any) {
    console.log('[AuthService] Google login with profile:', profile);
    
    // Find user by email since we don't have googleId field
    let user = await this.usersService.findByEmail(profile.email);
    console.log('[AuthService] Existing user found:', user);
    
    if (!user) {
      // Create new user if doesn't exist
      console.log('[AuthService] Creating new user for Google login');
      user = await this.usersService.create({
        email: profile.email,
        name: profile.name,
        image: profile.picture,
      });
      console.log('[AuthService] New user created:', user);
    } else {
      // Update existing user's name and image if they've changed
      if (user.name !== profile.name || user.image !== profile.picture) {
        console.log('[AuthService] Updating existing user');
        user = await this.usersService.update(user.id, {
          name: profile.name,
          image: profile.picture,
        });
        console.log('[AuthService] User updated:', user);
      }
    }

    console.log('[AuthService] Final user object for login:', user);
    return this.login(user);
  }

  async getProfile(userId: string) {
    console.log('[AuthService] getProfile called with userId:', userId);
    const user = await this.usersService.findOne(userId);
    console.log('[AuthService] getProfile result:', user);
    return user;
  }

  async devLogin(email: string, name?: string) {
    console.log('[AuthService] Dev login for email:', email);
    
    // Check if user exists
    let user = await this.usersService.findByEmail(email);
    
    if (!user) {
      // Create new user if doesn't exist
      console.log('[AuthService] Creating new user for dev login');
      user = await this.usersService.create({
        email,
        name: name || email.split('@')[0],
        image: null,
      });
    }
    
    console.log('[AuthService] Dev login successful for user:', user.id);
    return this.login(user);
  }
}
