.fade-in { opacity: 1; animation: fadeInBounce 0.8s cubic-bezier(0.175,0.885,0.32,1.275) forwards; }
.fade-in.loading { opacity: 0.7; animation: loadingPulse 2s ease-in-out infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4,0,0.6,1) infinite; }
@keyframes pulse { 0%,100%{opacity:1} 50%{opacity:.5} }
@keyframes fadeInBounce { 0%{opacity:0;transform:translateY(5px) scale(.99)} 60%{opacity:.8;transform:translateY(-1px) scale(1.005)} 100%{opacity:1;transform:translateY(0) scale(1)} }
@keyframes loadingPulse { 0%,100%{opacity:.7} 50%{opacity:.9} }
.table-container { transition: all .3s cubic-bezier(0.4,0,0.2,1); overflow:hidden; contain:layout style; position:relative; width:100%; max-width:100%; box-sizing:border-box; }
.table-container.loading { opacity:.8; animation: containerBreathing 3s ease-in-out infinite; }
@keyframes containerBreathing { 0%,100%{ box-shadow:0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -2px rgba(0,0,0,.05) } 50%{ box-shadow:0 12px 18px -3px rgba(0,0,0,.1),0 6px 8px -2px rgba(0,0,0,.05) } }
.skeleton-row { position:relative; overflow:hidden; animation:skeletonFloat 2s ease-in-out infinite }
.skeleton-row::before { content:''; position:absolute; top:0; left:-100%; width:100%; height:100%; background:linear-gradient(90deg,transparent 0%, rgba(255,255,255,.4) 50%, transparent 100%); animation:waveShimmer 2s ease-in-out infinite }
@keyframes waveShimmer { 0%{left:-100%} 100%{left:100%} }
@keyframes skeletonFloat { 0%,100%{transform:translateY(0)} 50%{transform:translateY(-1px)} }
.table-row { opacity:0; animation:staggeredFadeIn .6s ease-out forwards }
.table-row:nth-child(1){animation-delay:.05s}
.table-row:nth-child(2){animation-delay:.1s}
.table-row:nth-child(3){animation-delay:.15s}
.table-row:nth-child(4){animation-delay:.2s}
.table-row:nth-child(5){animation-delay:.25s}
.table-row:nth-child(6){animation-delay:.3s}
.table-row:nth-child(7){animation-delay:.35s}
.table-row:nth-child(8){animation-delay:.4s}
.table-row:nth-child(9){animation-delay:.45s}
.table-row:nth-child(10){animation-delay:.5s}
.table-row:nth-child(n+11){animation-delay:.55s}
@keyframes staggeredFadeIn { from{opacity:0; transform:translateX(-5px)} to{opacity:1; transform:translateX(0)} }
.table-row { transition:none; position:relative }
.loading tbody tr:hover { background-color:transparent !important; box-shadow:none !important }
tbody tr { transition: all .3s cubic-bezier(0.4,0,0.2,1) }
.table-header { animation: slideDown .6s ease-out }
@keyframes slideDown { from{opacity:0; transform:translateY(-5px)} to{opacity:1; transform:translateY(0)} }
.action-button { transition: all .2s cubic-bezier(0.4,0,0.2,1); position:relative; overflow:hidden }
.action-button:hover { transform: scale(1.05) }
.action-button::before { content:''; position:absolute; top:50%; left:50%; width:0; height:0; background: rgba(255,255,255,.3); border-radius:50%; transform: translate(-50%,-50%); transition: all .3s ease }
.action-button:hover::before { width:100%; height:100% }
.cell-loading { display:inline-block; width:20px; height:20px; border:2px solid rgba(243,243,243,1); border-top:2px solid #10b981; border-radius:50%; animation: spin 1s linear infinite }
@keyframes spin { 0%{transform:rotate(0)} 100%{transform:rotate(360deg)} }
.table-entrance { animation: tableSlideUp .8s cubic-bezier(0.175,0.885,0.32,1.275) }
@keyframes tableSlideUp { from{opacity:0; transform:translateY(8px) scale(.99)} to{opacity:1; transform:translateY(0) scale(1)} }
.overflow-x-auto { position:relative; scrollbar-width:thin }
.overflow-x-auto:not(.animating) { overflow-x:auto; overflow-y:hidden; transition: overflow .3s ease-out }
.overflow-x-auto { transition: all .3s ease-out }
.overflow-x-auto.animating { overflow:hidden !important; overflow-x:hidden !important; overflow-y:hidden !important }
.table-container.loading,.table-container.fade-in { overflow:hidden !important; contain: layout style }
.table-entrance { overflow:hidden !important; contain: layout }
.animating { scrollbar-width:none !important; -ms-overflow-style:none !important }
.animating::-webkit-scrollbar { display:none !important }
.animating * { overflow:visible !important }
.animating .table-container,.animating .overflow-x-auto { overflow:hidden !important; max-width:100% !important; max-height:100% !important }


