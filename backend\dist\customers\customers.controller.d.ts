import { CustomersService } from './customers.service';
import { Customer } from './customer.entity';
import { PaginatedResponse } from '../types/pagination';
export declare class CustomersController {
    private readonly customersService;
    constructor(customersService: CustomersService);
    create(createCustomerDto: Partial<Customer> & {
        userId?: string | number;
    }): Promise<Customer>;
    findAll(page?: string, limit?: string): Promise<PaginatedResponse<Customer>>;
    findByStoreId(storeId: string, page?: string, limit?: string): Promise<PaginatedResponse<Customer>>;
    findOne(id: string): Promise<Customer>;
    update(id: string, updateCustomerDto: Partial<Customer>): Promise<Customer>;
    remove(id: string): Promise<void>;
}
