import { useRouter } from 'next/router';
import { useAuth } from '../context/AuthContext';

export default function Index() {
  const router = useRouter();
  const { user } = useAuth();

  const goToStorefront = () => {
    router.push('/storefront');
  };

  const goToDashboard = () => {
    router.push('/dashboard');
  };

  const goToLogin = () => {
    router.push('/login');
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white flex items-center justify-center">
      <div className="max-w-4xl mx-auto px-4 text-center">
        <h1 className="text-5xl font-bold mb-6">Teno Store</h1>
        <p className="text-xl text-slate-300 mb-12">Your complete e-commerce solution</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto">
          {/* Public Storefront */}
          <div className="bg-slate-800 rounded-lg p-8 hover:bg-slate-700 transition-colors">
            <div className="mb-4">
              <svg className="w-16 h-16 text-blue-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
            </div>
            <h2 className="text-2xl font-semibold mb-4">Shop Now</h2>
            <p className="text-slate-300 mb-6">Browse stores and products without creating an account</p>
            <button
              onClick={goToStorefront}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg transition-colors"
            >
              Browse Stores
            </button>
          </div>

          {/* Store Management */}
          <div className="bg-slate-800 rounded-lg p-8 hover:bg-slate-700 transition-colors">
            <div className="mb-4">
              <svg className="w-16 h-16 text-green-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H9m3 0V9" />
              </svg>
            </div>
            <h2 className="text-2xl font-semibold mb-4">Manage Store</h2>
            <p className="text-slate-300 mb-6">
              {user ? 'Access your store management dashboard' : 'Sign in to manage your store'}
            </p>
            {user ? (
              <button
                onClick={goToDashboard}
                className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg transition-colors"
              >
                Go to Dashboard
              </button>
            ) : (
              <button
                onClick={goToLogin}
                className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg transition-colors"
              >
                Sign In
              </button>
            )}
          </div>
        </div>

        <div className="mt-12 text-slate-400">
          <p>Discover products, manage your store, and grow your business</p>
        </div>
      </div>
    </div>
  );
}
