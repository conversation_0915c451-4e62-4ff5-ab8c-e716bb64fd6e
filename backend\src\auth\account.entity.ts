import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';

@Entity('accounts')
export class Account {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar' })
  userId: string;

  @Column({ type: 'varchar' })
  type: string;

  @Column({ type: 'varchar' })
  provider: string;

  @Column({ type: 'varchar' })
  providerAccountId: string;

  @Column({ type: 'text', nullable: true })
  refresh_token: string;

  @Column({ type: 'text', nullable: true })
  access_token: string;

  @Column({ type: 'int', nullable: true })
  expires_at: number;

  @Column({ type: 'varchar', nullable: true })
  token_type: string;

  @Column({ type: 'varchar', nullable: true })
  scope: string;

  @Column({ type: 'text', nullable: true })
  id_token: string;

  @Column({ type: 'varchar', nullable: true })
  session_state: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.accounts)
  user: any;
}
