"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bigIntTransformer = bigIntTransformer;
exports.setupGlobalBigIntHandler = setupGlobalBigIntHandler;
exports.handleTypeORMResult = handleTypeORMResult;
exports.safeJsonStringify = safeJsonStringify;
exports.containsBigInt = containsBigInt;
exports.safeClone = safeClone;
function bigIntTransformer(obj) {
    if (obj === null || obj === undefined) {
        return obj;
    }
    if (typeof obj === 'bigint') {
        return obj.toString();
    }
    if (Array.isArray(obj)) {
        return obj.map(bigIntTransformer);
    }
    if (typeof obj === 'object') {
        const result = {};
        for (const [key, value] of Object.entries(obj)) {
            result[key] = bigIntTransformer(value);
        }
        return result;
    }
    return obj;
}
function setupGlobalBigIntHandler() {
    const originalStringify = JSON.stringify;
    JSON.stringify = function (value, replacer, space) {
        const bigIntReplacer = (key, value) => {
            if (typeof value === 'bigint') {
                return value.toString();
            }
            return value;
        };
        const finalReplacer = replacer ?
            (key, value) => {
                const bigIntResult = bigIntReplacer(key, value);
                return replacer(key, bigIntResult);
            } :
            bigIntReplacer;
        return originalStringify(value, finalReplacer, space);
    };
    console.log('Global BigInt handler setup complete');
}
function handleTypeORMResult(result) {
    try {
        return bigIntTransformer(result);
    }
    catch (error) {
        console.warn('Error handling TypeORM result:', error);
        return result;
    }
}
function safeJsonStringify(obj, space) {
    try {
        return JSON.stringify(obj, (key, value) => {
            if (typeof value === 'bigint') {
                return value.toString();
            }
            return value;
        }, space);
    }
    catch (error) {
        console.error('Error in safe JSON stringify:', error);
        try {
            const transformed = bigIntTransformer(obj);
            return JSON.stringify(transformed, null, space);
        }
        catch (fallbackError) {
            console.error('Fallback JSON stringify also failed:', fallbackError);
            throw new Error(`Failed to serialize object: ${error.message}`);
        }
    }
}
function containsBigInt(obj) {
    if (obj === null || obj === undefined) {
        return false;
    }
    if (typeof obj === 'bigint') {
        return true;
    }
    if (Array.isArray(obj)) {
        return obj.some(containsBigInt);
    }
    if (typeof obj === 'object') {
        return Object.values(obj).some(containsBigInt);
    }
    return false;
}
function safeClone(obj) {
    if (obj === null || obj === undefined) {
        return obj;
    }
    if (typeof obj === 'bigint') {
        return obj.toString();
    }
    if (Array.isArray(obj)) {
        return obj.map(safeClone);
    }
    if (typeof obj === 'object') {
        const cloned = {};
        for (const [key, value] of Object.entries(obj)) {
            cloned[key] = safeClone(value);
        }
        return cloned;
    }
    return obj;
}
//# sourceMappingURL=bigint-handler.js.map