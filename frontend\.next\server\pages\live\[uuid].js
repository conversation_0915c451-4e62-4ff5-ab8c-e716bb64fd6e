/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/live/[uuid]";
exports.ids = ["pages/live/[uuid]"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flive%2F%5Buuid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Clive%5C%5Buuid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flive%2F%5Buuid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Clive%5C%5Buuid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\live\\[uuid].tsx */ \"./src/pages/live/[uuid].tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/live/[uuid]\",\n        pathname: \"/live/[uuid]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_live_uuid_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flive%2F%5Buuid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Clive%5C%5Buuid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/MessageFormatter.tsx":
/*!*********************************************!*\
  !*** ./src/components/MessageFormatter.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Inline spoiler component for Telegram-like spoilers: ||hidden||\nconst Spoiler = ({ children, styles })=>{\n    const [revealed, setRevealed] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        onClick: ()=>setRevealed(true),\n        className: revealed ? styles.spoilerReveal : styles.spoiler,\n        role: \"button\",\n        tabIndex: 0,\n        onKeyDown: (e)=>{\n            if (e.key === \"Enter\" || e.key === \" \") setRevealed(true);\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\nfunction renderInline(text, keyPrefix, styles) {\n    const nodes = [];\n    const processText = (input, depth = 0)=>{\n        if (depth > 10) return [\n            input\n        ];\n        const result = [];\n        // Images first: ![alt](url)\n        const imageRegex = /(!\\[[^\\]]*\\]\\([^\\)]+\\))/g;\n        const imageParts = input.split(imageRegex);\n        if (imageParts.length > 1) {\n            imageParts.forEach((imgPart, imgIndex)=>{\n                const imgMatch = imgPart.match(/^!\\[([^\\]]*)\\]\\(([^\\)]+)\\)$/);\n                if (imgMatch) {\n                    const [, alt, src] = imgMatch;\n                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: src,\n                        alt: alt,\n                        className: styles.image\n                    }, `${keyPrefix}-img-${depth}-${imgIndex}`, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 23\n                    }, this));\n                } else {\n                    result.push(...processText(imgPart, depth + 1));\n                }\n            });\n            return result;\n        }\n        // Markdown links [text](url)\n        const linkRegex = /(\\[[^\\]]+\\]\\([^\\)]+\\))/g;\n        const linkParts = input.split(linkRegex);\n        linkParts.forEach((part, i)=>{\n            const linkMatch = part.match(/^\\[([^\\]]+)\\]\\(([^\\)]+)\\)$/);\n            if (linkMatch) {\n                const [, linkText, href] = linkMatch;\n                result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: href,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: styles.link,\n                    children: linkText\n                }, `${keyPrefix}-link-${depth}-${i}`, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this));\n                return;\n            }\n            // Inline code `code`\n            const codeRegex = /(`[^`\\n]+`)/g;\n            const codeParts = part.split(codeRegex);\n            codeParts.forEach((codePart, j)=>{\n                const codeMatch = codePart.match(/^`([^`\\n]+)`$/);\n                if (codeMatch) {\n                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: styles.inlineCode,\n                        children: codeMatch[1]\n                    }, `${keyPrefix}-code-${depth}-${i}-${j}`, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, this));\n                    return;\n                }\n                // Strikethrough ~~text~~\n                const strikeRegex = /(~~[^~]+?~~)/g;\n                const strikeParts = codePart.split(strikeRegex);\n                strikeParts.forEach((strikePart, k)=>{\n                    const strikeMatch = strikePart.match(/^~~([^~]+?)~~$/);\n                    if (strikeMatch) {\n                        result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: styles.strikethrough,\n                            children: strikeMatch[1]\n                        }, `${keyPrefix}-strike-${depth}-${i}-${j}-${k}`, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 15\n                        }, this));\n                        return;\n                    }\n                    // Underline __text__\n                    const underlineRegex = /(__[^_\\n]+?__)/g;\n                    const underlineParts = strikePart.split(underlineRegex);\n                    underlineParts.forEach((underlinePart, u)=>{\n                        const underlineMatch = underlinePart.match(/^__([^_\\n]+?)__$/);\n                        if (underlineMatch) {\n                            result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: styles.underline,\n                                children: underlineMatch[1]\n                            }, `${keyPrefix}-underline-${depth}-${i}-${j}-${k}-${u}`, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 17\n                            }, this));\n                            return;\n                        }\n                        // Bold **text**\n                        const boldRegex = /(\\*\\*[^*\\n]+?\\*\\*)/g;\n                        const boldParts = underlinePart.split(boldRegex);\n                        boldParts.forEach((boldPart, l)=>{\n                            const boldMatch = boldPart.match(/^\\*\\*([^*\\n]+?)\\*\\*$/);\n                            if (boldMatch) {\n                                result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"font-semibold\",\n                                    children: boldMatch[1]\n                                }, `${keyPrefix}-bold-${depth}-${i}-${j}-${k}-${l}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 29\n                                }, this));\n                                return;\n                            }\n                            // Italic *text* or _text_\n                            const italicRegex = /(\\*[^*\\n]+?\\*|_[^_\\n]+?_)/g;\n                            const italicParts = boldPart.split(italicRegex);\n                            italicParts.forEach((italicPart, m)=>{\n                                const italicMatch = italicPart.match(/^\\*([^*\\n]+?)\\*$/) || italicPart.match(/^_([^_\\n]+?)_$/);\n                                if (italicMatch) {\n                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                        className: \"italic opacity-90\",\n                                        children: italicMatch[1]\n                                    }, `${keyPrefix}-italic-${depth}-${i}-${j}-${k}-${l}-${m}`, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 31\n                                    }, this));\n                                    return;\n                                }\n                                // Plain text with autolinks/mentions/hashtags/commands/spoilers and preserved line breaks\n                                if (italicPart) {\n                                    const lines = italicPart.split(\"\\n\");\n                                    lines.forEach((line, n)=>{\n                                        if (n > 0) result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, `${keyPrefix}-br-${depth}-${i}-${j}-${k}-${l}-${m}-${n}`, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 44\n                                        }, this));\n                                        if (line) {\n                                            const tokenRegex = /(https?:\\/\\/[\\w.-]+(?:\\/[\\w\\-.~:/?#[\\]@!$&'()*+,;=%]*)?|\\b[\\w.+-]+@[\\w.-]+\\.[A-Za-z]{2,}\\b|\\B@[A-Za-z0-9_]{3,}|\\B#[A-Za-z0-9_]+|(?<=\\s|^)[\\/][A-Za-z0-9_]+\\b|\\|\\|[^|\\n]+\\|\\|)/g;\n                                            const tokens = line.split(tokenRegex);\n                                            tokens.forEach((tok, t)=>{\n                                                if (!tok) return;\n                                                if (/^https?:\\/\\//.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: tok,\n                                                        className: styles.link,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        children: tok\n                                                    }, `${keyPrefix}-auto-url-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 39\n                                                    }, this));\n                                                    return;\n                                                }\n                                                if (/^[\\w.+-]+@[\\w.-]+\\.[A-Za-z]{2,}$/.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: `mailto:${tok}`,\n                                                        className: styles.link,\n                                                        children: tok\n                                                    }, `${keyPrefix}-auto-email-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 39\n                                                    }, this));\n                                                    return;\n                                                }\n                                                if (/^@[A-Za-z0-9_]{3,}$/.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: styles.mention,\n                                                        children: tok\n                                                    }, `${keyPrefix}-mention-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 39\n                                                    }, this));\n                                                    return;\n                                                }\n                                                if (/^#[A-Za-z0-9_]+$/.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: styles.hashtag,\n                                                        children: tok\n                                                    }, `${keyPrefix}-hashtag-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 39\n                                                    }, this));\n                                                    return;\n                                                }\n                                                if (/^\\/[A-Za-z0-9_]+$/.test(tok)) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: styles.command,\n                                                        children: tok\n                                                    }, `${keyPrefix}-command-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 39\n                                                    }, this));\n                                                    return;\n                                                }\n                                                const spoilerMatch = tok.match(/^\\|\\|([^|\\n]+)\\|\\|$/);\n                                                if (spoilerMatch) {\n                                                    result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Spoiler, {\n                                                        styles: styles,\n                                                        children: spoilerMatch[1]\n                                                    }, `${keyPrefix}-spoiler-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 29\n                                                    }, this));\n                                                    return;\n                                                }\n                                                result.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: tok\n                                                }, `${keyPrefix}-text-${depth}-${i}-${j}-${k}-${l}-${m}-${n}-${t}`, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 37\n                                                }, this));\n                                            });\n                                        }\n                                    });\n                                }\n                            });\n                        });\n                    });\n                });\n            });\n        });\n        return result;\n    };\n    return processText(text);\n}\n// Enhanced block rendering with better AI text support\nfunction renderBlocks(text, styles) {\n    const lines = text.split(/\\r?\\n/);\n    const blocks = [];\n    let i = 0;\n    while(i < lines.length){\n        const line = lines[i];\n        // Skip extra blank lines but preserve paragraph separation\n        if (!line.trim()) {\n            i++;\n            continue;\n        }\n        const trimmed = line.trim();\n        // Horizontal rule --- or ***\n        if (/^(-{3,}|\\*{3,}|_{3,})$/.test(trimmed)) {\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                className: styles.hr\n            }, `hr-${i}`, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 223,\n                columnNumber: 19\n            }, this));\n            i++;\n            continue;\n        }\n        // Code block with language detection ```lang\n        if (/^```/.test(trimmed)) {\n            const langMatch = trimmed.match(/^```(\\w+)?/);\n            const language = langMatch?.[1] || \"\";\n            const codeLines = [];\n            i++;\n            while(i < lines.length && !/^```/.test(lines[i].trim())){\n                codeLines.push(lines[i]);\n                i++;\n            }\n            // Skip closing ``` if present\n            if (i < lines.length && /^```/.test(lines[i].trim())) i++;\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: styles.codeBlockWrapper,\n                children: [\n                    language && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: styles.codeBlockLanguage,\n                        children: language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: styles.codeBlockPre,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            children: codeLines.join(\"\\n\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, `code-${i}`, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Image-only line\n        const imageLineMatch = trimmed.match(/^!\\[([^\\]]*)\\]\\(([^\\)]+)\\)$/);\n        if (imageLineMatch) {\n            const [, alt, src] = imageLineMatch;\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: src,\n                    alt: alt,\n                    className: styles.image\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 11\n                }, this)\n            }, `img-${i}`, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this));\n            i++;\n            continue;\n        }\n        // GitHub-style tables\n        if (/^\\|?.*\\|.*$/.test(trimmed) && i + 1 < lines.length && /^\\|?\\s*:?[-]{3,}.*\\|.*$/.test(lines[i + 1].trim())) {\n            const headerCells = trimmed.replace(/^\\||\\|$/g, \"\").split(\"|\").map((c)=>c.trim());\n            i += 2; // skip separator\n            const bodyRows = [];\n            while(i < lines.length && /\\|/.test(lines[i])){\n                const rowCells = lines[i].replace(/^\\||\\|$/g, \"\").split(\"|\").map((c)=>c.trim());\n                bodyRows.push(rowCells);\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: styles.table,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: headerCells.map((cell, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: styles.tableHeader,\n                                    children: cell\n                                }, `th-${idx}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: bodyRows.map((row, r)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: row.map((cell, c)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: styles.tableCell,\n                                        children: cell\n                                    }, `td-${r}-${c}`, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 19\n                                    }, this))\n                            }, `tr-${r}`, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, `table-${i}`, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Blockquote > text\n        if (/^>\\s*/.test(trimmed)) {\n            const quoteLines = [];\n            while(i < lines.length && /^>\\s*/.test(lines[i].trim())){\n                const quoteLine = lines[i].replace(/^>\\s*/, \"\");\n                quoteLines.push(quoteLine);\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                className: styles.blockquote,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: styles.blockquoteBar\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: renderBlocks(quoteLines.join(\"\\n\"), styles)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, `quote-${i}`, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Headings #, ##, ###, ####, #####, ######\n        const heading = trimmed.match(/^(#{1,6})\\s+(.*)$/);\n        if (heading) {\n            const level = heading[1].length;\n            const content = heading[2];\n            const Tag = `h${Math.min(level, 6)}`;\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                className: `${styles.heading} ${getHeadingSize(level)}`,\n                children: renderInline(content, `h-${i}`, styles)\n            }, `h-${i}`, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this));\n            i++;\n            continue;\n        }\n        // Enhanced ordered list with nested support\n        const orderedItem = trimmed.match(/^(\\s*)(\\d+)\\.\\s+(.*)$/);\n        if (orderedItem) {\n            const baseIndent = orderedItem[1].length;\n            const items = [];\n            while(i < lines.length){\n                const li = lines[i];\n                const liTrimmed = li.trim();\n                const m = li.match(/^(\\s*)(\\d+)\\.\\s+(.*)$/);\n                if (!m || !liTrimmed) break;\n                const indent = m[1].length;\n                if (indent < baseIndent) break; // Less indented = end of this list\n                const content = m[3];\n                items.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: styles.olLi,\n                    children: renderInline(content, `ol-li-${i}`, styles)\n                }, `ol-li-${i}`, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 11\n                }, this));\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                className: \"mb-3 pl-6 space-y-1 list-decimal\",\n                children: items\n            }, `ol-${i}`, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 364,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Enhanced unordered list with nested support\n        const unorderedItem = trimmed.match(/^(\\s*)[-*+]\\s+(.*)$/);\n        if (unorderedItem) {\n            const baseIndent = unorderedItem[1].length;\n            const items = [];\n            while(i < lines.length){\n                const li = lines[i];\n                const liTrimmed = li.trim();\n                const m = li.match(/^(\\s*)[-*+]\\s+(.*)$/);\n                if (!m || !liTrimmed) break;\n                const indent = m[1].length;\n                if (indent < baseIndent) break; // Less indented = end of this list\n                const content = m[2];\n                items.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: styles.ulLi,\n                    children: renderInline(content, `ul-li-${i}`, styles)\n                }, `ul-li-${i}`, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 11\n                }, this));\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"mb-3 pl-6 space-y-1 list-disc\",\n                children: items\n            }, `ul-${i}`, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 397,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Task list [ ] and [x]\n        const taskItem = trimmed.match(/^(\\s*)[-*+]\\s+\\[([ x])\\]\\s+(.*)$/i);\n        if (taskItem) {\n            const baseIndent = taskItem[1].length;\n            const items = [];\n            while(i < lines.length){\n                const li = lines[i];\n                const liTrimmed = li.trim();\n                const m = li.match(/^(\\s*)[-*+]\\s+\\[([ x])\\]\\s+(.*)$/i);\n                if (!m || !liTrimmed) break;\n                const indent = m[1].length;\n                if (indent < baseIndent) break;\n                const checked = m[2].toLowerCase() === \"x\";\n                const content = m[3];\n                items.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: `${styles.ulLi} flex items-start gap-2`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"checkbox\",\n                            checked: checked,\n                            readOnly: true,\n                            className: \"mt-1 pointer-events-none\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: checked ? \"line-through opacity-60\" : \"\",\n                            children: renderInline(content, `task-li-${i}`, styles)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, `task-li-${i}`, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 11\n                }, this));\n                i++;\n            }\n            blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"mb-3 pl-6 space-y-1 list-none\",\n                children: items\n            }, `task-${i}`, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 440,\n                columnNumber: 9\n            }, this));\n            continue;\n        }\n        // Paragraph - collect consecutive non-empty, non-special lines\n        const para = [\n            line\n        ];\n        i++;\n        while(i < lines.length && lines[i].trim() && !/^```/.test(lines[i].trim()) && !/^#{1,6}\\s+/.test(lines[i].trim()) && !/^(\\s*)\\d+\\.\\s+/.test(lines[i]) && !/^(\\s*)[-*+]\\s+/.test(lines[i]) && !/^>\\s*/.test(lines[i].trim()) && !/^(-{3,}|\\*{3,}|_{3,})$/.test(lines[i].trim())){\n            para.push(lines[i]);\n            i++;\n        }\n        blocks.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: styles.paragraph,\n            children: renderInline(para.join(\"\\n\"), `p-${i}`, styles)\n        }, `p-${i}`, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n            lineNumber: 463,\n            columnNumber: 7\n        }, this));\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: blocks\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n        lineNumber: 469,\n        columnNumber: 10\n    }, this);\n}\n// Helper function for heading sizes\nfunction getHeadingSize(level) {\n    const sizes = {\n        1: \"text-2xl\",\n        2: \"text-xl\",\n        3: \"text-lg\",\n        4: \"text-base\",\n        5: \"text-sm\",\n        6: \"text-xs\"\n    };\n    return sizes[level] || \"text-base\";\n}\nconst MessageFormatter = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(({ content, variant = \"dark\" })=>{\n    const styles = variant === \"light\" ? {\n        link: \"text-indigo-600 hover:text-indigo-800 underline transition-colors\",\n        inlineCode: \"bg-gray-100 text-emerald-700 px-1.5 py-0.5 rounded text-[0.85em] font-mono border\",\n        heading: \"text-gray-900 font-semibold mt-4 mb-3 border-b border-gray-200 pb-1\",\n        olLi: \"text-gray-800 text-sm leading-relaxed mb-1\",\n        ulLi: \"text-gray-800 text-sm leading-relaxed mb-1\",\n        paragraph: \"mb-4 text-gray-800 text-sm leading-relaxed\",\n        codeBlockWrapper: \"my-4 bg-gray-50 border border-gray-200 rounded-lg font-mono text-sm overflow-hidden\",\n        codeBlockPre: \"text-gray-800 p-4 overflow-x-auto whitespace-pre\",\n        codeBlockLanguage: \"bg-gray-100 text-gray-600 px-3 py-1 text-xs font-medium border-b border-gray-200\",\n        blockquote: \"my-4 flex gap-3 p-3 bg-gray-50 border-l-4 border-indigo-400 rounded-r-lg\",\n        blockquoteBar: \"w-1 bg-indigo-400 rounded-full flex-shrink-0\",\n        strikethrough: \"line-through opacity-60\",\n        table: \"my-4 border-collapse border border-gray-300 rounded-lg overflow-hidden\",\n        tableHeader: \"bg-gray-100 border border-gray-300 px-3 py-2 font-medium text-gray-900\",\n        tableCell: \"border border-gray-300 px-3 py-2 text-gray-800\",\n        hr: \"my-6 border-0 h-px bg-gray-300\",\n        mention: \"text-blue-700 bg-blue-50 px-1 rounded\",\n        hashtag: \"text-purple-700 bg-purple-50 px-1 rounded\",\n        command: \"text-emerald-700 bg-emerald-50 px-1 rounded font-medium\",\n        underline: \"underline underline-offset-2\",\n        spoiler: \"bg-gray-300 text-gray-300 rounded px-1 cursor-pointer select-none\",\n        spoilerReveal: \"bg-gray-100 text-gray-800 rounded px-1\",\n        image: \"max-w-full h-auto rounded border border-gray-200\"\n    } : {\n        link: \"text-blue-400 hover:text-blue-300 underline transition-colors\",\n        inlineCode: \"bg-slate-700/50 text-green-300 px-1.5 py-0.5 rounded text-[0.85em] font-mono border border-slate-600/30\",\n        heading: \"text-slate-100 font-semibold mt-4 mb-3 border-b border-slate-600/40 pb-1\",\n        olLi: \"text-slate-200 text-sm leading-relaxed mb-1\",\n        ulLi: \"text-slate-200 text-sm leading-relaxed mb-1\",\n        paragraph: \"mb-4 text-slate-200 text-sm leading-relaxed\",\n        codeBlockWrapper: \"my-4 bg-slate-800/60 border border-slate-600/40 rounded-lg font-mono text-sm overflow-hidden\",\n        codeBlockPre: \"text-green-300 p-4 overflow-x-auto whitespace-pre\",\n        codeBlockLanguage: \"bg-slate-700/50 text-slate-300 px-3 py-1 text-xs font-medium border-b border-slate-600/40\",\n        blockquote: \"my-4 flex gap-3 p-3 bg-slate-800/40 border-l-4 border-blue-400 rounded-r-lg\",\n        blockquoteBar: \"w-1 bg-blue-400 rounded-full flex-shrink-0\",\n        strikethrough: \"line-through opacity-60\",\n        table: \"my-4 border-collapse border border-slate-600 rounded-lg overflow-hidden\",\n        tableHeader: \"bg-slate-700 border border-slate-600 px-3 py-2 font-medium text-slate-200\",\n        tableCell: \"border border-slate-600 px-3 py-2 text-slate-300\",\n        hr: \"my-6 border-0 h-px bg-slate-600\",\n        mention: \"text-blue-300 bg-slate-700/50 px-1 rounded\",\n        hashtag: \"text-fuchsia-300 bg-slate-700/50 px-1 rounded\",\n        command: \"text-emerald-300 bg-slate-700/50 px-1 rounded font-medium\",\n        underline: \"underline underline-offset-2 decoration-slate-400\",\n        spoiler: \"bg-slate-500 text-slate-500 rounded px-1 cursor-pointer select-none\",\n        spoilerReveal: \"bg-slate-800 text-slate-100 rounded px-1\",\n        image: \"max-w-full h-auto rounded border border-slate-600/40\"\n    };\n    const formatMessage = (text)=>{\n        try {\n            // Preprocess text to normalize line endings and handle edge cases\n            const normalizedText = text.replace(/\\r\\n/g, \"\\n\") // Normalize line endings\n            .replace(/\\r/g, \"\\n\") // Handle old Mac line endings\n            .trim(); // Remove leading/trailing whitespace\n            if (!normalizedText) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 italic\",\n                    children: \"Empty message\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 16\n                }, undefined);\n            }\n            return renderBlocks(normalizedText, styles);\n        } catch (error) {\n            console.error(\"MessageFormatter error:\", error);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `whitespace-pre-wrap text-sm leading-relaxed ${variant === \"light\" ? \"text-gray-800\" : \"text-slate-200\"}`,\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n                lineNumber: 553,\n                columnNumber: 9\n            }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `message-formatter ${variant === \"light\" ? \"prose prose-sm max-w-none\" : \"prose prose-sm prose-invert max-w-none\"}`,\n        children: formatMessage(content)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\MessageFormatter.tsx\",\n        lineNumber: 563,\n        columnNumber: 5\n    }, undefined);\n});\nMessageFormatter.displayName = \"MessageFormatter\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MessageFormatter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MessageFormatter.tsx\n");

/***/ }),

/***/ "./src/components/TimelineItem.tsx":
/*!*****************************************!*\
  !*** ./src/components/TimelineItem.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimelineItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MessageFormatter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageFormatter */ \"./src/components/MessageFormatter.tsx\");\n\n\n\n// Icon Components\nconst UserIcon = ({ name })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-blue-400/30\",\n        children: name.charAt(0).toUpperCase()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined);\nconst AIAgentIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-emerald-500 to-cyan-500 flex items-center justify-center text-white shadow-lg border border-emerald-400/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-4 h-4 md:w-5 md:w-5\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n            lineNumber: 65,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 64,\n        columnNumber: 3\n    }, undefined);\nconst CustomerIcon = ({ name })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-orange-400/30\",\n        children: name.charAt(0).toUpperCase()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 72,\n        columnNumber: 3\n    }, undefined);\nconst ToolIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-amber-500 to-yellow-500 flex items-center justify-center text-white shadow-lg border border-amber-400/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-4 h-4 md:w-5 md:w-5\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n            lineNumber: 79,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, undefined);\n// Performance Metrics Component\nconst PerformanceMetrics = ({ cost, executionTime, inputTokens, outputTokens, variant = \"default\" })=>{\n    // Convert null/undefined to 0 and ensure numeric values\n    const safeCost = Number(cost) || 0;\n    const safeExecutionTime = Number(executionTime) || 0;\n    const safeInputTokens = Number(inputTokens) || 0;\n    const safeOutputTokens = Number(outputTokens) || 0;\n    const hasMetrics = safeCost > 0 || safeExecutionTime > 0 || safeInputTokens > 0 || safeOutputTokens > 0;\n    if (!hasMetrics) return null;\n    const baseClasses = variant === \"tool\" ? \"bg-amber-500/10 border border-amber-400/20 rounded-md p-2 mb-2\" : \"mt-3 pt-3 border-t border-current/20\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: baseClasses,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs font-medium opacity-80 mb-2\",\n                children: \"Performance Metrics:\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-2 text-xs\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-80\",\n                        children: [\n                            \"\\uD83D\\uDCB0 Cost: $\",\n                            safeCost.toFixed(6)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-80\",\n                        children: [\n                            \"⏱️ Time: \",\n                            safeExecutionTime,\n                            \"ms\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-80\",\n                        children: [\n                            \"\\uD83D\\uDCE5 Input: \",\n                            safeInputTokens,\n                            \" tokens\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-80\",\n                        children: [\n                            \"\\uD83D\\uDCE4 Output: \",\n                            safeOutputTokens,\n                            \" tokens\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, undefined);\n};\nfunction TimelineItem({ item }) {\n    const timestamp = new Date(item.createdAt);\n    if (item.type === \"message\") {\n        // Check if this is a customer message by looking at the content format\n        const isCustomerMessage = item.content && item.content.startsWith(\"[\") && item.content.includes(\"]:\");\n        const isAgent = !item.user && !item.customer && !isCustomerMessage;\n        const isCustomer = !!item.customer || isCustomerMessage;\n        const isUser = !!item.user;\n        // Extract customer name from message content if it's a customer message\n        let senderName = \"Unknown\";\n        if (isCustomerMessage) {\n            const match = item.content.match(/^\\[([^\\]]+)\\]:/);\n            senderName = match ? match[1] : \"Customer\";\n        } else if (item.customer?.name || item.customer?.email) {\n            senderName = item.customer.name || item.customer.email || \"Customer\";\n        } else if (isAgent) {\n            senderName = \"AI Agent\";\n        } else if (item.user?.name || item.user?.email) {\n            senderName = item.user.name || item.user.email || \"User\";\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex gap-3 md:gap-4 ${isCustomer ? \"justify-end\" : \"justify-start\"}`,\n            children: [\n                !isCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 mt-1\",\n                    children: isAgent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIAgentIcon, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserIcon, {\n                        name: senderName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `max-w-[85%] md:max-w-[70%] ${isCustomer ? \"order-1\" : \"\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg ${isAgent ? \"bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100\" : isCustomer ? \"bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-400/30 text-orange-100\" : \"bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-400/30 text-blue-100\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between gap-2 md:gap-4 mb-1 md:mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-xs md:text-sm opacity-90\",\n                                        style: {\n                                            fontFamily: \"Exo 2, sans-serif\"\n                                        },\n                                        children: senderName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs opacity-60\",\n                                        style: {\n                                            fontFamily: \"Exo 2, sans-serif\"\n                                        },\n                                        children: isNaN(timestamp.getTime()) ? \"\" : timestamp.toLocaleTimeString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontFamily: \"Exo 2, sans-serif\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageFormatter__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    content: isCustomerMessage ? item.content.replace(/^\\[[^\\]]+\\]:\\s*/, \"\") : item.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PerformanceMetrics, {\n                                cost: item.cost,\n                                executionTime: item.executionTime,\n                                inputTokens: item.inputTokens,\n                                outputTokens: item.outputTokens\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            (item.imageUrl || item.videoUrl || item.attachmentUrl) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 pt-3 border-t border-current/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium opacity-80 mb-2\",\n                                        children: \"Attachments:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            item.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-80\",\n                                                children: [\n                                                    \"\\uD83D\\uDCF7 Image: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: item.imageUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"underline hover:opacity-100\",\n                                                        children: item.imageUrl\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.videoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-80\",\n                                                children: [\n                                                    \"\\uD83C\\uDFA5 Video: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: item.videoUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"underline hover:opacity-100\",\n                                                        children: item.videoUrl\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.attachmentUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-80\",\n                                                children: [\n                                                    \"\\uD83D\\uDCCE \",\n                                                    item.attachmentType || \"File\",\n                                                    \": \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: item.attachmentUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"underline hover:opacity-100\",\n                                                        children: item.attachmentUrl\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 59\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this),\n                isCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 mt-1 order-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerIcon, {\n                        name: senderName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    }\n    // Tool call item\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 md:gap-4 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolIcon, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[85%] md:max-w-[70%]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-amber-500/20 to-yellow-500/20 border border-amber-400/30 text-amber-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between gap-2 md:gap-4 mb-1 md:mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-xs md:text-sm opacity-90\",\n                                    style: {\n                                        fontFamily: \"Exo 2, sans-serif\"\n                                    },\n                                    children: [\n                                        \"\\uD83D\\uDD27 \",\n                                        item.toolName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs opacity-60\",\n                                    style: {\n                                        fontFamily: \"Exo 2, sans-serif\"\n                                    },\n                                    children: isNaN(timestamp.getTime()) ? \"\" : timestamp.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${item.success ? \"bg-emerald-500/10 text-emerald-300 ring-1 ring-emerald-500/20\" : \"bg-red-500/10 text-red-300 ring-1 ring-red-500/20\"}`,\n                                            children: item.success ? \"✅ Success\" : \"❌ Failed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        item.executionTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-amber-300/80\",\n                                            children: [\n                                                \"⏱️ \",\n                                                item.executionTime,\n                                                \"ms\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PerformanceMetrics, {\n                                    cost: item.cost,\n                                    executionTime: item.executionTime,\n                                    inputTokens: item.inputTokens,\n                                    outputTokens: item.outputTokens,\n                                    variant: \"tool\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                item.toolInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium opacity-80 mb-1\",\n                                            children: \"Input:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-amber-500/10 border border-amber-400/20 rounded-md p-2 text-xs overflow-x-auto\",\n                                            children: JSON.stringify(item.toolInput, null, 2)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                item.success && item.toolOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium opacity-80 mb-1\",\n                                            children: \"Output:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-emerald-500/10 border border-emerald-500/20 rounded-md p-2 text-xs overflow-x-auto\",\n                                            children: JSON.stringify(item.toolOutput, null, 2)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this),\n                                !item.success && item.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium opacity-80 mb-1 text-red-300\",\n                                            children: \"Error:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-500/10 border border-red-500/20 rounded-md p-2 text-xs text-red-200\",\n                                            children: item.errorMessage\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TimelineItem.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/TimelineItem.tsx\n");

/***/ }),

/***/ "./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const storageKey = \"teno:auth:user\";\n    const lastUserIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const current = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(current);\n            try {\n                if (current) {\n                    localStorage.setItem(storageKey, JSON.stringify(current));\n                } else {\n                    localStorage.removeItem(storageKey);\n                }\n            } catch  {}\n            try {\n                const prevId = lastUserIdRef.current;\n                const nextId = current?.id ?? null;\n                const changed = prevId !== nextId;\n                lastUserIdRef.current = nextId;\n                if (changed && \"undefined\" !== \"undefined\") {}\n            } catch  {}\n        } catch (err) {\n            console.warn(\"[AuthContext] Error during refresh:\", err);\n            setError(\"Failed to load user\");\n            setUser(null);\n            try {\n                localStorage.removeItem(storageKey);\n            } catch  {}\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const loginWithGoogle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((nextPath)=>{\n        (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.redirectToGoogleAuth)(nextPath);\n    }, []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        // Optimistically clear local state for snappier UX\n        setUser(null);\n        try {\n            localStorage.removeItem(storageKey);\n        } catch  {}\n        try {\n            await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.performLogout)();\n        } finally{\n            // Always refresh after logout to ensure backend/session is in sync\n            await refresh();\n        }\n    }, [\n        refresh\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Optimistically hydrate from localStorage for fast first paint\n        try {\n            const raw = localStorage.getItem(storageKey);\n            if (raw) {\n                const cached = JSON.parse(raw);\n                setUser(cached);\n                lastUserIdRef.current = cached?.id ?? null;\n                // If we have a cached user, try to refresh from backend\n                refresh();\n            } else {\n                // No cached user, just set loading to false\n                setIsLoading(false);\n            }\n        } catch (e) {\n            console.warn(\"[AuthContext] Error reading cached user:\", e);\n            setIsLoading(false);\n        }\n        // Listen for global unauthorized signals to immediately drop user state\n        const onUnauthorized = ()=>{\n            (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.clearClientAuthArtifacts)();\n            setUser(null);\n            lastUserIdRef.current = null;\n        };\n        if (false) {}\n        return ()=>{\n            if (false) {}\n        };\n    }, [\n        refresh\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            isLoading,\n            error,\n            refresh,\n            loginWithGoogle,\n            logout\n        }), [\n        user,\n        isLoading,\n        error,\n        refresh,\n        loginWithGoogle,\n        logout\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!ctx) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "./src/context/PreferencesContext.tsx":
/*!********************************************!*\
  !*** ./src/context/PreferencesContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreferencesProvider: () => (/* binding */ PreferencesProvider),\n/* harmony export */   usePreferences: () => (/* binding */ usePreferences)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _utils_preferences__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/preferences */ \"./src/utils/preferences.ts\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\n\n\n\n\nconst PreferencesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction getBrowserDefaults() {\n    const { language, country, currency } = (0,_utils_preferences__WEBPACK_IMPORTED_MODULE_3__.getBrowserPreferenceDefaults)();\n    return {\n        language,\n        country,\n        currency\n    };\n}\nfunction PreferencesProvider({ children }) {\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const defaults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getBrowserDefaults(), []);\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.language);\n    const [currency, setCurrencyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.currency);\n    const [country, setCountryState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.country);\n    const storageKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const userId = user?.id ? String(user.id) : \"anon\";\n        const key = `teno:prefs:${userId}`;\n        console.debug(\"[Prefs] storageKey computed\", {\n            userId,\n            key\n        });\n        return key;\n    }, [\n        user?.id\n    ]);\n    // Hydrate from localStorage on mount or when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const raw = localStorage.getItem(storageKey);\n            console.debug(\"[Prefs] hydrate start\", {\n                storageKey,\n                raw\n            });\n            if (raw) {\n                const parsed = JSON.parse(raw);\n                const nextLanguage = parsed.language || defaults.language;\n                const nextCurrency = parsed.currency || defaults.currency;\n                const nextCountry = parsed.country || defaults.country;\n                console.debug(\"[Prefs] hydrate parsed\", {\n                    parsed,\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n            } else {\n                console.debug(\"[Prefs] hydrate no existing, using defaults\", defaults);\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        } catch  {\n            console.debug(\"[Prefs] hydrate error, falling back to defaults\", defaults);\n            setLanguageState(defaults.language);\n            setCurrencyState(defaults.currency);\n            setCountryState(defaults.country);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        storageKey\n    ]);\n    // Re-hydrate on auth changes (login/logout) since key scope changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const onAuthChange = ()=>{\n            try {\n                const raw = localStorage.getItem(storageKey);\n                if (raw) {\n                    const parsed = JSON.parse(raw);\n                    setLanguageState(parsed.language || defaults.language);\n                    setCurrencyState(parsed.currency || defaults.currency);\n                    setCountryState(parsed.country || defaults.country);\n                } else {\n                    setLanguageState(defaults.language);\n                    setCurrencyState(defaults.currency);\n                    setCountryState(defaults.country);\n                }\n            } catch  {\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        };\n        if (false) {}\n        return ()=>{\n            if (false) {}\n        };\n    }, [\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    // If a user is present, fetch server-side preferences and apply\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userId = user?.id ? String(user.id) : null;\n        if (!userId) return;\n        let aborted = false;\n        (async ()=>{\n            try {\n                const url = `${(0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.getBackendUrl)()}/users/${encodeURIComponent(userId)}`;\n                console.debug(\"[Prefs] fetching server preferences\", {\n                    userId,\n                    url\n                });\n                const resp = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.fetchWithCredentials)(url);\n                if (!resp.ok) return;\n                const payload = await resp.json();\n                if (aborted || !payload) return;\n                const nextLanguage = payload.preferredLanguage || defaults.language;\n                const nextCurrency = payload.preferredCurrency || defaults.currency;\n                const nextCountry = payload.countryCode || defaults.country;\n                console.debug(\"[Prefs] server preferences received\", {\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n                try {\n                    localStorage.setItem(storageKey, JSON.stringify({\n                        language: nextLanguage,\n                        currency: nextCurrency,\n                        country: nextCountry\n                    }));\n                } catch  {}\n            } catch  {}\n        })();\n        return ()=>{\n            aborted = true;\n        };\n    }, [\n        user?.id,\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    const persist = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((next)=>{\n        try {\n            const current = {\n                language,\n                currency,\n                country\n            };\n            const merged = {\n                ...current,\n                ...next\n            };\n            console.debug(\"[Prefs] persist\", {\n                storageKey,\n                current,\n                next,\n                merged\n            });\n            localStorage.setItem(storageKey, JSON.stringify(merged));\n        } catch  {}\n    }, [\n        language,\n        currency,\n        country,\n        storageKey\n    ]);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lng)=>{\n        setLanguageState(lng);\n        persist({\n            language: lng\n        });\n    }, [\n        persist\n    ]);\n    const setCurrency = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cur)=>{\n        console.debug(\"[Prefs] setCurrency\", {\n            from: currency,\n            to: cur\n        });\n        setCurrencyState(cur);\n        persist({\n            currency: cur\n        });\n    }, [\n        persist,\n        currency\n    ]);\n    const setCountry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cc)=>{\n        setCountryState(cc);\n        persist({\n            country: cc\n        });\n    }, [\n        persist\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            language,\n            currency,\n            country,\n            setLanguage,\n            setCurrency,\n            setCountry\n        }), [\n        language,\n        currency,\n        country,\n        setLanguage,\n        setCurrency,\n        setCountry\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreferencesContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\PreferencesContext.tsx\",\n        lineNumber: 169,\n        columnNumber: 3\n    }, this);\n}\nfunction usePreferences() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PreferencesContext);\n    if (!ctx) {\n        throw new Error(\"usePreferences must be used within a PreferencesProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/PreferencesContext.tsx\n");

/***/ }),

/***/ "./src/context/StoreContext.tsx":
/*!**************************************!*\
  !*** ./src/context/StoreContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StoreProvider: () => (/* binding */ StoreProvider),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/context/AuthContext.tsx\");\n\n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction StoreProvider({ children }) {\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [currentStoreId, setCurrentStoreIdState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const storageKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const userId = user?.id ? String(user.id) : \"anon\";\n        return `teno:currentStoreId:${userId}`;\n    }, [\n        user?.id\n    ]);\n    // Hydrate from localStorage on mount or when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const raw = localStorage.getItem(storageKey);\n            if (raw) {\n                setCurrentStoreIdState(raw);\n            } else {\n                setCurrentStoreIdState(null);\n            }\n        } catch  {}\n    }, [\n        storageKey\n    ]);\n    // Clear store when user changes (login/logout) because key scope changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const onAuthChange = ()=>{\n            setCurrentStoreIdState(null);\n        };\n        if (false) {}\n        return ()=>{\n            if (false) {}\n        };\n    }, []);\n    const setCurrentStoreId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((storeId)=>{\n        setCurrentStoreIdState(storeId);\n        try {\n            if (storeId) localStorage.setItem(storageKey, storeId);\n            else localStorage.removeItem(storageKey);\n        } catch  {}\n    }, [\n        storageKey\n    ]);\n    const clearStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setCurrentStoreId(null);\n    }, [\n        setCurrentStoreId\n    ]);\n    const autoSelectFirstStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((stores)=>{\n        if (stores.length > 0 && !currentStoreId) {\n            const firstStore = stores[0];\n            setCurrentStoreId(firstStore.id);\n        }\n    }, [\n        currentStoreId,\n        setCurrentStoreId\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            currentStoreId,\n            setCurrentStoreId,\n            clearStore,\n            autoSelectFirstStore\n        }), [\n        currentStoreId,\n        setCurrentStoreId,\n        clearStore,\n        autoSelectFirstStore\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StoreContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\StoreContext.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\nfunction useStore() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StoreContext);\n    if (!ctx) {\n        throw new Error(\"useStore must be used within a StoreProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/StoreContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/EntityTable.fadein.css */ \"./src/components/EntityTable.fadein.css\");\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../context/PreferencesContext */ \"./src/context/PreferencesContext.tsx\");\n/* harmony import */ var _utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/useAuthGuard */ \"./src/utils/useAuthGuard.ts\");\n\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_StoreContext__WEBPACK_IMPORTED_MODULE_4__.StoreProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__.PreferencesProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GuardedApp, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                        ...pageProps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\nfunction GuardedApp({ children }) {\n    // Run global auth guard on every route navigation\n    (0,_utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__.useAuthGuard)({\n        publicPaths: [\n            \"/\",\n            \"/login\",\n            \"/_error\",\n            \"/setup/store\",\n            \"/store/[storeUuid]\",\n            \"/storefront\",\n            \"/live/[uuid]\"\n        ]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/live/[uuid].tsx":
/*!***********************************!*\
  !*** ./src/pages/live/[uuid].tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicLiveConversationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/TimelineItem */ \"./src/components/TimelineItem.tsx\");\n\n\n\n\n\n\n\n// Icon Components\nconst CustomerIcon = ({ name })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-orange-400/30\",\n        children: name.charAt(0).toUpperCase()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\nfunction PublicLiveConversationPage() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { uuid } = router.query;\n    const [page] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isCustomerNameSet, setIsCustomerNameSet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isWaitingForAgent, setIsWaitingForAgent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const waitingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const limit = 50;\n    const conversationUuid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        // Wait for router to be ready and uuid to be available\n        if (router.isReady && uuid) {\n            return uuid;\n        }\n        return null;\n    }, [\n        router.isReady,\n        uuid\n    ]);\n    // Fetch conversation data\n    const fetchConversation = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getByUuid(conversationUuid);\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setConversation(result.data || result);\n            } else {\n                setConversation(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching conversation:\", err);\n            setError(\"Failed to load conversation\");\n        }\n    };\n    // Fetch timeline data\n    const fetchTimeline = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getUnifiedTimelineByUuid(conversationUuid, {\n                page,\n                limit\n            });\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setTimeline(result.data || result);\n            } else {\n                setTimeline(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching timeline:\", err);\n            setError(\"Failed to load timeline\");\n        }\n    };\n    // Fetch data when dependencies change\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (conversationUuid && router.isReady) {\n            fetchConversation();\n            fetchTimeline();\n        }\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    // Set up polling for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!conversationUuid || !router.isReady) return;\n        const interval = setInterval(()=>{\n            fetchConversation();\n            fetchTimeline();\n        }, 2000); // Reduced from 5000ms to 2000ms for better responsiveness\n        return ()=>{\n            clearInterval(interval);\n            // Clean up waiting timeout on unmount\n            if (waitingTimeoutRef.current) {\n                clearTimeout(waitingTimeoutRef.current);\n            }\n        };\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    const appendMessage = async (messageData)=>{\n        if (!conversationUuid) return;\n        try {\n            console.log(\"\\uD83D\\uDD27 Frontend: Sending message:\", messageData);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.appendMessageByUuid(conversationUuid, messageData);\n            console.log(\"✅ Frontend: Message sent successfully:\", response);\n            setNewMessage(\"\");\n            // Set waiting state for agent response (only for user messages)\n            const isUserMessage = !messageData.agentId || messageData.agentId === \"customer-message\";\n            if (isUserMessage) {\n                setIsWaitingForAgent(true);\n                // Clear any existing timeout\n                if (waitingTimeoutRef.current) {\n                    clearTimeout(waitingTimeoutRef.current);\n                }\n                // Set a timeout to clear waiting state if no response comes (30 seconds)\n                waitingTimeoutRef.current = setTimeout(()=>{\n                    setIsWaitingForAgent(false);\n                }, 30000);\n            }\n            // Refresh data\n            fetchConversation();\n            fetchTimeline();\n            messagesEndRef.current?.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        } catch (err) {\n            console.error(\"❌ Frontend: Failed to send message:\", err);\n            setError(\"Failed to send message\");\n            setIsWaitingForAgent(false);\n        }\n    };\n    // Use a ref to track previous message count to prevent unnecessary effects\n    const prevMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    const prevAgentMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const currentMessageCount = timeline?.timeline?.length || 0;\n        if (currentMessageCount > prevMessageCountRef.current) {\n            messagesEndRef.current?.scrollIntoView({\n                behavior: \"smooth\"\n            });\n            prevMessageCountRef.current = currentMessageCount;\n        }\n        // Check for new agent messages to clear waiting state\n        if (timeline?.timeline) {\n            const agentMessages = timeline.timeline.filter((item)=>item.type === \"message\" && item.data?.metadata?.agentId && item.data?.metadata?.agentId !== \"customer-message\");\n            const currentAgentMessageCount = agentMessages.length;\n            if (currentAgentMessageCount > prevAgentMessageCountRef.current && isWaitingForAgent) {\n                setIsWaitingForAgent(false);\n                // Clear the timeout since we got a response\n                if (waitingTimeoutRef.current) {\n                    clearTimeout(waitingTimeoutRef.current);\n                    waitingTimeoutRef.current = null;\n                }\n                prevAgentMessageCountRef.current = currentAgentMessageCount;\n            } else if (currentAgentMessageCount !== prevAgentMessageCountRef.current) {\n                prevAgentMessageCountRef.current = currentAgentMessageCount;\n            }\n        }\n    }, [\n        timeline?.timeline?.length,\n        timeline?.timeline,\n        isWaitingForAgent\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage(e);\n        }\n    };\n    const handleSendMessage = (e)=>{\n        e.preventDefault();\n        if (!newMessage.trim() || !conversation) return;\n        // Additional validation\n        if (!conversation.uuid) {\n            console.error(\"Conversation UUID is missing\");\n            return;\n        }\n        // Require customer name\n        if (!customerName.trim()) {\n            setError(\"Please enter your name to send a message\");\n            return;\n        }\n        try {\n            // Create customer message\n            const messageData = {\n                content: `[${customerName.trim()}]: ${newMessage.trim()}`,\n                createdBy: \"1\",\n                agentId: \"customer-message\"\n            };\n            appendMessage(messageData);\n        } catch (error) {\n            console.error(\"Failed to prepare message:\", error);\n        }\n    };\n    // Customer name setup modal\n    if (!isCustomerNameSet && conversation) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-slate-950 relative overflow-hidden flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"Join Live Chat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Join the live customer conversation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0\",\n                            style: {\n                                backgroundImage: `\r\n              radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\r\n              radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\r\n              radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\r\n            `\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 max-w-md mx-auto px-6 py-8 bg-slate-900/50 border border-cyan-500/20 rounded-xl backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400 mb-2\",\n                                    children: \"Join Live Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-cyan-300/80\",\n                                    children: [\n                                        conversation?.store?.name || \"Store\",\n                                        \" - Live Conversation\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: (e)=>{\n                                e.preventDefault();\n                                if (customerName.trim()) {\n                                    setIsCustomerNameSet(true);\n                                }\n                            },\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"customerName\",\n                                            className: \"block text-sm font-medium text-cyan-300 mb-2\",\n                                            children: \"Your Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"customerName\",\n                                            value: customerName,\n                                            onChange: (e)=>setCustomerName(e.target.value),\n                                            placeholder: \"Enter your name\",\n                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 text-cyan-100 placeholder-cyan-400/50\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !customerName.trim(),\n                                    className: \"w-full px-6 py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95\",\n                                    children: \"Join Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            fontFamily: 'system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif'\n        },\n        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"min-h-screen bg-slate-950 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        className: \"jsx-c1d451e38a0b1618\",\n                        children: conversation?.title || \"Live Chat\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Live customer conversation\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 opacity-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: `\r\n            radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\r\n            radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\r\n            radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\r\n          `\n                        },\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: `\r\n            linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%),\r\n            linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%)\r\n          `,\n                            backgroundSize: \"50px 50px\"\n                        },\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 opacity-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"relative z-10 max-w-4xl mx-auto px-4 py-4 md:py-6 h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"mb-4 md:mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontFamily: \"Orbitron, monospace\"\n                                },\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400\",\n                                children: conversation?.store?.name || \"LIVE CHAT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontFamily: \"Exo 2, sans-serif\"\n                                },\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-sm md:text-base text-cyan-300/80 mt-1\",\n                                children: [\n                                    \"Live Chat - \",\n                                    customerName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"bg-red-500/10 border border-red-500/30 text-red-300 px-4 py-3 rounded-lg backdrop-blur-sm mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-5 h-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\",\n                                        className: \"jsx-c1d451e38a0b1618\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"relative w-16 h-16 mx-auto mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 border-4 border-cyan-400/20 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 border-4 border-transparent border-t-cyan-400 rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontFamily: \"Exo 2, sans-serif\"\n                                    },\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-cyan-300/80\",\n                                    children: \"Initializing Neural Interface...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this),\n                    conversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 flex flex-col bg-slate-900/30 border border-cyan-500/20 rounded-xl backdrop-blur-sm overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 overflow-y-auto p-3 md:p-4 space-y-3 md:space-y-4 scrollbar-thin scrollbar-thumb-cyan-500/30 scrollbar-track-transparent\",\n                                children: [\n                                    (timeline?.timeline || []).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            item: item\n                                        }, `${item.type}-${item.id}`, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)),\n                                    conversation?.notificationStatus && conversation.notificationStatus !== \"None\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-3 md:gap-4 justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerIcon, {\n                                                    name: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"max-w-[85%] md:max-w-[70%]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontFamily: \"Exo 2, sans-serif\"\n                                                                },\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-xs opacity-80\",\n                                                                children: conversation.notificationStatus === \"AgentIsThinking\" ? \"AI Agent is thinking...\" : \"AI Agent is generating response...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"inline-flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/80 rounded-full animate-bounce\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/60 rounded-full animate-bounce [animation-delay:120ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/40 rounded-full animate-bounce [animation-delay:240ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this),\n                                    isWaitingForAgent && (!conversation?.notificationStatus || conversation.notificationStatus === \"None\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-3 md:gap-4 justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerIcon, {\n                                                    name: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"max-w-[85%] md:max-w-[70%]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontFamily: \"Exo 2, sans-serif\"\n                                                                },\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-xs opacity-80\",\n                                                                children: \"AI Agent is preparing response...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"inline-flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/80 rounded-full animate-bounce\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/60 rounded-full animate-bounce [animation-delay:120ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/40 rounded-full animate-bounce [animation-delay:240ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-c1d451e38a0b1618\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSendMessage,\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"border-t border-cyan-500/20 p-3 md:p-4 bg-slate-900/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-2 md:gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Enter your message...\",\n                                                    style: {\n                                                        fontFamily: \"Exo 2, sans-serif\"\n                                                    },\n                                                    rows: 1,\n                                                    disabled: isLoading,\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-full px-3 md:px-4 py-2 md:py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 resize-none text-cyan-100 placeholder-cyan-400/50 text-sm md:text-base transition-all duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-4 h-4 border-2 border-cyan-400/30 border-t-cyan-400 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: !newMessage.trim() || isLoading || !customerName.trim(),\n                                            style: {\n                                                fontFamily: \"Exo 2, sans-serif\"\n                                            },\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-4 md:px-6 py-2 md:py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-cyan-500/25 text-sm md:text-base\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-4 h-4 md:w-5 md:h-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\",\n                                                    className: \"jsx-c1d451e38a0b1618\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this) : null\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"c1d451e38a0b1618\",\n                children: \".scrollbar-thin{scrollbar-width:thin}.scrollbar-thumb-cyan-500\\\\\\\\/30::-webkit-scrollbar-thumb {background-color:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}.scrollbar-track-transparent::-webkit-scrollbar-track{background-color:transparent}::-webkit-scrollbar{width:6px}::-webkit-scrollbar-track{background:transparent}::-webkit-scrollbar-thumb{background:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}::-webkit-scrollbar-thumb:hover{background:rgba(6,182,212,.5)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/live/[uuid].tsx\n");

/***/ }),

/***/ "./src/utils/api.ts":
/*!**************************!*\
  !*** ./src/utils/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   agentApi: () => (/* binding */ agentApi),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   conversationApi: () => (/* binding */ conversationApi),\n/* harmony export */   customerApi: () => (/* binding */ customerApi),\n/* harmony export */   imageApi: () => (/* binding */ imageApi),\n/* harmony export */   orderApi: () => (/* binding */ orderApi),\n/* harmony export */   productApi: () => (/* binding */ productApi),\n/* harmony export */   storeApi: () => (/* binding */ storeApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"./src/utils/auth.ts\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\n// Helper function to handle API responses\nasync function handleResponse(response) {\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);\n    }\n    return response.json();\n}\n// Generic API client class\nclass ApiClient {\n    constructor(baseUrl = API_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n    // Generic GET request\n    async get(endpoint, params) {\n        const url = new URL(`${this.baseUrl}${endpoint}`);\n        if (params) {\n            Object.entries(params).forEach(([key, value])=>{\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        console.log(\"API GET request to:\", url.toString());\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(url.toString(), {\n            method: \"GET\"\n        });\n        return handleResponse(response);\n    }\n    // Generic POST request\n    async post(endpoint, data) {\n        const url = `${this.baseUrl}${endpoint}`;\n        console.log(\"\\uD83D\\uDD27 API POST request to:\", url);\n        console.log(\"\\uD83D\\uDD27 API POST data:\", data);\n        console.log(\"\\uD83D\\uDD27 API base URL:\", this.baseUrl);\n        try {\n            const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(url, {\n                method: \"POST\",\n                body: data ? JSON.stringify(data) : undefined\n            });\n            console.log(\"\\uD83D\\uDD27 API POST response status:\", response.status);\n            console.log(\"\\uD83D\\uDD27 API POST response headers:\", Object.fromEntries(response.headers.entries()));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"❌ API POST error response:\", errorText);\n                throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);\n            }\n            return handleResponse(response);\n        } catch (error) {\n            console.error(\"❌ API POST fetch error:\", error);\n            throw error;\n        }\n    }\n    // Generic PUT request\n    async put(endpoint, data) {\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(`${this.baseUrl}${endpoint}`, {\n            method: \"PUT\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n        return handleResponse(response);\n    }\n    // Generic DELETE request\n    async delete(endpoint) {\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(`${this.baseUrl}${endpoint}`, {\n            method: \"DELETE\"\n        });\n        return handleResponse(response);\n    }\n}\n// Create a default API client instance\nconst apiClient = new ApiClient();\n// Store API methods\nconst storeApi = {\n    getAll: (params)=>apiClient.get(\"/stores\", params),\n    getById: (id)=>apiClient.get(`/stores/${id}`),\n    getByUserId: (userId, params)=>apiClient.get(`/stores/user/${userId}`, params),\n    getByUuid: (uuid)=>apiClient.get(`/stores/uuid/${uuid}`),\n    create: (data)=>apiClient.post(\"/stores\", data),\n    update: (id, data)=>apiClient.put(`/stores/${id}`, data),\n    delete: (id)=>apiClient.delete(`/stores/${id}`)\n};\n// Product API methods\nconst productApi = {\n    getAll: (params)=>apiClient.get(\"/products\", params),\n    getById: (id)=>apiClient.get(`/products/${id}`),\n    getByStoreId: (storeId, params)=>apiClient.get(`/products/store/${storeId}`, params),\n    getByStoreUuid: (storeUuid, params)=>apiClient.get(`/products/store/uuid/${storeUuid}`, params),\n    create: (data)=>apiClient.post(\"/products\", data),\n    update: (id, data)=>apiClient.put(`/products/${id}`, data),\n    delete: (id)=>apiClient.delete(`/products/${id}`)\n};\n// Customer API methods\nconst customerApi = {\n    getAll: (params)=>apiClient.get(\"/customers\", params),\n    getById: (id)=>apiClient.get(`/customers/${id}`),\n    getByStoreId: (storeId, params)=>apiClient.get(`/customers/store/${storeId}`, params),\n    create: (data)=>apiClient.post(\"/customers\", data),\n    update: (id, data)=>apiClient.put(`/customers/${id}`, data),\n    delete: (id)=>apiClient.delete(`/customers/${id}`)\n};\n// User API methods\nconst userApi = {\n    getAll: (params)=>apiClient.get(\"/users\", params),\n    getById: (id)=>apiClient.get(`/users/${id}`),\n    create: (data)=>apiClient.post(\"/users\", data),\n    update: (id, data)=>apiClient.put(`/users/${id}`, data),\n    delete: (id)=>apiClient.delete(`/users/${id}`)\n};\n// Order API methods\nconst orderApi = {\n    getAll: (params)=>apiClient.get(\"/orders\", params),\n    getById: (id)=>apiClient.get(`/orders/${id}`),\n    getByOrderNumber: (orderNumber)=>apiClient.get(`/orders/order-number/${orderNumber}`),\n    getByStoreId: (storeId, params)=>apiClient.get(`/orders/store/${storeId}`, params),\n    filter: (data)=>apiClient.post(\"/orders/filter\", data),\n    create: (data)=>apiClient.post(\"/orders\", data),\n    update: (id, data)=>apiClient.put(`/orders/${id}`, data),\n    delete: (id)=>apiClient.delete(`/orders/${id}`)\n};\n// Conversation API methods (using existing endpoints)\nconst conversationApi = {\n    getAll: (params)=>apiClient.get(\"/conversations\", params),\n    getById: (id)=>apiClient.get(`/conversations/${id}`),\n    getByUuid: (uuid)=>apiClient.get(`/conversations/uuid/${uuid}`),\n    getByStoreId: (storeId, params)=>apiClient.get(`/conversations/store/${storeId}`, params),\n    getByUserId: (userId, params)=>apiClient.get(`/conversations/user/${userId}`, params),\n    create: (data)=>apiClient.post(\"/conversations\", data),\n    getTimeline: (id, params)=>apiClient.get(`/conversations/${id}/timeline`, params),\n    getUnifiedTimeline: (id, params)=>apiClient.get(`/conversations/${id}/unified-timeline`, params),\n    getUnifiedTimelineByUuid: (uuid, params)=>apiClient.get(`/conversations/uuid/${uuid}/unified-timeline`, params),\n    appendMessage: (id, data)=>apiClient.post(`/conversations/${id}/messages`, data),\n    appendMessageByUuid: (uuid, data)=>apiClient.post(`/conversations/uuid/${uuid}/messages`, data)\n};\n// Image API methods\nconst imageApi = {\n    upload: (file)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(\"/images\", {\n            method: \"POST\",\n            body: formData\n        }).then((response)=>{\n            if (!response.ok) {\n                return response.json().then((errorData)=>{\n                    throw new Error(errorData.error || \"Failed to upload image\");\n                });\n            }\n            return response.json();\n        });\n    }\n};\n// Agent API methods (using existing endpoints)\nconst agentApi = {\n    getAll: (params)=>apiClient.get(\"/agents\", params),\n    getById: (id)=>apiClient.get(`/agents/${id}`),\n    create: (data)=>apiClient.post(\"/agents\", data),\n    update: (id, data)=>apiClient.put(`/agents/${id}`, data),\n    delete: (id)=>apiClient.delete(`/agents/${id}`),\n    generateMessage: (data)=>apiClient.post(\"/agents/runtime/generate-message\", data)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/api.ts\n");

/***/ }),

/***/ "./src/utils/auth.ts":
/*!***************************!*\
  !*** ./src/utils/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearClientAuthArtifacts: () => (/* binding */ clearClientAuthArtifacts),\n/* harmony export */   debugLogin: () => (/* binding */ debugLogin),\n/* harmony export */   fetchWithCredentials: () => (/* binding */ fetchWithCredentials),\n/* harmony export */   getBackendUrl: () => (/* binding */ getBackendUrl),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   handleUnauthorized: () => (/* binding */ handleUnauthorized),\n/* harmony export */   performLogout: () => (/* binding */ performLogout),\n/* harmony export */   redirectToGoogleAuth: () => (/* binding */ redirectToGoogleAuth)\n/* harmony export */ });\nconst getBackendUrl = ()=>{\n    return \"http://localhost:8000\" || 0;\n};\nconst clearClientAuthArtifacts = ()=>{\n    console.log(\"[clearClientAuthArtifacts] Clearing all auth artifacts\");\n    try {\n        // Clear local cache of user\n        if (typeof localStorage !== \"undefined\") {\n            localStorage.removeItem(\"teno:auth:user\");\n            localStorage.removeItem(\"teno:auth:token\");\n            console.log(\"[clearClientAuthArtifacts] Cleared localStorage tokens\");\n        }\n    } catch  {}\n    try {\n        // Proactively drop any readable client token if it exists\n        if (typeof document !== \"undefined\") {\n            // Expire both potential names just in case\n            document.cookie = \"access_token_client=; Path=/; Max-Age=0; SameSite=Lax\";\n            document.cookie = \"access_token=; Path=/; Max-Age=0; SameSite=Lax\";\n            console.log(\"[clearClientAuthArtifacts] Cleared cookie tokens\");\n        }\n    } catch  {}\n};\nconst handleUnauthorized = ()=>{\n    console.log(\"[handleUnauthorized] Called - clearing auth artifacts and redirecting to login\");\n    // Ensure client artifacts are cleared immediately\n    clearClientAuthArtifacts();\n    try {\n        // Notify any listeners (e.g., UI) that auth state became unauthorized\n        if (false) {}\n    } catch  {}\n    // Best-effort redirect to login preserving next path\n    try {\n        if (false) {}\n    } catch  {}\n};\nconst fetchWithCredentials = async (input, init)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...init && init.headers ? init.headers : {}\n    };\n    // Try to get token from multiple sources in order of preference\n    try {\n        if (typeof document !== \"undefined\" && !headers[\"Authorization\"]) {\n            let token;\n            // First try cookie (primary method)\n            const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);\n            if (cookieMatch) {\n                token = decodeURIComponent(cookieMatch[1]);\n                console.log(\"[fetchWithCredentials] Token found in cookie:\", token.substring(0, 20) + \"...\");\n            } else {\n                console.log(\"[fetchWithCredentials] No token found in cookie\");\n            }\n            // Fallback to localStorage if cookie not found\n            if (!token) {\n                try {\n                    token = localStorage.getItem(\"teno:auth:token\") || undefined;\n                    if (token) {\n                        console.log(\"[fetchWithCredentials] Token found in localStorage:\", token.substring(0, 20) + \"...\");\n                    } else {\n                        console.log(\"[fetchWithCredentials] No token found in localStorage\");\n                    }\n                } catch (e) {\n                    console.warn(\"Could not access localStorage:\", e);\n                }\n            }\n            if (token) {\n                headers[\"Authorization\"] = `Bearer ${token}`;\n                console.log(\"[fetchWithCredentials] Token added to Authorization header\");\n            } else {\n                console.log(\"[fetchWithCredentials] No token available for Authorization header\");\n            }\n        }\n    } catch (e) {\n        console.warn(\"Error getting auth token:\", e);\n    }\n    const response = await fetch(input, {\n        ...init,\n        credentials: \"include\",\n        headers\n    });\n    // If backend says unauthorized/forbidden, clear local auth and nudge UI\n    if (response.status === 401 || response.status === 403) {\n        handleUnauthorized();\n    }\n    return response;\n};\nconst getCurrentUser = async ()=>{\n    // Check if we have a token before making the API call\n    let hasToken = false;\n    try {\n        if (typeof document !== \"undefined\") {\n            const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);\n            if (cookieMatch) {\n                hasToken = true;\n            }\n        }\n        if (!hasToken && typeof localStorage !== \"undefined\") {\n            try {\n                const token = localStorage.getItem(\"teno:auth:token\");\n                if (token) {\n                    hasToken = true;\n                }\n            } catch (e) {\n                console.warn(\"Could not access localStorage:\", e);\n            }\n        }\n    } catch (e) {\n        console.warn(\"Error checking for token:\", e);\n    }\n    if (!hasToken) {\n        console.log(\"[getCurrentUser] No token found, skipping API call\");\n        return null;\n    }\n    const url = `${getBackendUrl()}/auth/me`;\n    console.log(\"[getCurrentUser] Making API call to:\", url);\n    const response = await fetchWithCredentials(url);\n    if (!response.ok) {\n        console.log(\"[getCurrentUser] API call failed with status:\", response.status);\n        return null;\n    }\n    const data = await response.json();\n    console.log(\"[getCurrentUser] API call successful, user:\", data.user);\n    return data.user ?? null;\n};\nconst performLogout = async ()=>{\n    const url = `${getBackendUrl()}/auth/logout`;\n    try {\n        await fetchWithCredentials(url, {\n            method: \"POST\"\n        });\n    } finally{\n        // Always clear client artifacts regardless of server response\n        clearClientAuthArtifacts();\n    }\n};\nconst redirectToGoogleAuth = (nextPath)=>{\n    let url = `${getBackendUrl()}/auth/google`;\n    try {\n        // Prefer explicit nextPath, otherwise pick it up from current URL (?next=...)\n        let nextParam = nextPath;\n        if (!nextParam && \"undefined\" !== \"undefined\") {}\n        if (nextParam) {\n            // Only allow app-internal paths starting with '/'\n            const safeNext = decodeURIComponent(nextParam);\n            if (safeNext.startsWith(\"/\")) {\n                url += `?next=${encodeURIComponent(safeNext)}`;\n            }\n        }\n    } catch  {}\n    if (false) {}\n};\nconst debugLogin = async (email, name)=>{\n    const url = `${getBackendUrl()}/auth/dev-login`;\n    console.log(\"[debugLogin] Attempting debug login for:\", email);\n    const response = await fetch(url, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email,\n            name\n        })\n    });\n    if (!response.ok) {\n        console.error(\"[debugLogin] Login failed with status:\", response.status);\n        return null;\n    }\n    const data = await response.json();\n    console.log(\"[debugLogin] Login successful, received data:\", data);\n    // Extract and store the token\n    if (data.access_token) {\n        console.log(\"[debugLogin] Storing access token\");\n        // Store in cookie\n        const isLocalhost =  false && (0);\n        const cookieOptions = isLocalhost ? `path=/; max-age=86400; samesite=lax` : `path=/; max-age=86400; secure; samesite=strict`;\n        document.cookie = `access_token_client=${encodeURIComponent(data.access_token)}; ${cookieOptions}`;\n        // Store in localStorage as backup\n        try {\n            localStorage.setItem(\"teno:auth:token\", data.access_token);\n        } catch (e) {\n            console.warn(\"[debugLogin] Could not store token in localStorage:\", e);\n        }\n        console.log(\"[debugLogin] Token stored successfully\");\n    } else {\n        console.warn(\"[debugLogin] No access_token in response\");\n    }\n    return data.user ?? null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/auth.ts\n");

/***/ }),

/***/ "./src/utils/preferences.ts":
/*!**********************************!*\
  !*** ./src/utils/preferences.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENCY_OPTIONS: () => (/* binding */ CURRENCY_OPTIONS),\n/* harmony export */   CURRENCY_SYMBOLS: () => (/* binding */ CURRENCY_SYMBOLS),\n/* harmony export */   DEFAULT_CURRENCY_BY_COUNTRY: () => (/* binding */ DEFAULT_CURRENCY_BY_COUNTRY),\n/* harmony export */   LANGUAGE_OPTIONS: () => (/* binding */ LANGUAGE_OPTIONS),\n/* harmony export */   TRANSLATIONS: () => (/* binding */ TRANSLATIONS),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   getBrowserPreferenceDefaults: () => (/* binding */ getBrowserPreferenceDefaults),\n/* harmony export */   getStoreCurrency: () => (/* binding */ getStoreCurrency),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation)\n/* harmony export */ });\nconst LANGUAGE_OPTIONS = [\n    \"en\",\n    \"es\",\n    \"fr\",\n    \"de\",\n    \"it\",\n    \"pt\",\n    \"ru\",\n    \"zh\",\n    \"ja\",\n    \"ko\",\n    \"ar\",\n    \"hi\",\n    \"bn\",\n    \"pa\",\n    \"ur\",\n    \"fa\",\n    \"tr\",\n    \"nl\",\n    \"sv\",\n    \"no\",\n    \"da\",\n    \"fi\",\n    \"pl\",\n    \"cs\",\n    \"sk\",\n    \"hu\",\n    \"ro\",\n    \"bg\",\n    \"hr\",\n    \"sl\"\n];\nconst CURRENCY_OPTIONS = [\n    \"USD\",\n    \"EUR\",\n    \"DZD\",\n    \"GBP\",\n    \"CAD\",\n    \"AUD\",\n    \"NZD\",\n    \"JPY\",\n    \"CNY\",\n    \"HKD\",\n    \"SGD\",\n    \"INR\",\n    \"BRL\",\n    \"MXN\",\n    \"ZAR\",\n    \"SEK\",\n    \"NOK\",\n    \"DKK\",\n    \"CHF\",\n    \"PLN\",\n    \"CZK\",\n    \"HUF\",\n    \"ILS\",\n    \"TRY\",\n    \"AED\",\n    \"SAR\",\n    \"QAR\",\n    \"KWD\",\n    \"BHD\",\n    \"OMR\",\n    \"EGP\",\n    \"NGN\",\n    \"KES\",\n    \"ARS\",\n    \"CLP\",\n    \"COP\",\n    \"PEN\",\n    \"UYU\",\n    \"KRW\",\n    \"THB\",\n    \"MYR\",\n    \"PHP\",\n    \"IDR\"\n];\nconst CURRENCY_SYMBOLS = {\n    \"USD\": \"$\",\n    \"EUR\": \"€\",\n    \"DZD\": \"DZD\",\n    \"GBP\": \"\\xa3\",\n    \"CAD\": \"C$\",\n    \"AUD\": \"A$\",\n    \"NZD\": \"NZ$\",\n    \"JPY\": \"\\xa5\",\n    \"CNY\": \"\\xa5\",\n    \"HKD\": \"HK$\",\n    \"SGD\": \"S$\",\n    \"INR\": \"₹\",\n    \"BRL\": \"R$\",\n    \"MXN\": \"$\",\n    \"ZAR\": \"R\",\n    \"SEK\": \"kr\",\n    \"NOK\": \"kr\",\n    \"DKK\": \"kr\",\n    \"CHF\": \"CHF\",\n    \"PLN\": \"PLN\",\n    \"CZK\": \"CZK\",\n    \"HUF\": \"HUF\",\n    \"ILS\": \"₪\",\n    \"TRY\": \"₺\",\n    \"AED\": \"AED\",\n    \"SAR\": \"SAR\",\n    \"QAR\": \"QAR\",\n    \"KWD\": \"KWD\",\n    \"BHD\": \"BHD\",\n    \"OMR\": \"OMR\",\n    \"EGP\": \"EGP\",\n    \"NGN\": \"NGN\",\n    \"KES\": \"KES\",\n    \"ARS\": \"ARS\",\n    \"CLP\": \"CLP\",\n    \"COP\": \"COP\",\n    \"PEN\": \"PEN\",\n    \"UYU\": \"UYU\",\n    \"KRW\": \"₩\",\n    \"THB\": \"฿\",\n    \"MYR\": \"RM\",\n    \"PHP\": \"₱\",\n    \"IDR\": \"IDR\"\n};\nconst DEFAULT_CURRENCY_BY_COUNTRY = {\n    US: \"USD\",\n    GB: \"GBP\",\n    CA: \"CAD\",\n    AU: \"AUD\",\n    NZ: \"NZD\",\n    JP: \"JPY\",\n    CN: \"CNY\",\n    HK: \"HKD\",\n    SG: \"SGD\",\n    IN: \"INR\",\n    BR: \"BRL\",\n    MX: \"MXN\",\n    ZA: \"ZAR\",\n    SE: \"SEK\",\n    NO: \"NOK\",\n    DK: \"DKK\",\n    CH: \"CHF\",\n    PL: \"PLN\",\n    CZ: \"CZK\",\n    HU: \"HUF\",\n    IL: \"ILS\",\n    TR: \"TRY\",\n    AE: \"AED\",\n    SA: \"SAR\",\n    DZ: \"DZD\",\n    DE: \"EUR\",\n    FR: \"EUR\",\n    ES: \"EUR\",\n    IT: \"EUR\",\n    PT: \"EUR\"\n};\nfunction getBrowserPreferenceDefaults() {\n    let language = \"en\";\n    let country = \"US\";\n    try {\n        if (typeof navigator !== \"undefined\") {\n            const browserLang = navigator.language || \"en\";\n            // Convert browser language to supported format (e.g., 'en-US' -> 'en')\n            language = browserLang.split(\"-\")[0];\n            if (browserLang.split(\"-\").length > 1) country = browserLang.split(\"-\")[1].toUpperCase();\n        }\n    } catch  {}\n    const currency = DEFAULT_CURRENCY_BY_COUNTRY[country] || \"USD\";\n    return {\n        language,\n        country,\n        currency\n    };\n}\nfunction formatCurrency(amount, currency = \"USD\") {\n    const symbol = CURRENCY_SYMBOLS[currency] || currency;\n    return `${symbol}${amount.toLocaleString(\"en-US\", {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    })}`;\n}\n// Simple translation system for supported languages\nconst TRANSLATIONS = {\n    \"en\": {\n        \"storeDirectory\": \"Store Directory\",\n        \"browseStores\": \"Browse available stores and their products\",\n        \"visitStore\": \"Visit Store\",\n        \"shoppingCart\": \"Shopping Cart\",\n        \"addToCart\": \"Add to Cart\",\n        \"proceedToCheckout\": \"Proceed to Checkout\",\n        \"loading\": \"Loading...\",\n        \"noProducts\": \"No products available in this store.\",\n        \"howItWorks\": \"How it works\",\n        \"browseAndShop\": \"Browse stores, view their products, and add items to your cart. No account required - start shopping immediately!\"\n    },\n    \"fr\": {\n        \"storeDirectory\": \"R\\xe9pertoire des Magasins\",\n        \"browseStores\": \"Parcourez les magasins disponibles et leurs produits\",\n        \"visitStore\": \"Visiter le Magasin\",\n        \"shoppingCart\": \"Panier d'Achats\",\n        \"addToCart\": \"Ajouter au Panier\",\n        \"proceedToCheckout\": \"Proc\\xe9der au Paiement\",\n        \"loading\": \"Chargement...\",\n        \"noProducts\": \"Aucun produit disponible dans ce magasin.\",\n        \"howItWorks\": \"Comment \\xe7a marche\",\n        \"browseAndShop\": \"Parcourez les magasins, consultez leurs produits et ajoutez des articles \\xe0 votre panier. Aucun compte requis - commencez \\xe0 faire vos achats imm\\xe9diatement !\"\n    },\n    \"ar\": {\n        \"storeDirectory\": \"دليل المتاجر\",\n        \"browseStores\": \"تصفح المتاجر المتاحة ومنتجاتها\",\n        \"visitStore\": \"زيارة المتجر\",\n        \"shoppingCart\": \"سلة التسوق\",\n        \"addToCart\": \"إضافة إلى السلة\",\n        \"proceedToCheckout\": \"المتابعة للدفع\",\n        \"loading\": \"جاري التحميل...\",\n        \"noProducts\": \"لا توجد منتجات متاحة في هذا المتجر.\",\n        \"howItWorks\": \"كيف يعمل\",\n        \"browseAndShop\": \"تصفح المتاجر، عرض منتجاتها، وإضافة العناصر إلى سلة التسوق الخاصة بك. لا حاجة لحساب - ابدأ التسوق فوراً!\"\n    }\n};\nfunction getTranslation(key, language = \"en\") {\n    return TRANSLATIONS[language]?.[key] || TRANSLATIONS[\"en\"][key] || key;\n}\n// Get store currency - currently defaults to USD, but can be easily updated\n// to use store-specific currency when that field is added to the Store model\nfunction getStoreCurrency(store) {\n    // TODO: When store.currency field is added, use: return store?.currency || 'USD';\n    return \"USD\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvcHJlZmVyZW5jZXMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBSU8sTUFBTUEsbUJBQXdDO0lBQ3BEO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtDQUM5SyxDQUFDO0FBRUssTUFBTUMsbUJBQXdDO0lBQ3BEO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0NBQzVQLENBQUM7QUFFSyxNQUFNQyxtQkFBc0Q7SUFDbEUsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztBQUNSLEVBQUU7QUFFSyxNQUFNQyw4QkFBaUU7SUFDN0VDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0FBQ0wsRUFBRTtBQUVLLFNBQVNDO0lBQ2YsSUFBSUMsV0FBOEI7SUFDbEMsSUFBSUMsVUFBdUI7SUFDM0IsSUFBSTtRQUNILElBQUksT0FBT0MsY0FBYyxhQUFhO1lBQ3JDLE1BQU1DLGNBQWNELFVBQVVGLFFBQVEsSUFBSTtZQUMxQyx1RUFBdUU7WUFDdkVBLFdBQVdHLFlBQVlDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUNwQyxJQUFJRCxZQUFZQyxLQUFLLENBQUMsS0FBS0MsTUFBTSxHQUFHLEdBQUdKLFVBQVVFLFlBQVlDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDRSxXQUFXO1FBQ3ZGO0lBQ0QsRUFBRSxPQUFNLENBQUM7SUFDVCxNQUFNQyxXQUFXdkMsMkJBQTJCLENBQUNpQyxRQUFRLElBQUk7SUFDekQsT0FBTztRQUFFRDtRQUFVQztRQUFTTTtJQUFTO0FBQ3RDO0FBRU8sU0FBU0MsZUFBZUMsTUFBYyxFQUFFRixXQUE4QixLQUFLO0lBQ2pGLE1BQU1HLFNBQVMzQyxnQkFBZ0IsQ0FBQ3dDLFNBQVMsSUFBSUE7SUFFN0MsT0FBTyxDQUFDLEVBQUVHLE9BQU8sRUFBRUQsT0FBT0UsY0FBYyxDQUFDLFNBQVM7UUFDakRDLHVCQUF1QjtRQUN2QkMsdUJBQXVCO0lBQ3hCLEdBQUcsQ0FBQztBQUNMO0FBRUEsb0RBQW9EO0FBQzdDLE1BQU1DLGVBQWU7SUFDM0IsTUFBTTtRQUNMLGtCQUFrQjtRQUNsQixnQkFBZ0I7UUFDaEIsY0FBYztRQUNkLGdCQUFnQjtRQUNoQixhQUFhO1FBQ2IscUJBQXFCO1FBQ3JCLFdBQVc7UUFDWCxjQUFjO1FBQ2QsY0FBYztRQUNkLGlCQUFpQjtJQUNsQjtJQUNBLE1BQU07UUFDTCxrQkFBa0I7UUFDbEIsZ0JBQWdCO1FBQ2hCLGNBQWM7UUFDZCxnQkFBZ0I7UUFDaEIsYUFBYTtRQUNiLHFCQUFxQjtRQUNyQixXQUFXO1FBQ1gsY0FBYztRQUNkLGNBQWM7UUFDZCxpQkFBaUI7SUFDbEI7SUFDQSxNQUFNO1FBQ0wsa0JBQWtCO1FBQ2xCLGdCQUFnQjtRQUNoQixjQUFjO1FBQ2QsZ0JBQWdCO1FBQ2hCLGFBQWE7UUFDYixxQkFBcUI7UUFDckIsV0FBVztRQUNYLGNBQWM7UUFDZCxjQUFjO1FBQ2QsaUJBQWlCO0lBQ2xCO0FBQ0QsRUFBRTtBQUVLLFNBQVNDLGVBQWVDLEdBQW9DLEVBQUVoQixXQUE4QixJQUFJO0lBQzlGLE9BQU9jLFlBQVksQ0FBQ2QsU0FBc0MsRUFBRSxDQUFDZ0IsSUFBSSxJQUFJRixZQUFZLENBQUMsS0FBSyxDQUFDRSxJQUFJLElBQUlBO0FBQ3pHO0FBRUEsNEVBQTRFO0FBQzVFLDZFQUE2RTtBQUN0RSxTQUFTQyxpQkFBaUJDLEtBQVc7SUFDM0Msa0ZBQWtGO0lBQ2xGLE9BQU87QUFDUiIsInNvdXJjZXMiOlsid2VicGFjazovL3Rlbm8tc3RvcmUtZnJvbnRlbmQvLi9zcmMvdXRpbHMvcHJlZmVyZW5jZXMudHM/ZjMwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdHlwZSBTdXBwb3J0ZWRMYW5ndWFnZSA9IHN0cmluZztcbmV4cG9ydCB0eXBlIFN1cHBvcnRlZEN1cnJlbmN5ID0gc3RyaW5nO1xuZXhwb3J0IHR5cGUgQ291bnRyeUNvZGUgPSBzdHJpbmc7XG5cbmV4cG9ydCBjb25zdCBMQU5HVUFHRV9PUFRJT05TOiBTdXBwb3J0ZWRMYW5ndWFnZVtdID0gW1xuXHQnZW4nLCAnZXMnLCAnZnInLCAnZGUnLCAnaXQnLCAncHQnLCAncnUnLCAnemgnLCAnamEnLCAna28nLCAnYXInLCAnaGknLCAnYm4nLCAncGEnLCAndXInLCAnZmEnLCAndHInLCAnbmwnLCAnc3YnLCAnbm8nLCAnZGEnLCAnZmknLCAncGwnLCAnY3MnLCAnc2snLCAnaHUnLCAncm8nLCAnYmcnLCAnaHInLCAnc2wnXG5dO1xuXG5leHBvcnQgY29uc3QgQ1VSUkVOQ1lfT1BUSU9OUzogU3VwcG9ydGVkQ3VycmVuY3lbXSA9IFtcblx0J1VTRCcsJ0VVUicsJ0RaRCcsJ0dCUCcsJ0NBRCcsJ0FVRCcsJ05aRCcsJ0pQWScsJ0NOWScsJ0hLRCcsJ1NHRCcsJ0lOUicsJ0JSTCcsJ01YTicsJ1pBUicsJ1NFSycsJ05PSycsJ0RLSycsJ0NIRicsJ1BMTicsJ0NaSycsJ0hVRicsJ0lMUycsJ1RSWScsJ0FFRCcsJ1NBUicsJ1FBUicsJ0tXRCcsJ0JIRCcsJ09NUicsJ0VHUCcsJ05HTicsJ0tFUycsJ0FSUycsJ0NMUCcsJ0NPUCcsJ1BFTicsJ1VZVScsJ0tSVycsJ1RIQicsJ01ZUicsJ1BIUCcsJ0lEUidcbl07XG5cbmV4cG9ydCBjb25zdCBDVVJSRU5DWV9TWU1CT0xTOiBSZWNvcmQ8U3VwcG9ydGVkQ3VycmVuY3ksIHN0cmluZz4gPSB7XG5cdCdVU0QnOiAnJCcsXG5cdCdFVVInOiAn4oKsJyxcblx0J0RaRCc6ICdEWkQnLFxuXHQnR0JQJzogJ8KjJyxcblx0J0NBRCc6ICdDJCcsXG5cdCdBVUQnOiAnQSQnLFxuXHQnTlpEJzogJ05aJCcsXG5cdCdKUFknOiAnwqUnLFxuXHQnQ05ZJzogJ8KlJyxcblx0J0hLRCc6ICdISyQnLFxuXHQnU0dEJzogJ1MkJyxcblx0J0lOUic6ICfigrknLFxuXHQnQlJMJzogJ1IkJyxcblx0J01YTic6ICckJyxcblx0J1pBUic6ICdSJyxcblx0J1NFSyc6ICdrcicsXG5cdCdOT0snOiAna3InLFxuXHQnREtLJzogJ2tyJyxcblx0J0NIRic6ICdDSEYnLFxuXHQnUExOJzogJ1BMTicsXG5cdCdDWksnOiAnQ1pLJyxcblx0J0hVRic6ICdIVUYnLFxuXHQnSUxTJzogJ+KCqicsXG5cdCdUUlknOiAn4oK6Jyxcblx0J0FFRCc6ICdBRUQnLFxuXHQnU0FSJzogJ1NBUicsXG5cdCdRQVInOiAnUUFSJyxcblx0J0tXRCc6ICdLV0QnLFxuXHQnQkhEJzogJ0JIRCcsXG5cdCdPTVInOiAnT01SJyxcblx0J0VHUCc6ICdFR1AnLFxuXHQnTkdOJzogJ05HTicsXG5cdCdLRVMnOiAnS0VTJyxcblx0J0FSUyc6ICdBUlMnLFxuXHQnQ0xQJzogJ0NMUCcsXG5cdCdDT1AnOiAnQ09QJyxcblx0J1BFTic6ICdQRU4nLFxuXHQnVVlVJzogJ1VZVScsXG5cdCdLUlcnOiAn4oKpJyxcblx0J1RIQic6ICfguL8nLFxuXHQnTVlSJzogJ1JNJyxcblx0J1BIUCc6ICfigrEnLFxuXHQnSURSJzogJ0lEUicsXG59O1xuXG5leHBvcnQgY29uc3QgREVGQVVMVF9DVVJSRU5DWV9CWV9DT1VOVFJZOiBSZWNvcmQ8c3RyaW5nLCBTdXBwb3J0ZWRDdXJyZW5jeT4gPSB7XG5cdFVTOiAnVVNEJyxcblx0R0I6ICdHQlAnLFxuXHRDQTogJ0NBRCcsXG5cdEFVOiAnQVVEJyxcblx0Tlo6ICdOWkQnLFxuXHRKUDogJ0pQWScsXG5cdENOOiAnQ05ZJyxcblx0SEs6ICdIS0QnLFxuXHRTRzogJ1NHRCcsXG5cdElOOiAnSU5SJyxcblx0QlI6ICdCUkwnLFxuXHRNWDogJ01YTicsXG5cdFpBOiAnWkFSJyxcblx0U0U6ICdTRUsnLFxuXHROTzogJ05PSycsXG5cdERLOiAnREtLJyxcblx0Q0g6ICdDSEYnLFxuXHRQTDogJ1BMTicsXG5cdENaOiAnQ1pLJyxcblx0SFU6ICdIVUYnLFxuXHRJTDogJ0lMUycsXG5cdFRSOiAnVFJZJyxcblx0QUU6ICdBRUQnLFxuXHRTQTogJ1NBUicsXG5cdERaOiAnRFpEJyxcblx0REU6ICdFVVInLFxuXHRGUjogJ0VVUicsXG5cdEVTOiAnRVVSJyxcblx0SVQ6ICdFVVInLFxuXHRQVDogJ0VVUicsXG59O1xuXG5leHBvcnQgZnVuY3Rpb24gZ2V0QnJvd3NlclByZWZlcmVuY2VEZWZhdWx0cygpOiB7IGxhbmd1YWdlOiBTdXBwb3J0ZWRMYW5ndWFnZTsgY291bnRyeTogQ291bnRyeUNvZGU7IGN1cnJlbmN5OiBTdXBwb3J0ZWRDdXJyZW5jeSB9IHtcblx0bGV0IGxhbmd1YWdlOiBTdXBwb3J0ZWRMYW5ndWFnZSA9ICdlbic7XG5cdGxldCBjb3VudHJ5OiBDb3VudHJ5Q29kZSA9ICdVUyc7XG5cdHRyeSB7XG5cdFx0aWYgKHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnKSB7XG5cdFx0XHRjb25zdCBicm93c2VyTGFuZyA9IG5hdmlnYXRvci5sYW5ndWFnZSB8fCAnZW4nO1xuXHRcdFx0Ly8gQ29udmVydCBicm93c2VyIGxhbmd1YWdlIHRvIHN1cHBvcnRlZCBmb3JtYXQgKGUuZy4sICdlbi1VUycgLT4gJ2VuJylcblx0XHRcdGxhbmd1YWdlID0gYnJvd3Nlckxhbmcuc3BsaXQoJy0nKVswXSBhcyBTdXBwb3J0ZWRMYW5ndWFnZTtcblx0XHRcdGlmIChicm93c2VyTGFuZy5zcGxpdCgnLScpLmxlbmd0aCA+IDEpIGNvdW50cnkgPSBicm93c2VyTGFuZy5zcGxpdCgnLScpWzFdLnRvVXBwZXJDYXNlKCk7XG5cdFx0fVxuXHR9IGNhdGNoIHt9XG5cdGNvbnN0IGN1cnJlbmN5ID0gREVGQVVMVF9DVVJSRU5DWV9CWV9DT1VOVFJZW2NvdW50cnldIHx8ICdVU0QnO1xuXHRyZXR1cm4geyBsYW5ndWFnZSwgY291bnRyeSwgY3VycmVuY3kgfTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEN1cnJlbmN5KGFtb3VudDogbnVtYmVyLCBjdXJyZW5jeTogU3VwcG9ydGVkQ3VycmVuY3kgPSAnVVNEJyk6IHN0cmluZyB7XG5cdGNvbnN0IHN5bWJvbCA9IENVUlJFTkNZX1NZTUJPTFNbY3VycmVuY3ldIHx8IGN1cnJlbmN5O1xuXG5cdHJldHVybiBgJHtzeW1ib2x9JHthbW91bnQudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJywge1xuXHRcdG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMixcblx0XHRtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDIsXG5cdH0pfWA7XG59XG5cbi8vIFNpbXBsZSB0cmFuc2xhdGlvbiBzeXN0ZW0gZm9yIHN1cHBvcnRlZCBsYW5ndWFnZXNcbmV4cG9ydCBjb25zdCBUUkFOU0xBVElPTlMgPSB7XG5cdCdlbic6IHtcblx0XHQnc3RvcmVEaXJlY3RvcnknOiAnU3RvcmUgRGlyZWN0b3J5Jyxcblx0XHQnYnJvd3NlU3RvcmVzJzogJ0Jyb3dzZSBhdmFpbGFibGUgc3RvcmVzIGFuZCB0aGVpciBwcm9kdWN0cycsXG5cdFx0J3Zpc2l0U3RvcmUnOiAnVmlzaXQgU3RvcmUnLFxuXHRcdCdzaG9wcGluZ0NhcnQnOiAnU2hvcHBpbmcgQ2FydCcsXG5cdFx0J2FkZFRvQ2FydCc6ICdBZGQgdG8gQ2FydCcsXG5cdFx0J3Byb2NlZWRUb0NoZWNrb3V0JzogJ1Byb2NlZWQgdG8gQ2hlY2tvdXQnLFxuXHRcdCdsb2FkaW5nJzogJ0xvYWRpbmcuLi4nLFxuXHRcdCdub1Byb2R1Y3RzJzogJ05vIHByb2R1Y3RzIGF2YWlsYWJsZSBpbiB0aGlzIHN0b3JlLicsXG5cdFx0J2hvd0l0V29ya3MnOiAnSG93IGl0IHdvcmtzJyxcblx0XHQnYnJvd3NlQW5kU2hvcCc6ICdCcm93c2Ugc3RvcmVzLCB2aWV3IHRoZWlyIHByb2R1Y3RzLCBhbmQgYWRkIGl0ZW1zIHRvIHlvdXIgY2FydC4gTm8gYWNjb3VudCByZXF1aXJlZCAtIHN0YXJ0IHNob3BwaW5nIGltbWVkaWF0ZWx5ISdcblx0fSxcblx0J2ZyJzoge1xuXHRcdCdzdG9yZURpcmVjdG9yeSc6ICdSw6lwZXJ0b2lyZSBkZXMgTWFnYXNpbnMnLFxuXHRcdCdicm93c2VTdG9yZXMnOiAnUGFyY291cmV6IGxlcyBtYWdhc2lucyBkaXNwb25pYmxlcyBldCBsZXVycyBwcm9kdWl0cycsXG5cdFx0J3Zpc2l0U3RvcmUnOiAnVmlzaXRlciBsZSBNYWdhc2luJyxcblx0XHQnc2hvcHBpbmdDYXJ0JzogJ1BhbmllciBkXFwnQWNoYXRzJyxcblx0XHQnYWRkVG9DYXJ0JzogJ0Fqb3V0ZXIgYXUgUGFuaWVyJyxcblx0XHQncHJvY2VlZFRvQ2hlY2tvdXQnOiAnUHJvY8OpZGVyIGF1IFBhaWVtZW50Jyxcblx0XHQnbG9hZGluZyc6ICdDaGFyZ2VtZW50Li4uJyxcblx0XHQnbm9Qcm9kdWN0cyc6ICdBdWN1biBwcm9kdWl0IGRpc3BvbmlibGUgZGFucyBjZSBtYWdhc2luLicsXG5cdFx0J2hvd0l0V29ya3MnOiAnQ29tbWVudCDDp2EgbWFyY2hlJyxcblx0XHQnYnJvd3NlQW5kU2hvcCc6ICdQYXJjb3VyZXogbGVzIG1hZ2FzaW5zLCBjb25zdWx0ZXogbGV1cnMgcHJvZHVpdHMgZXQgYWpvdXRleiBkZXMgYXJ0aWNsZXMgw6Agdm90cmUgcGFuaWVyLiBBdWN1biBjb21wdGUgcmVxdWlzIC0gY29tbWVuY2V6IMOgIGZhaXJlIHZvcyBhY2hhdHMgaW1tw6lkaWF0ZW1lbnQgISdcblx0fSxcblx0J2FyJzoge1xuXHRcdCdzdG9yZURpcmVjdG9yeSc6ICfYr9mE2YrZhCDYp9mE2YXYqtin2KzYsScsXG5cdFx0J2Jyb3dzZVN0b3Jlcyc6ICfYqti12YHYrSDYp9mE2YXYqtin2KzYsSDYp9mE2YXYqtin2K3YqSDZiNmF2YbYqtis2KfYqtmH2KcnLFxuXHRcdCd2aXNpdFN0b3JlJzogJ9iy2YrYp9ix2Kkg2KfZhNmF2KrYrNixJyxcblx0XHQnc2hvcHBpbmdDYXJ0JzogJ9iz2YTYqSDYp9mE2KrYs9mI2YInLFxuXHRcdCdhZGRUb0NhcnQnOiAn2KXYttin2YHYqSDYpdmE2Ykg2KfZhNiz2YTYqScsXG5cdFx0J3Byb2NlZWRUb0NoZWNrb3V0JzogJ9in2YTZhdiq2KfYqNi52Kkg2YTZhNiv2YHYuScsXG5cdFx0J2xvYWRpbmcnOiAn2KzYp9ix2Yog2KfZhNiq2K3ZhdmK2YQuLi4nLFxuXHRcdCdub1Byb2R1Y3RzJzogJ9mE2Kcg2KrZiNis2K8g2YXZhtiq2KzYp9iqINmF2KrYp9it2Kkg2YHZiiDZh9iw2Kcg2KfZhNmF2KrYrNixLicsXG5cdFx0J2hvd0l0V29ya3MnOiAn2YPZitmBINmK2LnZhdmEJyxcblx0XHQnYnJvd3NlQW5kU2hvcCc6ICfYqti12YHYrSDYp9mE2YXYqtin2KzYsdiMINi52LHYtiDZhdmG2KrYrNin2KrZh9in2Iwg2YjYpdi22KfZgdipINin2YTYudmG2KfYtdixINil2YTZiSDYs9mE2Kkg2KfZhNiq2LPZiNmCINin2YTYrtin2LXYqSDYqNmDLiDZhNinINit2KfYrNipINmE2K3Ys9in2KggLSDYp9io2K/YoyDYp9mE2KrYs9mI2YIg2YHZiNix2KfZiyEnXG5cdH1cbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRUcmFuc2xhdGlvbihrZXk6IGtleW9mIHR5cGVvZiBUUkFOU0xBVElPTlNbJ2VuJ10sIGxhbmd1YWdlOiBTdXBwb3J0ZWRMYW5ndWFnZSA9ICdlbicpOiBzdHJpbmcge1xuXHQgICAgICAgIHJldHVybiBUUkFOU0xBVElPTlNbbGFuZ3VhZ2UgYXMga2V5b2YgdHlwZW9mIFRSQU5TTEFUSU9OU10/LltrZXldIHx8IFRSQU5TTEFUSU9OU1snZW4nXVtrZXldIHx8IGtleTtcbn1cblxuLy8gR2V0IHN0b3JlIGN1cnJlbmN5IC0gY3VycmVudGx5IGRlZmF1bHRzIHRvIFVTRCwgYnV0IGNhbiBiZSBlYXNpbHkgdXBkYXRlZFxuLy8gdG8gdXNlIHN0b3JlLXNwZWNpZmljIGN1cnJlbmN5IHdoZW4gdGhhdCBmaWVsZCBpcyBhZGRlZCB0byB0aGUgU3RvcmUgbW9kZWxcbmV4cG9ydCBmdW5jdGlvbiBnZXRTdG9yZUN1cnJlbmN5KHN0b3JlPzogYW55KTogU3VwcG9ydGVkQ3VycmVuY3kge1xuXHQvLyBUT0RPOiBXaGVuIHN0b3JlLmN1cnJlbmN5IGZpZWxkIGlzIGFkZGVkLCB1c2U6IHJldHVybiBzdG9yZT8uY3VycmVuY3kgfHwgJ1VTRCc7XG5cdHJldHVybiAnVVNEJztcbn1cblxuXG4iXSwibmFtZXMiOlsiTEFOR1VBR0VfT1BUSU9OUyIsIkNVUlJFTkNZX09QVElPTlMiLCJDVVJSRU5DWV9TWU1CT0xTIiwiREVGQVVMVF9DVVJSRU5DWV9CWV9DT1VOVFJZIiwiVVMiLCJHQiIsIkNBIiwiQVUiLCJOWiIsIkpQIiwiQ04iLCJISyIsIlNHIiwiSU4iLCJCUiIsIk1YIiwiWkEiLCJTRSIsIk5PIiwiREsiLCJDSCIsIlBMIiwiQ1oiLCJIVSIsIklMIiwiVFIiLCJBRSIsIlNBIiwiRFoiLCJERSIsIkZSIiwiRVMiLCJJVCIsIlBUIiwiZ2V0QnJvd3NlclByZWZlcmVuY2VEZWZhdWx0cyIsImxhbmd1YWdlIiwiY291bnRyeSIsIm5hdmlnYXRvciIsImJyb3dzZXJMYW5nIiwic3BsaXQiLCJsZW5ndGgiLCJ0b1VwcGVyQ2FzZSIsImN1cnJlbmN5IiwiZm9ybWF0Q3VycmVuY3kiLCJhbW91bnQiLCJzeW1ib2wiLCJ0b0xvY2FsZVN0cmluZyIsIm1pbmltdW1GcmFjdGlvbkRpZ2l0cyIsIm1heGltdW1GcmFjdGlvbkRpZ2l0cyIsIlRSQU5TTEFUSU9OUyIsImdldFRyYW5zbGF0aW9uIiwia2V5IiwiZ2V0U3RvcmVDdXJyZW5jeSIsInN0b3JlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/utils/preferences.ts\n");

/***/ }),

/***/ "./src/utils/useAuthGuard.ts":
/*!***********************************!*\
  !*** ./src/utils/useAuthGuard.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthGuard: () => (/* binding */ useAuthGuard)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n\n\n\n\n// Routes that don't require authentication\nconst PUBLIC_ROUTES = [\n    \"/login\",\n    \"/auth/callback\",\n    \"/setup/store\"\n];\nfunction useAuthGuard() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, isLoading, error } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { currentStoreId, setCurrentStoreId } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const [storesData, setStoresData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isStoresLoading, setIsStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const handledCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const isRedirectingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isPublic = (path)=>PUBLIC_ROUTES.some((route)=>path.startsWith(route));\n    // Fetch stores data for the current user\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!user) return;\n        const fetchStores = async ()=>{\n            setIsStoresLoading(true);\n            try {\n                // Import the store API dynamically to avoid circular dependencies\n                const { storeApi } = await __webpack_require__.e(/*! import() */ \"src_utils_api_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../utils/api */ \"./src/utils/api.ts\"));\n                const result = await storeApi.getByUserId(user.id, {\n                    page: 1,\n                    limit: 100\n                });\n                setStoresData(result);\n            } catch (error) {\n                console.error(\"Error fetching stores:\", error);\n                setStoresData({\n                    data: [],\n                    meta: {\n                        total: 0\n                    }\n                });\n            } finally{\n                setIsStoresLoading(false);\n            }\n        };\n        fetchStores();\n    }, [\n        user\n    ]);\n    // Main auth guard logic\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Add a small delay to allow auth state to stabilize after token storage\n        const timeoutId = setTimeout(()=>{\n            // Avoid redirecting while we are still determining auth state\n            if (isLoading) return;\n            const currentPath = router.pathname;\n            const asPath = router.asPath;\n            const fullPath = asPath || currentPath;\n            const searchParams = new URLSearchParams( false ? 0 : \"\");\n            const loggedOutFlag = searchParams.get(\"loggedOut\");\n            // Ensure we only handle/log once per path after loading has completed\n            if (handledCache.current[currentPath]) {\n                return;\n            }\n            console.log(\"[AuthGuard] route/useEffect fired\", {\n                pathname: currentPath,\n                asPath: router.asPath,\n                isLoading,\n                hasUser: !!user,\n                error\n            });\n            if (!user && !isPublic(currentPath)) {\n                if (isRedirectingRef.current) {\n                    console.log(\"[AuthGuard] Redirect already in progress; skipping\");\n                    return;\n                }\n                isRedirectingRef.current = true;\n                const nextParam = encodeURIComponent(fullPath || \"/\");\n                const loginUrl = `/login?next=${nextParam}`;\n                console.log(\"[AuthGuard] Not authenticated; redirecting to\", loginUrl);\n                router.replace(loginUrl).finally(()=>{\n                    setTimeout(()=>{\n                        isRedirectingRef.current = false;\n                    }, 0);\n                });\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (isPublic(currentPath) && currentPath === \"/login\" && loggedOutFlag === \"1\") {\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && isPublic(currentPath) && currentPath === \"/login\") {\n                if (isStoresLoading) {\n                    return;\n                }\n                if (storesData === null) {\n                    return;\n                }\n                if (!isRedirectingRef.current) {\n                    isRedirectingRef.current = true;\n                    let target = \"/dashboard\";\n                    try {\n                        const nextParam = searchParams.get(\"next\") || undefined;\n                        const totalActive = storesData?.meta?.total ?? storesData?.data?.length ?? 0;\n                        if (nextParam && nextParam.startsWith(\"/\")) {\n                            target = nextParam;\n                        } else {\n                            if (totalActive === 0) {\n                                target = \"/setup/store\";\n                            }\n                        }\n                    } catch  {}\n                    console.log(\"[AuthGuard] Already authenticated; redirecting away from public auth page to\", target);\n                    router.replace(target).finally(()=>{\n                        setTimeout(()=>{\n                            isRedirectingRef.current = false;\n                        }, 0);\n                    });\n                }\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && !isPublic(currentPath)) {\n                if (storesData === null) {\n                    console.log(\"[AuthGuard] Stores data not yet fetched, waiting...\");\n                    return;\n                }\n                const totalActive = storesData?.meta?.total ?? storesData?.data?.length ?? 0;\n                console.log(\"[AuthGuard] Store check:\", {\n                    currentPath,\n                    totalActive,\n                    storesData,\n                    isStoresLoading,\n                    loggedOutFlag\n                });\n                if (!isStoresLoading && totalActive === 0 && currentPath !== \"/setup/store\" && loggedOutFlag !== \"1\") {\n                    if (!isRedirectingRef.current) {\n                        isRedirectingRef.current = true;\n                        console.log(\"[AuthGuard] Authenticated user without active stores; redirecting to /setup/store\");\n                        router.replace(\"/setup/store\").finally(()=>{\n                            setTimeout(()=>{\n                                isRedirectingRef.current = false;\n                            }, 0);\n                        });\n                        handledCache.current[currentPath] = true;\n                        return;\n                    }\n                } else if (totalActive > 0 && !currentStoreId) {\n                    const firstId = storesData?.data?.[0]?.id;\n                    if (firstId) setCurrentStoreId(String(firstId));\n                }\n            }\n            if (!(user && !isPublic(currentPath) && isStoresLoading)) {\n                handledCache.current[currentPath] = true;\n            }\n        }, 100); // 100ms delay to allow auth state to stabilize\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        router.pathname,\n        isLoading,\n        user,\n        error,\n        isStoresLoading,\n        storesData,\n        currentStoreId,\n        router,\n        setCurrentStoreId\n    ]);\n    return {\n        user,\n        isLoading,\n        error,\n        storesData,\n        isStoresLoading,\n        currentStoreId\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/useAuthGuard.ts\n");

/***/ }),

/***/ "./src/components/EntityTable.fadein.css":
/*!***********************************************!*\
  !*** ./src/components/EntityTable.fadein.css ***!
  \***********************************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-jsx/style");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flive%2F%5Buuid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Clive%5C%5Buuid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();