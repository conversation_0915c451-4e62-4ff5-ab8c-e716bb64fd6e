import axios from 'axios';
import * as cheerio from 'cheerio';
import { Agent } from '../src/agent';
import { createTool } from '../src/tools';

/**
 * Tool for web scraping
 */
const webSearch = createTool(
  async function webSearch(url: string): Promise<string> {
    try {
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const $ = cheerio.load(response.data);
      
      // Remove script and style elements
      $('script, style').remove();
      
      // Get text content
      const text = $('body').text().replace(/\s+/g, ' ').trim();
      
      // Limit to first 2000 characters
      return text.substring(0, 2000);
    } catch (error) {
      return `Error fetching ${url}: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
  {
    description: 'Search and scrape content from a website URL',
    parameterTypes: {
      url: String
    },
    requiredParams: ['url']
  }
);

/**
 * Tool for getting current date/time
 */
const getCurrentTime = createTool(
  function getCurrentTime(): string {
    return new Date().toISOString();
  },
  {
    description: 'Get the current date and time in ISO format'
  }
);

/**
 * Create an information agent with web search capabilities
 */
export function createInfoAgent(): Agent {
  return new Agent(
    'InfoAgent',
    `You are a helpful information assistant. You can search the web for current information and answer questions.
    
    Guidelines:
    - Always provide accurate and up-to-date information
    - Cite your sources when using web search
    - If you can't find information, say so clearly
    - Be concise but thorough in your responses`,
    [webSearch, getCurrentTime],
    'Information research and web search assistant'
  );
}
