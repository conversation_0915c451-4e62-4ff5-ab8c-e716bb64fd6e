"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/live/[uuid]",{

/***/ "./src/pages/live/[uuid].tsx":
/*!***********************************!*\
  !*** ./src/pages/live/[uuid].tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PublicLiveConversationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/TimelineItem */ \"./src/components/TimelineItem.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Icon Components\nconst CustomerIcon = (param)=>{\n    let { name } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-orange-400/30\",\n        children: name.charAt(0).toUpperCase()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n};\n_c = CustomerIcon;\nfunction PublicLiveConversationPage() {\n    var _timeline_timeline, _conversation_store;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { uuid } = router.query;\n    const [page] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isCustomerNameSet, setIsCustomerNameSet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const limit = 50;\n    const conversationUuid = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        // Wait for router to be ready and uuid to be available\n        if (router.isReady && uuid) {\n            return uuid;\n        }\n        return null;\n    }, [\n        router.isReady,\n        uuid\n    ]);\n    // Fetch conversation data\n    const fetchConversation = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getByUuid(conversationUuid);\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setConversation(result.data || result);\n            } else {\n                setConversation(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching conversation:\", err);\n            setError(\"Failed to load conversation\");\n        }\n    };\n    // Fetch timeline data\n    const fetchTimeline = async ()=>{\n        if (!conversationUuid || !router.isReady) return;\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.getUnifiedTimelineByUuid(conversationUuid, {\n                page,\n                limit\n            });\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setTimeline(result.data || result);\n            } else {\n                setTimeline(result);\n            }\n        } catch (err) {\n            console.error(\"Error fetching timeline:\", err);\n            setError(\"Failed to load timeline\");\n        }\n    };\n    // Fetch data when dependencies change\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (conversationUuid && router.isReady) {\n            fetchConversation();\n            fetchTimeline();\n        }\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    // Set up polling for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!conversationUuid || !router.isReady) return;\n        const interval = setInterval(()=>{\n            fetchConversation();\n            fetchTimeline();\n        }, 2000); // Reduced from 5000ms to 2000ms for better responsiveness\n        return ()=>clearInterval(interval);\n    }, [\n        conversationUuid,\n        router.isReady\n    ]);\n    const appendMessage = async (messageData)=>{\n        if (!conversationUuid) return;\n        try {\n            var _messagesEndRef_current;\n            console.log(\"\\uD83D\\uDD27 Frontend: Sending message:\", messageData);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.conversationApi.appendMessageByUuid(conversationUuid, messageData);\n            console.log(\"✅ Frontend: Message sent successfully:\", response);\n            setNewMessage(\"\");\n            // Refresh data\n            fetchConversation();\n            fetchTimeline();\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        } catch (err) {\n            console.error(\"❌ Frontend: Failed to send message:\", err);\n            setError(\"Failed to send message\");\n        }\n    };\n    // Use a ref to track previous message count to prevent unnecessary effects\n    const prevMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        var _timeline_timeline;\n        const currentMessageCount = (timeline === null || timeline === void 0 ? void 0 : (_timeline_timeline = timeline.timeline) === null || _timeline_timeline === void 0 ? void 0 : _timeline_timeline.length) || 0;\n        if (currentMessageCount > prevMessageCountRef.current) {\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n            prevMessageCountRef.current = currentMessageCount;\n        }\n    }, [\n        timeline === null || timeline === void 0 ? void 0 : (_timeline_timeline = timeline.timeline) === null || _timeline_timeline === void 0 ? void 0 : _timeline_timeline.length\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage(e);\n        }\n    };\n    const handleSendMessage = (e)=>{\n        e.preventDefault();\n        if (!newMessage.trim() || !conversation) return;\n        // Additional validation\n        if (!conversation.uuid) {\n            console.error(\"Conversation UUID is missing\");\n            return;\n        }\n        // Require customer name\n        if (!customerName.trim()) {\n            setError(\"Please enter your name to send a message\");\n            return;\n        }\n        try {\n            // Create customer message\n            const messageData = {\n                content: \"[\".concat(customerName.trim(), \"]: \").concat(newMessage.trim()),\n                createdBy: \"1\",\n                agentId: \"customer-message\"\n            };\n            appendMessage(messageData);\n        } catch (error) {\n            console.error(\"Failed to prepare message:\", error);\n        }\n    };\n    // Customer name setup modal\n    if (!isCustomerNameSet && conversation) {\n        var _conversation_store1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-slate-950 relative overflow-hidden flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"Join Live Chat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Join the live customer conversation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0\",\n                            style: {\n                                backgroundImage: \"\\n              radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\\n            \"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 max-w-md mx-auto px-6 py-8 bg-slate-900/50 border border-cyan-500/20 rounded-xl backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400 mb-2\",\n                                    children: \"Join Live Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-cyan-300/80\",\n                                    children: [\n                                        (conversation === null || conversation === void 0 ? void 0 : (_conversation_store1 = conversation.store) === null || _conversation_store1 === void 0 ? void 0 : _conversation_store1.name) || \"Store\",\n                                        \" - Live Conversation\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: (e)=>{\n                                e.preventDefault();\n                                if (customerName.trim()) {\n                                    setIsCustomerNameSet(true);\n                                }\n                            },\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"customerName\",\n                                            className: \"block text-sm font-medium text-cyan-300 mb-2\",\n                                            children: \"Your Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"customerName\",\n                                            value: customerName,\n                                            onChange: (e)=>setCustomerName(e.target.value),\n                                            placeholder: \"Enter your name\",\n                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 text-cyan-100 placeholder-cyan-400/50\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !customerName.trim(),\n                                    className: \"w-full px-6 py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95\",\n                                    children: \"Join Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            fontFamily: 'system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif'\n        },\n        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"min-h-screen bg-slate-950 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        className: \"jsx-c1d451e38a0b1618\",\n                        children: (conversation === null || conversation === void 0 ? void 0 : conversation.title) || \"Live Chat\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Live customer conversation\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-c1d451e38a0b1618\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 opacity-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n            radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\\n            radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\\n            radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)\\n          \"\n                        },\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n            linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%),\\n            linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%)\\n          \",\n                            backgroundSize: \"50px 50px\"\n                        },\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 opacity-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"relative z-10 max-w-4xl mx-auto px-4 py-4 md:py-6 h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"mb-4 md:mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontFamily: \"Orbitron, monospace\"\n                                },\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400\",\n                                children: (conversation === null || conversation === void 0 ? void 0 : (_conversation_store = conversation.store) === null || _conversation_store === void 0 ? void 0 : _conversation_store.name) || \"LIVE CHAT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontFamily: \"Exo 2, sans-serif\"\n                                },\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-sm md:text-base text-cyan-300/80 mt-1\",\n                                children: [\n                                    \"Live Chat - \",\n                                    customerName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"bg-red-500/10 border border-red-500/30 text-red-300 px-4 py-3 rounded-lg backdrop-blur-sm mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-5 h-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\",\n                                        className: \"jsx-c1d451e38a0b1618\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"relative w-16 h-16 mx-auto mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 border-4 border-cyan-400/20 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute inset-0 border-4 border-transparent border-t-cyan-400 rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontFamily: \"Exo 2, sans-serif\"\n                                    },\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-cyan-300/80\",\n                                    children: \"Initializing Neural Interface...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    conversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 flex flex-col bg-slate-900/30 border border-cyan-500/20 rounded-xl backdrop-blur-sm overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 overflow-y-auto p-3 md:p-4 space-y-3 md:space-y-4 scrollbar-thin scrollbar-thumb-cyan-500/30 scrollbar-track-transparent\",\n                                children: [\n                                    ((timeline === null || timeline === void 0 ? void 0 : timeline.timeline) || []).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TimelineItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            item: item\n                                        }, \"\".concat(item.type, \"-\").concat(item.id), false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)),\n                                    (conversation === null || conversation === void 0 ? void 0 : conversation.notificationStatus) && conversation.notificationStatus !== \"None\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-3 md:gap-4 justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerIcon, {\n                                                    name: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"max-w-[85%] md:max-w-[70%]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontFamily: \"Exo 2, sans-serif\"\n                                                                },\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"text-xs opacity-80\",\n                                                                children: conversation.notificationStatus === \"AgentIsThinking\" ? \"AI Agent is thinking...\" : \"AI Agent is generating response...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"inline-flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/80 rounded-full animate-bounce\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/60 rounded-full animate-bounce [animation-delay:120ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-1.5 h-1.5 bg-emerald-300/40 rounded-full animate-bounce [animation-delay:240ms]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-c1d451e38a0b1618\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSendMessage,\n                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"border-t border-cyan-500/20 p-3 md:p-4 bg-slate-900/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex gap-2 md:gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Enter your message...\",\n                                                    style: {\n                                                        fontFamily: \"Exo 2, sans-serif\"\n                                                    },\n                                                    rows: 1,\n                                                    disabled: isLoading,\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-full px-3 md:px-4 py-2 md:py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 resize-none text-cyan-100 placeholder-cyan-400/50 text-sm md:text-base transition-all duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-c1d451e38a0b1618\" + \" \" + \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-4 h-4 border-2 border-cyan-400/30 border-t-cyan-400 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: !newMessage.trim() || isLoading || !customerName.trim(),\n                                            style: {\n                                                fontFamily: \"Exo 2, sans-serif\"\n                                            },\n                                            className: \"jsx-c1d451e38a0b1618\" + \" \" + \"px-4 md:px-6 py-2 md:py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-cyan-500/25 text-sm md:text-base\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-c1d451e38a0b1618\" + \" \" + \"w-4 h-4 md:w-5 md:h-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\",\n                                                    className: \"jsx-c1d451e38a0b1618\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this) : null\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"c1d451e38a0b1618\",\n                children: \".scrollbar-thin{scrollbar-width:thin}.scrollbar-thumb-cyan-500\\\\\\\\/30::-webkit-scrollbar-thumb {background-color:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}.scrollbar-track-transparent::-webkit-scrollbar-track{background-color:transparent}::-webkit-scrollbar{width:6px}::-webkit-scrollbar-track{background:transparent}::-webkit-scrollbar-thumb{background:rgba(6,182,212,.3);-webkit-border-radius:9999px;-moz-border-radius:9999px;border-radius:9999px}::-webkit-scrollbar-thumb:hover{background:rgba(6,182,212,.5)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\live\\\\[uuid].tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicLiveConversationPage, \"bsIEZR9K7LoW29+ic6ycO1/HOKk=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c1 = PublicLiveConversationPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"CustomerIcon\");\n$RefreshReg$(_c1, \"PublicLiveConversationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/live/[uuid].tsx\n"));

/***/ })

});