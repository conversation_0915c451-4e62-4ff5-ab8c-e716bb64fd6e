import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
export class DatabaseService {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Get the main application DataSource
   * This replaces the separate DataSource in agents/database
   */
  getDataSource(): DataSource {
    return this.dataSource;
  }

  /**
   * Check if the database connection is healthy
   */
  isConnected(): boolean {
    return this.dataSource.isInitialized;
  }
}
