/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/auth/callback"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cauth%5Ccallback.tsx&page=%2Fauth%2Fcallback!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cauth%5Ccallback.tsx&page=%2Fauth%2Fcallback! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/auth/callback\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/auth/callback.tsx */ \"./src/pages/auth/callback.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/auth/callback\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNtYWhsbCU1Q0RvY3VtZW50cyU1Q3dvcmtzcGFjZSU1Q3Byb2plY3RzJTVDdGVuby1zdG9yZSU1Q2Zyb250ZW5kJTVDc3JjJTVDcGFnZXMlNUNhdXRoJTVDY2FsbGJhY2sudHN4JnBhZ2U9JTJGYXV0aCUyRmNhbGxiYWNrISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLG9FQUErQjtBQUN0RDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/YjFlNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2F1dGgvY2FsbGJhY2tcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9hdXRoL2NhbGxiYWNrLnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvYXV0aC9jYWxsYmFja1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cauth%5Ccallback.tsx&page=%2Fauth%2Fcallback!\n"));

/***/ }),

/***/ "./src/pages/auth/callback.tsx":
/*!*************************************!*\
  !*** ./src/pages/auth/callback.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AuthCallback; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction AuthCallback() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { refresh } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const processCallback = async ()=>{\n            try {\n                const { token, error: authError } = router.query;\n                if (authError) {\n                    setError(\"Authentication failed: \".concat(authError));\n                    setIsProcessing(false);\n                    return;\n                }\n                if (!token || typeof token !== \"string\") {\n                    setError(\"No authentication token received\");\n                    setIsProcessing(false);\n                    return;\n                }\n                console.log(\"[AuthCallback] Received token:\", token.substring(0, 20) + \"...\");\n                // Store the token in a secure cookie (remove secure flag for localhost development)\n                const isLocalhost = window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\";\n                const cookieOptions = isLocalhost ? \"path=/; max-age=86400; samesite=lax\" : \"path=/; max-age=86400; secure; samesite=strict\";\n                document.cookie = \"access_token_client=\".concat(encodeURIComponent(token), \"; \").concat(cookieOptions);\n                console.log(\"[AuthCallback] Token stored in cookie successfully\");\n                // Also store in localStorage as backup\n                try {\n                    localStorage.setItem(\"teno:auth:token\", token);\n                    console.log(\"[AuthCallback] Token stored in localStorage successfully\");\n                } catch (e) {\n                    console.warn(\"Could not store token in localStorage:\", e);\n                }\n                // Refresh auth context to get user data\n                await refresh();\n                // Redirect to the next page or dashboard\n                const nextParam = router.query.next;\n                const target = nextParam && nextParam.startsWith(\"/\") ? nextParam : \"/dashboard\";\n                console.log(\"[AuthCallback] Redirecting to:\", target);\n                router.replace(target);\n            } catch (err) {\n                console.error(\"Error processing auth callback:\", err);\n                setError(\"Failed to complete authentication\");\n                setIsProcessing(false);\n            }\n        };\n        if (router.isReady) {\n            processCallback();\n        }\n    }, [\n        router.isReady,\n        router.query,\n        router,\n        refresh\n    ]);\n    if (isProcessing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n                                children: \"Completing Authentication\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-gray-600\",\n                                children: \"Please wait while we complete your login...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n                            children: \"Authentication Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/login\"),\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\auth\\\\callback.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_s(AuthCallback, \"2FBTzXI1eFCalOKMS8CI0GFoQhc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AuthCallback;\nvar _c;\n$RefreshReg$(_c, \"AuthCallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/auth/callback.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cauth%5Ccallback.tsx&page=%2Fauth%2Fcallback!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);